<?php
// 添加线下扫码支付菜单到后台管理系统

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    echo "<h2>🔧 添加线下扫码支付菜单</h2>";
    
    // 1. 查找支付管理主菜单
    echo "<h3>1. 查找支付管理主菜单</h3>";
    $paymentMenu = DB::table('strongadmin_menu')
                    ->where('name', 'LIKE', '%支付%')
                    ->where('level', 1)
                    ->first();
    
    if (!$paymentMenu) {
        echo "<div style='color: orange;'>⚠️ 未找到支付管理主菜单，查找其他可能的菜单...</div>";
        
        // 查找所有一级菜单
        $allMenus = DB::table('strongadmin_menu')
                     ->where('level', 1)
                     ->orderBy('sort')
                     ->get(['id', 'name', 'route_url']);
        
        echo "<div>现有一级菜单：</div>";
        foreach ($allMenus as $menu) {
            echo "<div>- ID: {$menu->id}, 名称: {$menu->name}, 路由: {$menu->route_url}</div>";
        }
        
        // 创建支付管理主菜单
        echo "<h4>创建支付管理主菜单</h4>";
        $parentId = DB::table('strongadmin_menu')->insertGetId([
            'level' => 1,
            'parent_id' => 0,
            'name' => '支付管理',
            'route_url' => '',
            'status' => 1,
            'sort' => 50,
            'delete_allow' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "<div style='color: green;'>✅ 创建支付管理主菜单，ID: {$parentId}</div>";
        $paymentMenu = (object)['id' => $parentId];
    } else {
        echo "<div style='color: green;'>✅ 找到支付管理主菜单，ID: {$paymentMenu->id}, 名称: {$paymentMenu->name}</div>";
    }
    
    // 2. 检查是否已存在线下扫码支付菜单
    echo "<h3>2. 检查线下扫码支付菜单</h3>";
    $existingMenu = DB::table('strongadmin_menu')
                     ->where('name', '线下扫码支付')
                     ->where('parent_id', $paymentMenu->id)
                     ->first();
    
    if ($existingMenu) {
        echo "<div style='color: orange;'>⚠️ 线下扫码支付菜单已存在，ID: {$existingMenu->id}</div>";
        echo "<div>更新菜单路由...</div>";
        
        DB::table('strongadmin_menu')
          ->where('id', $existingMenu->id)
          ->update([
              'route_url' => 'offline-qr-payment',
              'updated_at' => now()
          ]);
        
        echo "<div style='color: green;'>✅ 更新线下扫码支付菜单路由</div>";
    } else {
        // 创建线下扫码支付菜单
        echo "<h4>创建线下扫码支付菜单</h4>";
        $menuId = DB::table('strongadmin_menu')->insertGetId([
            'level' => 2,
            'parent_id' => $paymentMenu->id,
            'name' => '线下扫码支付',
            'route_url' => 'offline-qr-payment',
            'status' => 1,
            'sort' => 10,
            'delete_allow' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "<div style='color: green;'>✅ 创建线下扫码支付菜单，ID: {$menuId}</div>";
    }
    
    // 3. 查看最终结果
    echo "<h3>3. 验证菜单结构</h3>";
    $menus = DB::table('strongadmin_menu')
              ->where('parent_id', $paymentMenu->id)
              ->orderBy('sort')
              ->get(['id', 'name', 'route_url', 'status']);
    
    echo "<div>支付管理子菜单：</div>";
    foreach ($menus as $menu) {
        $status = $menu->status == 1 ? '启用' : '禁用';
        echo "<div>- ID: {$menu->id}, 名称: {$menu->name}, 路由: {$menu->route_url}, 状态: {$status}</div>";
    }
    
    echo "<h2 style='color: green;'>🎉 菜单添加完成！</h2>";
    echo "<p>现在您可以在后台管理系统中看到：</p>";
    echo "<ul>";
    echo "<li><strong>支付管理</strong> → <strong>线下扫码支付</strong></li>";
    echo "<li>访问地址：<code>/strongadmin/offline-qr-payment</code></li>";
    echo "</ul>";
    
    echo "<p><a href='/strongadmin' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>返回后台测试</a></p>";
    
} catch (\Exception $e) {
    echo "<div style='color: red;'>❌ 错误: " . $e->getMessage() . "</div>";
    echo "<div>错误详情: " . $e->getTraceAsString() . "</div>";
}
?>
