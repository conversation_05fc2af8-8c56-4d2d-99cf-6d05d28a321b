<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\PaymentOption;

class AddOfflinePayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment:add-offline';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '添加线下扫码支付方式';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始添加线下扫码支付方式...');

        // 线下扫码支付方式数据
        $offlinePayments = [
            [
                'name' => '支付宝扫码支付',
                'code' => 'offline_alipay',
                'description' => '线下支付宝扫码支付，扫码后请在备注中填写订单号',
                'config' => [
                    'type' => 'offline_qr',
                    'payment_name' => '支付宝扫码支付',
                    'icon' => 'fab fa-alipay',
                    'color' => '#1677FF',
                    'qr_code_path' => '/images/qr/alipay_qr.png',
                    'account_info' => [
                        '收款账户' => '<EMAIL>',
                        '收款人' => '全速达物流'
                    ],
                    'instructions' => [
                        '打开支付宝APP，扫描二维码',
                        '在转账备注中填写：订单号 + 您的联系方式',
                        '完成支付后，请联系客服确认到账',
                        '客服会在1-2小时内处理您的订单'
                    ],
                    'customer_service' => [
                        'qq' => '*********',
                        'wechat' => 'Jagship',
                        'phone' => '***********',
                        'email' => '<EMAIL>'
                    ]
                ],
                'status' => 0,
                'sort_order' => 100
            ],
            [
                'name' => '微信扫码支付',
                'code' => 'offline_wechat',
                'description' => '线下微信扫码支付，扫码后请在备注中填写订单号',
                'config' => [
                    'type' => 'offline_qr',
                    'payment_name' => '微信扫码支付',
                    'icon' => 'fab fa-weixin',
                    'color' => '#07C160',
                    'qr_code_path' => '/images/qr/wechat_qr.png',
                    'account_info' => [
                        '收款账户' => 'Jagship',
                        '收款人' => '全速达物流'
                    ],
                    'instructions' => [
                        '打开微信APP，扫描二维码',
                        '在转账备注中填写：订单号 + 您的联系方式',
                        '完成支付后，请联系客服确认到账',
                        '客服会在1-2小时内处理您的订单'
                    ],
                    'customer_service' => [
                        'qq' => '*********',
                        'wechat' => 'Jagship',
                        'phone' => '***********',
                        'email' => '<EMAIL>'
                    ]
                ],
                'status' => 0,
                'sort_order' => 101
            ],
            [
                'name' => 'QQ钱包支付',
                'code' => 'offline_qq',
                'description' => '线下QQ钱包扫码支付，扫码后请在备注中填写订单号',
                'config' => [
                    'type' => 'offline_qr',
                    'payment_name' => 'QQ钱包支付',
                    'icon' => 'fab fa-qq',
                    'color' => '#12B7F5',
                    'qr_code_path' => '/images/qr/qq_qr.png',
                    'account_info' => [
                        'QQ号码' => '*********',
                        '收款人' => '全速达物流'
                    ],
                    'instructions' => [
                        '打开QQ或QQ钱包，扫描二维码',
                        '在转账备注中填写：订单号 + 您的联系方式',
                        '完成支付后，请联系客服确认到账',
                        '客服会在1-2小时内处理您的订单'
                    ],
                    'customer_service' => [
                        'qq' => '*********',
                        'wechat' => 'Jagship',
                        'phone' => '***********',
                        'email' => '<EMAIL>'
                    ]
                ],
                'status' => 0,
                'sort_order' => 102
            ],
            [
                'name' => '京东支付',
                'code' => 'offline_jd',
                'description' => '线下京东支付扫码支付，扫码后请在备注中填写订单号',
                'config' => [
                    'type' => 'offline_qr',
                    'payment_name' => '京东支付',
                    'icon' => 'fas fa-shopping-cart',
                    'color' => '#E1251B',
                    'qr_code_path' => '/images/qr/jd_qr.png',
                    'account_info' => [
                        '收款账户' => '<EMAIL>',
                        '收款人' => '全速达物流'
                    ],
                    'instructions' => [
                        '打开京东APP，扫描二维码',
                        '在转账备注中填写：订单号 + 您的联系方式',
                        '完成支付后，请联系客服确认到账',
                        '客服会在1-2小时内处理您的订单'
                    ],
                    'customer_service' => [
                        'qq' => '*********',
                        'wechat' => 'Jagship',
                        'phone' => '***********',
                        'email' => '<EMAIL>'
                    ]
                ],
                'status' => 0,
                'sort_order' => 103
            ],
            [
                'name' => '百度钱包',
                'code' => 'offline_baidu',
                'description' => '线下百度钱包扫码支付，扫码后请在备注中填写订单号',
                'config' => [
                    'type' => 'offline_qr',
                    'payment_name' => '百度钱包',
                    'icon' => 'fas fa-wallet',
                    'color' => '#2932E1',
                    'qr_code_path' => '/images/qr/baidu_qr.png',
                    'account_info' => [
                        '收款账户' => '<EMAIL>',
                        '收款人' => '全速达物流'
                    ],
                    'instructions' => [
                        '打开百度APP，扫描二维码',
                        '在转账备注中填写：订单号 + 您的联系方式',
                        '完成支付后，请联系客服确认到账',
                        '客服会在1-2小时内处理您的订单'
                    ],
                    'customer_service' => [
                        'qq' => '*********',
                        'wechat' => 'Jagship',
                        'phone' => '***********',
                        'email' => '<EMAIL>'
                    ]
                ],
                'status' => 0,
                'sort_order' => 104
            ]
        ];

        try {
            // 插入或更新支付方式
            foreach ($offlinePayments as $paymentData) {
                $existing = PaymentOption::where('code', $paymentData['code'])->first();
                
                if ($existing) {
                    // 更新现有记录
                    $existing->config = $paymentData['config'];
                    $existing->save();
                    $this->info("✅ 更新支付方式: {$paymentData['name']}");
                } else {
                    // 插入新记录
                    PaymentOption::create($paymentData);
                    $this->info("✅ 添加支付方式: {$paymentData['name']}");
                }
            }

            // 查看结果
            $results = PaymentOption::where('code', 'like', 'offline_%')
                ->orderBy('sort_order')
                ->get(['id', 'name', 'code', 'status', 'sort_order']);

            $this->info("\n📋 线下扫码支付方式列表:");
            $this->table(
                ['ID', '名称', '代码', '状态', '排序'],
                $results->map(function ($item) {
                    return [
                        $item->id,
                        $item->name,
                        $item->code,
                        $item->status ? '启用' : '禁用',
                        $item->sort_order
                    ];
                })
            );

            $this->info("\n🎉 线下扫码支付方式添加完成！");
            $this->warn("⚠️  请到后台支付管理中启用这些支付方式。");

        } catch (\Exception $e) {
            $this->error("❌ 错误: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
