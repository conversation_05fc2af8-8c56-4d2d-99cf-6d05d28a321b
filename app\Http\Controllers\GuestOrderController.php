<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Order\Order;

class GuestOrderController extends Controller
{
    /**
     * 显示游客订单查询页面
     */
    public function index(Request $request)
    {
        // 如果有自动查询参数，直接执行查询
        if ($request->has('auto') && $request->has('order_no') && $request->has('email')) {
            return $this->lookup($request);
        }

        return view('guest.order-lookup');
    }
    
    /**
     * 查询游客订单
     */
    public function lookup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_no' => 'required|string',
            'email' => 'required|email',
        ], [
            'order_no.required' => '请输入订单号',
            'email.required' => '请输入邮箱地址',
            'email.email' => '请输入有效的邮箱地址',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'code' => 4001,
                    'message' => $validator->errors()->first(),
                    'data' => $validator->errors()
                ]);
            }
            return back()->withErrors($validator)->withInput();
        }

        // 查找订单
        $order = Order::where('order_no', $request->order_no)
                     ->where('email', $request->email)
                     ->with([
                         'orderProducts:product_id,order_id,sku,title,sale_price,qty,img_cover,specs',
                         'shippingOption:id,title,code,desc',
                         'paymentOption:id,name,code,description'
                     ])
                     ->first();

        if (!$order) {
            $message = '未找到匹配的订单，请检查订单号和邮箱地址是否正确';
            if ($request->expectsJson()) {
                return response()->json([
                    'code' => 4004,
                    'message' => $message
                ]);
            }
            return back()->withErrors(['order_no' => $message])->withInput();
        }

        // 隐藏敏感信息
        $order->makeHidden(['http_referer', 'source', 'deleted_at']);

        if ($request->expectsJson()) {
            return response()->json([
                'code' => 200,
                'message' => '查询成功',
                'data' => $order
            ]);
        }

        return view('guest.order-detail', ['order' => $order]);
    }
    
    /**
     * 游客订单跟踪
     */
    public function tracking(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_no' => 'required|string',
        ], [
            'order_no.required' => '请输入订单号',
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'code' => 4001,
                    'message' => $validator->errors()->first()
                ]);
            }
            return back()->withErrors($validator)->withInput();
        }

        $order = Order::where('order_no', $request->order_no)->first();

        if (!$order) {
            $message = '未找到订单信息';
            if ($request->expectsJson()) {
                return response()->json([
                    'code' => 4004,
                    'message' => $message
                ]);
            }
            return back()->withErrors(['order_no' => $message])->withInput();
        }

        if ($request->expectsJson()) {
            return response()->json([
                'code' => 200,
                'message' => '查询成功',
                'data' => [
                    'order_no' => $order->order_no,
                    'order_status' => $order->order_status,
                    'order_status_text' => $order->order_status_text,
                    'created_at' => $order->created_at,
                    'tracking_number' => $order->tracking_number ?? '',
                ]
            ]);
        }

        return view('guest.order-tracking', ['order' => $order]);
    }
}
