<?php

/**
 * StrongShop
 * <AUTHOR> <<EMAIL>>
 * @license http://www.strongshop.cn/license/
 * @copyright StrongShop Software
 */

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PaymentOption;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class IndexController extends Controller
{

    public function index(Request $request)
    {
        $orderId = $request->orderId;
        $paycode = $request->paycode;

        // 检查是否是AJAX请求
        $isAjax = $request->expectsJson() || $request->ajax();

        // 检查支付方式是否存在且启用
        $payment = PaymentOption::where('code', $paycode)
            ->where('status', 1)
            ->first();

        // 根据支付方式进行真实的支付处理
        try {
            switch ($paycode) {
                case 'paypal':
                case 'paypal_legacy':
                case 'paypal_new':
                    return $this->handlePayPalPayment($orderId, $paycode);

                case 'stripe':
                    return $this->handleStripePayment($orderId);

                case 'alipay':
                    return $this->handleAlipayPayment($orderId);

                case 'wechat_pay':
                    return $this->handleWechatPayment($orderId);

                case 'bank_transfer':
                    return $this->handleBankTransferPayment($orderId);

                case 'offline_qr_payment':
                    return $this->handleOfflineQrPayment($orderId, $paycode, $isAjax);

                case 'offline_alipay':
                case 'offline_wechat':
                case 'offline_qq':
                case 'offline_jd':
                case 'offline_baidu':
                    return $this->handleOfflineQrPayment($orderId, $paycode, $isAjax);

                default:
                    // 未知支付方式，显示错误
                    return view('payment.error', [
                        'orderId' => $orderId,
                        'paycode' => $paycode,
                        'message' => 'Unsupported payment method: ' . $paycode
                    ]);
            }
        } catch (\Exception $e) {
            \Log::error('Payment processing failed: ' . $e->getMessage());
            return view('payment.error', [
                'orderId' => $orderId,
                'paycode' => $paycode,
                'message' => 'Payment processing failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 处理PayPal支付 - 根据配置选择新旧版本
     */
    private function handlePayPalPayment($orderId, $paycode = 'paypal')
    {
        // 根据paycode确定PayPal类型
        if ($paycode === 'paypal_new') {
            $paypalType = 'new';
        } elseif ($paycode === 'paypal_legacy') {
            $paypalType = 'legacy';
        } else {
            // 默认情况，检查系统配置
            $paypalType = $this->getCurrentPayPalType();
        }

        if ($paypalType === 'new') {
            // 使用新PayPal对接
            $url = $this->generateNewPayPalUrl($orderId);
        } else {
            // 使用原有PayPal对接（legacy）
            $url = $this->generateLegacyPayPalUrl($orderId);
        }

        // 重定向到PayPal支付页面
        return redirect($url);
    }

    /**
     * 获取当前PayPal类型配置
     */
    private function getCurrentPayPalType()
    {
        try {
            $config = DB::table('web_configs')
                ->where('config_key', 'paypal_type')
                ->value('config_value');

            return $config ?: 'legacy'; // 默认使用原有PayPal
        } catch (\Exception $e) {
            return 'legacy';
        }
    }

    /**
     * 生成新PayPal支付URL
     */
    private function generateNewPayPalUrl($orderId)
    {
        // 检查新PayPal路由是否存在
        if (Route::has('paypal_new.pay')) {
            return route('paypal_new.pay', ['orderId' => $orderId]);
        }

        // 如果没有新PayPal路由，使用国际支付路由
        if (Route::has('international.paypal.pay')) {
            return route('international.paypal.pay', ['orderId' => $orderId]);
        }

        // 都没有则回退到原有PayPal
        return $this->generateLegacyPayPalUrl($orderId);
    }

    /**
     * 生成原有PayPal支付URL
     */
    private function generateLegacyPayPalUrl($orderId)
    {
        if (!Route::has('paypal.pay')) {
            throw new \Exception("原有PayPal路由 'paypal.pay' 不存在");
        }

        return route('paypal.pay', ['orderId' => $orderId]);
    }

    /**
     * 生成其他支付方式URL
     */
    private function generatePaymentUrl($paycode, $orderId)
    {
        $routeName = "{$paycode}.pay";

        if (!Route::has($routeName)) {
            throw new \Exception("支付路由 '{$routeName}' 不存在");
        }

        return route($routeName, ['orderId' => $orderId]);
    }

    /**
     * 处理Stripe支付
     */
    private function handleStripePayment($orderId)
    {
        if (Route::has('stripe.pay')) {
            return redirect(route('stripe.pay', ['orderId' => $orderId]));
        }

        // 如果没有Stripe路由，显示临时支付页面
        return view('payment.stripe', [
            'orderId' => $orderId,
            'message' => 'Stripe payment is not configured yet.'
        ]);
    }

    /**
     * 处理支付宝支付
     */
    private function handleAlipayPayment($orderId)
    {
        if (Route::has('alipay.pay')) {
            return redirect(route('alipay.pay', ['orderId' => $orderId]));
        }

        return view('payment.alipay', [
            'orderId' => $orderId,
            'message' => 'Alipay payment is not configured yet.'
        ]);
    }

    /**
     * 处理微信支付
     */
    private function handleWechatPayment($orderId)
    {
        if (Route::has('wechat.pay')) {
            return redirect(route('wechat.pay', ['orderId' => $orderId]));
        }

        return view('payment.wechat', [
            'orderId' => $orderId,
            'message' => 'WeChat Pay is not configured yet.'
        ]);
    }

    /**
     * 处理银行转账
     */
    private function handleBankTransferPayment($orderId)
    {
        try {
            // 获取订单信息
            $order = \App\Models\Admin\Order\Order::find($orderId);
            if (!$order) {
                return view('payment.error', [
                    'orderId' => $orderId,
                    'paycode' => 'bank_transfer',
                    'message' => '订单不存在'
                ]);
            }

            // 获取银行转账配置
            $bankTransferConfig = \DB::select("SELECT * FROM st_payment_options WHERE code = 'bank_transfer' AND status = 1 LIMIT 1");

            $bankInfo = [];
            if (!empty($bankTransferConfig)) {
                $config = json_decode($bankTransferConfig[0]->config ?? '{}', true);
                $bankInfo = [
                    'bank_name' => $config['bank_name'] ?? '',
                    'account_name' => $config['account_name'] ?? '',
                    'account_number' => $config['account_number'] ?? '',
                    'bank_address' => $config['bank_address'] ?? '',
                    'swift_code' => $config['swift_code'] ?? '',
                    'supported_currencies' => $config['supported_currencies'] ?? '',
                    'transfer_instructions' => $config['transfer_instructions'] ?? '',
                    'contact_info' => $config['contact_info'] ?? '',
                    // 新增的联系方式字段
                    'contact_email' => $config['contact_email'] ?? '',
                    'contact_wechat' => $config['contact_wechat'] ?? '',
                    'contact_phone' => $config['contact_phone'] ?? '',
                    'service_hours' => $config['service_hours'] ?? '',
                    'contact_qq' => $config['contact_qq'] ?? '',
                    'wechat_qrcode' => $config['wechat_qrcode'] ?? ''
                ];
            }

            return view('payment.bank_transfer', [
                'orderId' => $orderId,
                'order' => $order,
                'bankInfo' => $bankInfo
            ]);
        } catch (\Exception $e) {
            return view('payment.error', [
                'orderId' => $orderId,
                'paycode' => 'bank_transfer',
                'message' => '获取银行信息失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 处理线下扫码支付
     */
    private function handleOfflineQrPayment($orderId, $paycode, $isAjax = false)
    {
        try {
            // 如果是新的统一线下扫码支付
            if ($paycode === 'offline_qr_payment') {
                // 获取订单信息
                $order = \App\Models\Admin\Order\Order::find($orderId);
                if (!$order) {
                    if ($isAjax) {
                        return response()->json([
                            'success' => false,
                            'message' => '订单不存在'
                        ]);
                    }
                    return redirect()->route('home')->with('error', '订单不存在');
                }

                // 获取所有启用的线下扫码支付方式
                $qrPayments = \App\Models\OfflineQrPayment::where('status', 1)
                                                         ->orderBy('sort_order', 'asc')
                                                         ->orderBy('id', 'asc')
                                                         ->get();

                if ($qrPayments->isEmpty()) {
                    if ($isAjax) {
                        return response()->json([
                            'success' => false,
                            'message' => '暂无可用的扫码支付方式'
                        ]);
                    }
                    return redirect()->route('home')->with('error', '暂无可用的扫码支付方式');
                }

                if ($isAjax) {
                    return response()->json([
                        'success' => true,
                        'type' => 'offline_qr_modal',
                        'data' => [
                            'order' => $order,
                            'qrPayments' => $qrPayments
                        ]
                    ]);
                } else {
                    // 非AJAX请求，跳转到独立页面
                    return redirect()->route('payment.offline_qr', [
                        'order_id' => $orderId
                    ]);
                }
            }

            // 兼容旧的线下扫码支付代码
            $order = \App\Models\Admin\Order\Order::find($orderId);
            if (!$order) {
                return view('payment.error', [
                    'orderId' => $orderId,
                    'paycode' => $paycode,
                    'message' => '订单不存在'
                ]);
            }

            // 获取支付方式配置
            $paymentOption = \App\Models\PaymentOption::where('code', $paycode)->where('status', 1)->first();
            if (!$paymentOption) {
                return view('payment.error', [
                    'orderId' => $orderId,
                    'paycode' => $paycode,
                    'message' => '支付方式未启用或不存在'
                ]);
            }

            $config = $paymentOption->config;
            if (is_string($config)) {
                $config = json_decode($config, true);
            }

            return view('payment.offline_qr', [
                'order' => $order,
                'paycode' => $paycode,
                'paymentOption' => $paymentOption,
                'config' => $config
            ]);

        } catch (\Exception $e) {
            return view('payment.error', [
                'orderId' => $orderId,
                'paycode' => $paycode,
                'message' => '线下扫码支付配置错误: ' . $e->getMessage()
            ]);
        }
    }

}
