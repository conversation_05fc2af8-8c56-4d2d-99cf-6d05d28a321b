<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order\Order;

class OfflinePaymentController extends Controller
{
    /**
     * 显示线下支付页面
     */
    public function show(Request $request, $orderId)
    {
        // 获取订单信息
        $order = Order::find($orderId);
        
        if (!$order) {
            return redirect()->route('home')->with('error', '订单不存在');
        }
        
        // 获取支付方式代码
        $paycode = $request->get('paycode', 'offline_alipay');
        
        // 线下支付方式配置
        $offlinePayments = $this->getOfflinePaymentMethods();
        
        // 获取当前支付方式信息
        $currentPayment = $offlinePayments[$paycode] ?? $offlinePayments['offline_alipay'];
        
        return view('payment.offline_payment', [
            'order' => $order,
            'paycode' => $paycode,
            'currentPayment' => $currentPayment,
            'offlinePayments' => $offlinePayments
        ]);
    }
    
    /**
     * 获取线下支付方式配置
     */
    private function getOfflinePaymentMethods()
    {
        return [
            'offline_alipay' => [
                'name' => '支付宝扫码支付',
                'icon' => 'fab fa-alipay',
                'color' => '#1677FF',
                'qr_code' => '/images/qr/alipay_qr.png', // 需要上传实际二维码
                'account_info' => [
                    '收款账户' => '<EMAIL>',
                    '收款人' => '全速达物流'
                ],
                'instructions' => [
                    '打开支付宝APP，扫描上方二维码',
                    '在转账备注中填写：订单号 + 您的联系方式',
                    '完成支付后，请联系客服确认到账',
                    '客服会在1-2小时内处理您的订单'
                ]
            ],
            'offline_wechat' => [
                'name' => '微信扫码支付',
                'icon' => 'fab fa-weixin',
                'color' => '#07C160',
                'qr_code' => '/images/qr/wechat_qr.png',
                'account_info' => [
                    '收款账户' => 'Jagship',
                    '收款人' => '全速达物流'
                ],
                'instructions' => [
                    '打开微信APP，扫描上方二维码',
                    '在转账备注中填写：订单号 + 您的联系方式',
                    '完成支付后，请联系客服确认到账',
                    '客服会在1-2小时内处理您的订单'
                ]
            ],
            'offline_qq' => [
                'name' => 'QQ钱包支付',
                'icon' => 'fab fa-qq',
                'color' => '#12B7F5',
                'qr_code' => '/images/qr/qq_qr.png',
                'account_info' => [
                    'QQ号码' => '*********',
                    '收款人' => '全速达物流'
                ],
                'instructions' => [
                    '打开QQ或QQ钱包，扫描上方二维码',
                    '在转账备注中填写：订单号 + 您的联系方式',
                    '完成支付后，请联系客服确认到账',
                    '客服会在1-2小时内处理您的订单'
                ]
            ],
            'offline_jd' => [
                'name' => '京东支付',
                'icon' => 'fas fa-shopping-cart',
                'color' => '#E1251B',
                'qr_code' => '/images/qr/jd_qr.png',
                'account_info' => [
                    '收款账户' => '<EMAIL>',
                    '收款人' => '全速达物流'
                ],
                'instructions' => [
                    '打开京东APP，扫描上方二维码',
                    '在转账备注中填写：订单号 + 您的联系方式',
                    '完成支付后，请联系客服确认到账',
                    '客服会在1-2小时内处理您的订单'
                ]
            ],
            'offline_baidu' => [
                'name' => '百度钱包',
                'icon' => 'fas fa-wallet',
                'color' => '#2932E1',
                'qr_code' => '/images/qr/baidu_qr.png',
                'account_info' => [
                    '收款账户' => '<EMAIL>',
                    '收款人' => '全速达物流'
                ],
                'instructions' => [
                    '打开百度APP，扫描上方二维码',
                    '在转账备注中填写：订单号 + 您的联系方式',
                    '完成支付后，请联系客服确认到账',
                    '客服会在1-2小时内处理您的订单'
                ]
            ]
        ];
    }
    
    /**
     * 处理支付确认
     */
    public function confirm(Request $request, $orderId)
    {
        $order = Order::find($orderId);
        
        if (!$order) {
            return response()->json(['code' => 4004, 'message' => '订单不存在']);
        }
        
        // 这里可以记录用户确认支付的信息
        // 实际的支付确认需要客服手动处理
        
        return response()->json([
            'code' => 200,
            'message' => '支付信息已提交，请联系客服确认到账',
            'data' => [
                'order_no' => $order->order_no,
                'contact_info' => [
                    'qq' => '*********',
                    'wechat' => 'Jagship',
                    'phone' => '19128765851',
                    'email' => '<EMAIL>'
                ]
            ]
        ]);
    }
}
