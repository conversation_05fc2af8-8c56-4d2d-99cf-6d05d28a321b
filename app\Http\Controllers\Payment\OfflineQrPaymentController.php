<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\OfflineQrPayment;
use App\Models\Order\Order;

class OfflineQrPaymentController extends Controller
{
    /**
     * 显示线下扫码支付页面
     */
    public function show(Request $request)
    {
        $orderId = $request->input('order_id');
        $orderNo = $request->input('order_no');
        
        // 根据订单ID或订单号获取订单
        if ($orderId) {
            $order = Order::find($orderId);
        } elseif ($orderNo) {
            $order = Order::where('order_no', $orderNo)->first();
        } else {
            return redirect()->route('home')->with('error', '订单参数错误');
        }
        
        if (!$order) {
            return redirect()->route('home')->with('error', '订单不存在');
        }
        
        // 获取所有启用的线下扫码支付方式
        $qrPayments = OfflineQrPayment::where('status', 1)
                                     ->orderBy('sort_order', 'asc')
                                     ->orderBy('id', 'asc')
                                     ->get();
        
        if ($qrPayments->isEmpty()) {
            return redirect()->route('home')->with('error', '暂无可用的扫码支付方式');
        }
        
        return view('payment.offline_qr_payment', [
            'order' => $order,
            'qrPayments' => $qrPayments
        ]);
    }
    
    /**
     * 处理支付确认
     */
    public function confirm(Request $request)
    {
        $orderId = $request->input('order_id');
        $qrPaymentId = $request->input('qr_payment_id');
        
        $order = Order::find($orderId);
        if (!$order) {
            return response()->json(['success' => false, 'message' => '订单不存在']);
        }
        
        $qrPayment = OfflineQrPayment::find($qrPaymentId);
        if (!$qrPayment || $qrPayment->status != 1) {
            return response()->json(['success' => false, 'message' => '支付方式不可用']);
        }
        
        // 更新订单状态为待确认
        $order->update([
            'payment_status' => 'pending',
            'payment_method' => $qrPayment->name,
            'payment_notes' => '线下扫码支付，等待客服确认'
        ]);
        
        return response()->json([
            'success' => true, 
            'message' => '支付确认已提交，请联系客服确认到账',
            'redirect' => route('guest.order.lookup', [
                'order_no' => $order->order_no,
                'email' => $order->email
            ])
        ]);
    }
}
