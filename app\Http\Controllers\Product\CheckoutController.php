<?php

/**
 * StrongShop
 * <AUTHOR> <<EMAIL>>
 * @license http://www.strongshop.cn/license/
 * @copyright StrongShop Software
 */

namespace App\Http\Controllers\Product;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\UserAddress;
use App\Repositories\RegionRepository;
use App\Models\Order\Order;
use App\Repositories\CartRepository;
use Illuminate\Support\Facades\DB;
use App\Repositories\OrderRepository;
use App\Repositories\AppRepository;
use App\Models\PaymentOption;
use Illuminate\Validation\Rule;
use App\Repositories\ShippingRepository;
use App\Events\CreatedOrder;
use Illuminate\Contracts\Auth\MustVerifyEmail;

class CheckoutController extends Controller
{

    public function showIndexForm(Request $request)
    {
        // 检查用户是否登录
        $user = $request->user('web');
        $isGuest = !$user;

        // 如果用户已登录，检查邮箱验证
        if (!$isGuest && app('strongshop')->getShopConfig('order_tip_email_verified') && $user instanceof MustVerifyEmail && !$user->hasVerifiedEmail())
        {
            $request->session()->flash('status', __('Your email address is not verified. It is strongly recommended that you verify the email'));
        }

        // 检查是否是立即购买模式
        $buyNow = $request->get('buyNow') == '1' || $request->get('buyNow') === true;

        $cart = CartRepository::getCart($buyNow);
        if (count($cart['rows']) < 1)
        {
            // 如果是立即购买模式但购物车为空，提供更详细的错误信息
            if ($buyNow) {
                abort(201, __('No product selected for immediate purchase. Please select a product first.'));
            } else {
                abort(201, __('Shopping Cart is empty.'));
            }
        }

        //支付方式 - 如果是游客下单，需要检查允许的支付方式
        if ($isGuest) {
            // 获取游客下单允许的支付方式
            try {
                $allowedPaymentMethods = \App\Models\SystemSetting::get('guest_order_payment_methods', ['paypal', 'alipay']);

                // 确保返回的是数组
                if (is_string($allowedPaymentMethods)) {
                    $allowedPaymentMethods = json_decode($allowedPaymentMethods, true) ?: ['paypal', 'alipay'];
                }
                if (!is_array($allowedPaymentMethods)) {
                    $allowedPaymentMethods = ['paypal', 'alipay'];
                }
            } catch (\Exception $e) {
                $allowedPaymentMethods = ['paypal', 'alipay']; // 默认允许的支付方式
            }

            // 获取所有启用的支付方式，不限制特定的支付方式列表
            // 这样确保后台启用的任何支付方式都会在前端显示
            $payment_options_raw = \DB::select("SELECT * FROM st_payment_options WHERE status = 1 ORDER BY sort_order ASC");

            // 转换为对象集合，添加访问器方法
            $payment_options = collect($payment_options_raw)->map(function($option) {
                $obj = (object) $option;
                // 添加向后兼容的属性访问
                $obj->title = $obj->name;
                $obj->desc = $obj->description;
                $obj->more = json_decode($obj->config ?? '{}', true);
                return $obj;
            });
        } else {
            // 获取启用的支付方式，按排序显示
            $payment_options_raw = \DB::select("SELECT * FROM st_payment_options WHERE status = 1 ORDER BY sort_order ASC");

            // 转换为对象集合，添加访问器方法
            $payment_options = collect($payment_options_raw)->map(function($option) {
                $obj = (object) $option;
                // 添加向后兼容的属性访问
                $obj->title = $obj->name;
                $obj->desc = $obj->description;
                $obj->more = json_decode($obj->config ?? '{}', true);
                return $obj;
            });

            // 检查是否有多个PayPal类型启用，如果有，只保留一个
            $paypalOptions = $payment_options->whereIn('code', ['paypal', 'paypal_legacy', 'paypal_new']);
            if ($paypalOptions->count() > 1) {
                // 获取当前配置的PayPal类型，如果表不存在则使用默认值
                try {
                    $currentPaypalType = \DB::table('web_configs')
                        ->where('config_key', 'paypal_type')
                        ->value('config_value') ?: 'legacy';
                } catch (\Exception $e) {
                    // 如果表不存在，使用默认值
                    $currentPaypalType = 'legacy';
                }

                // 根据配置保留对应的PayPal
                $keepCode = $currentPaypalType === 'new' ? 'paypal_new' :
                           ($currentPaypalType === 'legacy' ? 'paypal_legacy' : 'paypal');

                // 过滤掉其他PayPal选项
                $payment_options = $payment_options->reject(function ($option) use ($keepCode) {
                    return in_array($option->code, ['paypal', 'paypal_legacy', 'paypal_new']) && $option->code !== $keepCode;
                });
            }
        }

        // 如果没有支付选项，记录错误但不创建假数据
        if ($payment_options->isEmpty()) {
            \Log::warning('No payment options found in database');
        }

        // 调试信息 - 临时添加
        \Log::info('Payment options debug', [
            'isGuest' => $isGuest,
            'payment_options_count' => $payment_options->count(),
            'payment_options' => $payment_options->toArray()
        ]);

        //默认地址
        $default_address = null;
        if (!$isGuest) {
            // 已登录用户：获取默认地址
            $default_address = UserAddress::where('user_id', $user->id)->where('is_default', 1)->first();
            if (!$default_address)
            {
                $default_address = Order::orderByDesc('id')
                        ->where('user_id', $user->id)
                        ->select('country_code', 'first_name', 'last_name', 'email', 'phone', 'fax', 'address_line_1', 'address_line_2', 'city', 'state_code', 'state_other', 'postal_code', 'billing_country_code', 'billing_first_name', 'billing_last_name', 'billing_email', 'billing_phone', 'billing_fax', 'billing_address_line_1', 'billing_address_line_2', 'billing_city', 'billing_state_code', 'billing_state_other', 'billing_postal_code')
                        ->first();
                if (!$default_address)
                {
                    $default_address = new \stdClass();
                    $default_address->country_code = $user->country_code;
                    $default_address->first_name = $user->first_name;
                    $default_address->last_name = $user->last_name;
                    $default_address->email = $user->email;
                }
            }
        } else {
            // 游客用户：创建空的地址对象
            $default_address = new \stdClass();
            $default_address->country_code = 'US'; // 默认国家
            $default_address->first_name = '';
            $default_address->last_name = '';
            $default_address->email = '';
            $default_address->phone = '';
        }

        //国家
        $countries = RegionRepository::countries();
        //省
        $states = $default_address->country_code ? RegionRepository::states($default_address->country_code) : [];
        //配送方式
        $shipping_options = ShippingRepository::getShippingFeeList($cart['cart_weight'], $default_address->country_code);

        //本次订单可使用积分额度（游客不能使用积分）
        $viable_credits = 0;
        if (!$isGuest) {
            $use_credits_amount = intval($cart['total']['cart_total'] / 2);
            $viable_credits = CartRepository::getUsedCreditsByUseCreditsAmount($use_credits_amount);
        }

        return view('shoppingcart.checkout', [
            'cart' => $cart,
            'shipping_options' => $shipping_options,
            'payment_options' => $payment_options,
            'default_address' => $default_address,
            'countries' => $countries,
            'states' => $states,
            'viable_credits' => $viable_credits,
            'is_guest' => $isGuest,
        ]);
    }

    /**
     * 配送方式列表
     * @param Request $request
     * @return type
     */
    public function getShippingOptions(Request $request)
    {
        $rules = [
            'country_code' => ['required', 'string', 'max:255'],
            'state_code' => ['nullable', 'string', 'max:255'],
            'buyNow' => ['nullable', 'in:0,1'], //是否是立即购买
        ];
        $messages = [];
        $customAttributes = [];
        $validator = Validator::make($request->all(), $rules, $messages, $customAttributes);
        if ($validator->fails())
        {
            return response()->json(['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()]);
        }
        $cart = CartRepository::getCart($request->buyNow);
        $shipping_options = ShippingRepository::getShippingFeeList($cart['cart_weight'], $request->country_code);
        if ($this->isApi)
        {
            return response()->json(['code' => 200, 'message' => __('Success'), 'data' => $shipping_options]);
        }
        $content = (string) view('layouts.includes.shippingOptions', ['shipping_options' => $shipping_options]);
        return response()->json(['code' => 200, 'message' => __('Success'), 'data' => $shipping_options, 'content' => $content]);
    }

    /**
     * 支付方式列表
     * @return type
     */
    public function getPaymentOptions()
    {
        $rows = PaymentOption::where('status', 1)->get();
        $rows->makeHidden('more');
        return response()->json(['code' => 200, 'message' => __('Success'), 'data' => $rows]);
    }

    /**
     * 订单总计
     */
    public function orderTotal(Request $request)
    {
        $rules = [
            'country_code' => ['required', 'string', 'max:255'],
            'shipping_option_id' => ['required', 'integer', 'exists:shipping_option,id'],
            'payment_option_id' => ['required', 'integer', 'exists:payment_options,id'],
            'use_credits' => ['nullable', 'integer', 'gte:100'],
            'buyNow' => ['nullable', 'in:0,1'], //是否是立即购买
        ];
        $messages = [];
        $customAttributes = [];
        $validator = Validator::make($request->all(), $rules, $messages, $customAttributes);
        if ($validator->fails())
        {
            return response()->json(['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()]);
        }
        // 检查是否是立即购买模式
        $buyNow = $request->get('buyNow') == '1' || $request->get('buyNow') === true;

        $cart = CartRepository::getCart($buyNow);
        $order_total = CartRepository::getOrderTotal($request, $cart);
        $data = $order_total;
        //订单结算货币
        $data['order_total_defaultCurrencyPay'] = $data['order_total'];
        if (config('strongshop.defaultCurrencyPay') !== AppRepository::getCurrentCurrency())
        {
            $data['order_total_defaultCurrencyPay'] = AppRepository::convertCurrencyToDefault($data['order_total']);
        }
        return response()->json(['code' => 200, 'message' => __('Success'), 'data' => $data]);
    }

    /**
     * 创建订单
     * @param Request $request
     * @return type
     */
    public function createOrder(Request $request)
    {
        // 检查是否是立即购买模式
        $buyNow = $request->get('buyNow') == '1' || $request->get('buyNow') === true;

        // 检查用户是否登录
        $user = $request->user('web');
        $isGuest = !$user;

        // 如果用户已登录，检查邮箱验证
        if (!$isGuest && app('strongshop')->getShopConfig('order_checkout_email_verified') && $user instanceof MustVerifyEmail && !$user->hasVerifiedEmail())
        {
            return response()->json(['code' => 4003, 'message' => __('Your email address is not verified')]);
        }

        // 如果是游客下单，检查系统设置
        if ($isGuest) {
            try {
                $guestOrderEnabled = \App\Models\SystemSetting::get('guest_order_enabled', false);
            } catch (\Exception $e) {
                // 如果数据库查询失败，临时启用游客下单
                $guestOrderEnabled = true;
            }

            if (!$guestOrderEnabled) {
                return response()->json(['code' => 4004, 'message' => __('Guest order is not enabled')]);
            }

            // 检查游客下单的必填字段要求
            try {
                $requirePhone = \App\Models\SystemSetting::get('guest_order_require_phone', true);
                $requireEmail = \App\Models\SystemSetting::get('guest_order_require_email', true);
            } catch (\Exception $e) {
                // 如果数据库查询失败，使用默认值
                $requirePhone = true;
                $requireEmail = true;
            }

            if ($requirePhone && empty($request->phone)) {
                return ['code' => 4005, 'message' => __('Phone number is required for guest orders')];
            }

            if ($requireEmail && empty($request->email)) {
                return response()->json(['code' => 4006, 'message' => __('Email address is required for guest orders')]);
            }

            // 智能账户关联检查
            if (!empty($request->email)) {
                $existingUserByEmail = \App\Models\User::where('email', $request->email)->first();
                if ($existingUserByEmail) {
                    // 邮箱已存在，检查手机号是否匹配
                    $orderPhone = $request->phone ?? '';
                    $userPhone = $existingUserByEmail->mobile ?? '';

                    if (!empty($orderPhone) && !empty($userPhone) && $orderPhone !== $userPhone) {
                        // 手机号不匹配，提示用户确认
                        return response()->json([
                            'code' => 4007,
                            'message' => "该邮箱已注册，但手机号不匹配。如果这是您的账户，请登录后下单；如果不是，请使用其他邮箱。\n注册手机号：" . substr($userPhone, 0, 3) . '****' . substr($userPhone, -4)
                        ]);
                    }

                    // 邮箱存在且手机号匹配（或其中一个为空），将自动关联账户
                    // 这里不返回错误，让订单继续创建，在自动注册监听器中处理关联
                }
            }

            // 检查手机号是否已被其他邮箱注册
            if (!empty($request->phone)) {
                $existingUserByPhone = \App\Models\User::where('mobile', $request->phone)->first();
                if ($existingUserByPhone && $existingUserByPhone->email !== $request->email) {
                    // 手机号被其他邮箱注册，不允许使用
                    return response()->json([
                        'code' => 4008,
                        'message' => '该手机号已被其他邮箱注册，请使用其他手机号或登录原账户'
                    ]);
                }
            }
        }
        $rules = [
            'buyNow' => ['nullable', 'in:0,1'], //是否是立即购买
            //配送地址
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'string', 'email', 'max:255'],
            'phone' => ['required', 'string', 'max:255'],
            'fax' => ['nullable', 'string', 'max:255'],
            'country_code' => ['required', 'string', 'max:255', Rule::in(collect(RegionRepository::countries())->keys())],
            'state_code' => ['required_without:state_other', 'nullable', 'string', 'max:255'],
            'state_other' => ['required_without:state_code', 'nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'address_line_1' => ['required', 'string', 'max:255'],
            'address_line_2' => ['nullable', 'string', 'max:255'],
            'postal_code' => ['required', 'string', 'max:255'],
            //账单地址
            'billing_first_name' => ['required', 'string', 'max:255'],
            'billing_last_name' => ['required', 'string', 'max:255'],
            'billing_email' => ['required', 'string', 'max:255'],
            'billing_phone' => ['required', 'string', 'max:255'],
            'billing_fax' => ['nullable', 'string', 'max:255'],
            'billing_country_code' => ['required', 'string', 'max:255'],
            'billing_state_code' => ['required_without:billing_state_other', 'nullable', 'string', 'max:255'],
            'billing_state_other' => ['required_without:billing_state_code', 'nullable', 'string', 'max:255'],
            'billing_city' => ['required', 'string', 'max:255'],
            'billing_address_line_1' => ['required', 'string', 'max:255'],
            'billing_address_line_2' => ['nullable', 'string', 'max:255'],
            'billing_postal_code' => ['required', 'string', 'max:255'],
            'shipping_option_id' => ['required', 'integer', Rule::exists('shipping_option_config', 'shipping_option_id')->where('status', 1)->where(function ($query)use ($request) {
                    return $query->whereJsonContains('countries', [$request->country_code]);
                })],
            'payment_option_id' => ['required', 'integer'],
            'remark' => ['nullable', 'string', 'max:255'],
        ];

        // 如果用户已登录，添加积分验证规则
        if (!$isGuest) {
            $rules['use_credits'] = ['nullable', 'integer', 'gte:100', 'lte:' . $user->pay_credits];
        }
        $messages = [];
        $customAttributes = [
            'state_code' => __('State/Province'),
            'state_other' => __('Other State/Province'),
        ];
        $validator = Validator::make($request->all(), $rules, $messages, $customAttributes);
        if ($validator->fails())
        {
            return response()->json(['code' => 3001, 'message' => $validator->errors()->first(), 'data' => $validator->errors()]);
        }
        $order_data = $request->only(array_keys($rules));
        //购物车信息
        $cart = CartRepository::getCart($buyNow);
        if (count($cart['rows']) < 1)
        {
            return response()->json(['code' => 4001, 'message' => __('Shopping Cart is empty.')]);
        }
        //本次订单可使用积分额度（游客不能使用积分）
        $viable_credits = 0;
        if (!$isGuest) {
            $use_credits_amount = intval($cart['total']['cart_total'] / 2);
            $viable_credits = CartRepository::getUsedCreditsByUseCreditsAmount($use_credits_amount);
            if ($request->use_credits > $viable_credits)
            {
                return response()->json(['code' => 4002, 'message' => __('The maximum credits for the order that you can use are <b>:viable_credits</b> credits.', ['viable_credits' => $viable_credits])]);
            }
        }
        //当前选择的货币
        $currency_code = AppRepository::getCurrentCurrency();
        //订单合计
        $order_total = CartRepository::getOrderTotal($request, $cart);
        //当前货币转换率
        $currency_rate = AppRepository::getCurrentCurrencyRate();
        //产品金额
        $products_amount = $order_total['cart_total'];
        //配送费用
        $shipping_fee = $order_total['shipping_fee'];
        //支付手续费
        $handling_fee = $order_total['handling_fee'];
        //税收费用
        $tax_fee = $order_total['tax_fee'];
        //订单金额，支付金额
        $order_amount = $order_total['order_total'];
        //使用积分金额
        $used_credits_amount = $order_total['used_credits_amount'];
        //折扣金额
        $discount_amount = 0;

        /**
         * 生成订单
         */
        $orderModel = new Order();
        $orderModel->fill($order_data);
        $orderModel->user_id = $isGuest ? 0 : $user->id;
        $orderModel->order_no = OrderRepository::generateOrderNo();
        $orderModel->currency_code = $currency_code;
        $orderModel->currency_rate = $currency_rate;
        $orderModel->weight_total = $cart['cart_weight']; //产品总重
        $orderModel->products_amount = $products_amount; //产品金额
        $orderModel->shipping_fee = $shipping_fee; //配送费用
        $orderModel->handling_fee = $handling_fee; //支付手续费
        $orderModel->tax_fee = $tax_fee; //税收费用
        $orderModel->order_amount = $order_amount; //订单金额，支付金额
        $orderModel->paid_amount = 0;
        $orderModel->used_credits_amount = $used_credits_amount; //积分金额
        $orderModel->discount_amount = $discount_amount; //折扣金额
        $orderModel->order_status = Order::STATUS_UNPAID; //订单状态
        $orderModel->shipping_option_id = $request->shipping_option_id;
        $orderModel->payment_option_id = $request->payment_option_id;
        $orderModel->remark = (string) $request->remark;

        DB::transaction(function ()use ($orderModel, $cart, $request, $buyNow) {
            $orderModel->save();
            //订单产品
            foreach ($cart['rows'] as $cartRow)
            {
                $specs = [];
                foreach ($cartRow['product']['specs'] as $spec)
                {
                    $specs[] = [
                        'name' => $spec['name'],
                        'value' => $spec['pivot']['spec_value']
                    ];
                }
                $orderProducts[] = [
                    'order_id' => $orderModel->id,
                    'product_id' => $cartRow['product']['id'],
                    'title' => $cartRow['product']['title'],
                    'sku' => $cartRow['product']['sku'],
                    'weight' => $cartRow['product']['weight'],
                    'currency_code' => $orderModel->currency_code,
                    'currency_rate' => $orderModel->currency_rate,
                    'original_price' => $cartRow['product']['original_price'],
                    'sale_price' => $cartRow['product_price_now'],
                    'img_cover' => $cartRow['product']['img_cover'],
                    'specs' => json_encode($specs),
                    'qty' => $cartRow['qty'],
                    'created_at' => now(),
                ];
            }
            DB::table('order_product')->insert($orderProducts);
            //清空购物车
            CartRepository::clearCart($buyNow);
        }, 2);

        //触发`下单成功`事件
        event(new CreatedOrder($orderModel));

        //支付方式
        $payment = PaymentOption::find($request->payment_option_id);

        // 如果找不到支付选项，使用默认的或创建临时的
        if (!$payment) {
            // 根据支付选项ID创建临时支付信息
            $paymentMap = [
                1 => ['code' => 'paypal', 'name' => 'PayPal'],
                2 => ['code' => 'stripe', 'name' => 'Credit Card'],
                3 => ['code' => 'bank_transfer', 'name' => 'Bank Transfer'],
            ];

            $paymentInfo = $paymentMap[$request->payment_option_id] ?? $paymentMap[1];
            $paycode = $paymentInfo['code'];
        } else {
            $paycode = $payment->code;
        }

        $url = route("payment.pay", ['orderId' => $orderModel->id, 'paycode' => $paycode]);

        return response()->json(['code' => 200, 'message' => __('Success'), 'data' => [
                'toUrl' => $url,
                'orderId' => $orderModel->id,
                'paycode' => $paycode,
        ]]);
    }



}
