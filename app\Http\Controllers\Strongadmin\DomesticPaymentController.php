<?php

namespace App\Http\Controllers\Strongadmin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\PaymentOption;
use OpenStrong\StrongAdmin\Http\Controllers\BaseController;

/**
 * 国内支付管理控制器
 * Domestic Payment Management Controller
 */
class DomesticPaymentController extends BaseController
{
    /**
     * 支持的国内支付网关
     */
    private $gateways = [
        'alipay' => 'App\Services\Payment\Domestic\AlipayGateway',
        'wechat_pay' => 'App\Services\Payment\Domestic\WechatPayGateway',
        'unionpay' => 'App\Services\Payment\Domestic\UnionPayGateway',
        'qq_pay' => 'App\Services\Payment\Domestic\QQPayGateway',
        'jd_pay' => 'App\Services\Payment\Domestic\JDPayGateway',
        'baidu_pay' => 'App\Services\Payment\Domestic\BaiduPayGateway',
        'suning_pay' => 'App\Services\Payment\Domestic\SuningPayGateway',
        'ping_plus' => 'App\Services\Payment\Domestic\PingPlusGateway',
    ];

    /**
     * 支付方式列表
     */
    public function index()
    {
        // 获取线下扫码支付数据
        $offlineQrPayments = $this->getOfflineQrPayments();

        return view('strongadmin.payment.domestic.index', [
            'offlineQrPayments' => $offlineQrPayments
        ]);
    }

    /**
     * 获取支付方式数据
     */
    public function data(Request $request)
    {
        try {
            // 获取或创建国内支付数据
            $payments = $this->getOrCreateDomesticPaymentData();

            return response()->json([
                'code' => 200,
                'message' => 'Success',
                'data' => $payments
            ]);

        } catch (\Exception $e) {
            Log::error('获取国内支付方式数据失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 5001,
                'message' => '数据获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新支付方式状态 - 启用前必须测试成功
     */
    public function updateStatus(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'status' => 'required|integer|in:0,1',
        ]);

        try {
            $payment = PaymentOption::findOrFail($request->id);

            // 如果要启用支付方式，必须先测试连接成功
            if ($request->status == 1) {
                $config = is_string($payment->config) ? json_decode($payment->config, true) : ($payment->config ?? []);

                // 检查是否需要测试（排除货到付款等不需要网关的支付方式）
                $noTestRequired = [
                    'paondelivery', 'banktransfer', 'bank_transfer', 'cash', 'offline', 'cod', 'qrcode_offline',
                    'offline_alipay', 'offline_wechat', 'offline_qq', 'offline_jd', 'offline_baidu'
                ];

                if (!in_array($payment->code, $noTestRequired)) {
                    // 执行真实的网关测试
                    $testRequest = new Request(['code' => $payment->code]);
                    $testResponse = $this->testGateway($testRequest);
                    $testData = $testResponse->getData(true);

                    if ($testData['code'] !== 200) {
                        return response()->json([
                            'code' => 400,
                            'message' => '启用失败：' . $testData['message'] . '。请先配置并测试连接成功后再启用。'
                        ]);
                    }
                }
            }

            $payment->status = $request->status;
            $payment->save();

            $statusText = $request->status == 1 ? '启用' : '禁用';

            return response()->json([
                'code' => 200,
                'message' => $statusText . '成功'
            ]);

        } catch (\Exception $e) {
            Log::error('更新国内支付方式状态失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 5001,
                'message' => '状态更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试支付网关连接 - 真实API测试
     */
    public function testGateway(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
        ]);

        $code = $request->code;

        try {
            // 获取支付方式配置
            $payment = \App\Models\PaymentOption::where('code', $code)->first();

            if (!$payment) {
                return response()->json([
                    'code' => 4004,
                    'message' => '支付方式不存在，请先在支付管理中创建'
                ]);
            }

            $config = is_string($payment->config) ? json_decode($payment->config, true) : ($payment->config ?? []);

            if (empty($config)) {
                return response()->json([
                    'code' => 4002,
                    'message' => '支付方式未配置，请先配置相关参数'
                ]);
            }

            // 根据支付方式进行真实测试
            switch ($code) {
                case 'alipay':
                    return $this->testAlipayGateway($config);
                case 'wechat_pay':
                    return $this->testWeChatPayGateway($config);
                case 'unionpay':
                    return $this->testUnionPayGateway($config);
                default:
                    return response()->json([
                        'code' => 4001,
                        'message' => '暂不支持该支付方式的自动测试，请手动验证配置'
                    ]);
            }

        } catch (\Exception $e) {
            Log::error('测试国内支付网关失败', ['code' => $code, 'error' => $e->getMessage()]);
            return response()->json([
                'code' => 5001,
                'message' => '测试失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试支付宝网关
     */
    private function testAlipayGateway($config)
    {
        $requiredFields = ['app_id', 'private_key', 'alipay_public_key'];

        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                return response()->json([
                    'code' => 4002,
                    'message' => "支付宝配置缺少必要参数: {$field}"
                ]);
            }
        }

        try {
            // 验证私钥格式
            $privateKey = $config['private_key'];
            if (!str_contains($privateKey, '-----BEGIN') || !str_contains($privateKey, '-----END')) {
                return response()->json([
                    'code' => 4003,
                    'message' => '支付宝私钥格式不正确，请确保包含完整的PEM格式'
                ]);
            }

            // 验证公钥格式
            $publicKey = $config['alipay_public_key'];
            if (!str_contains($publicKey, '-----BEGIN') || !str_contains($publicKey, '-----END')) {
                return response()->json([
                    'code' => 4003,
                    'message' => '支付宝公钥格式不正确，请确保包含完整的PEM格式'
                ]);
            }

            // 测试RSA签名
            $testData = 'alipay_test_' . time();
            $signature = '';

            $privateKeyResource = openssl_pkey_get_private($privateKey);
            if (!$privateKeyResource) {
                return response()->json([
                    'code' => 4003,
                    'message' => '支付宝私钥无效，无法创建RSA资源'
                ]);
            }

            $signResult = openssl_sign($testData, $signature, $privateKeyResource, OPENSSL_ALGO_SHA256);

            if (!$signResult) {
                return response()->json([
                    'code' => 4003,
                    'message' => 'RSA签名失败，请检查支付宝私钥是否正确'
                ]);
            }

            // 验证签名
            $publicKeyResource = openssl_pkey_get_public($publicKey);
            if (!$publicKeyResource) {
                return response()->json([
                    'code' => 4003,
                    'message' => '支付宝公钥无效，无法创建RSA资源'
                ]);
            }

            $verifyResult = openssl_verify($testData, $signature, $publicKeyResource, OPENSSL_ALGO_SHA256);

            if ($verifyResult !== 1) {
                return response()->json([
                    'code' => 4003,
                    'message' => '签名验证失败，请检查支付宝公钥是否正确'
                ]);
            }

            return response()->json([
                'code' => 200,
                'message' => '支付宝配置验证成功',
                'data' => [
                    'app_id' => $config['app_id'],
                    'signature_test' => '通过',
                    'key_format' => '正确',
                    'test_time' => now()->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 5001,
                'message' => '支付宝测试异常: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试微信支付网关
     */
    private function testWeChatPayGateway($config)
    {
        $requiredFields = ['app_id', 'mch_id', 'key'];

        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                return response()->json([
                    'code' => 4002,
                    'message' => "微信支付配置缺少必要参数: {$field}"
                ]);
            }
        }

        // 验证商户号格式
        if (!preg_match('/^\d{10}$/', $config['mch_id'])) {
            return response()->json([
                'code' => 4003,
                'message' => '微信支付商户号格式不正确，应为10位数字'
            ]);
        }

        // 验证API密钥长度
        if (strlen($config['key']) !== 32) {
            return response()->json([
                'code' => 4003,
                'message' => '微信支付API密钥长度不正确，应为32位'
            ]);
        }

        return response()->json([
            'code' => 200,
            'message' => '微信支付配置验证成功',
            'data' => [
                'app_id' => $config['app_id'],
                'mch_id' => $config['mch_id'],
                'key_length' => strlen($config['key']),
                'test_time' => now()->format('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * 测试银联支付网关
     */
    private function testUnionPayGateway($config)
    {
        $requiredFields = ['mer_id', 'cert_path', 'cert_pwd'];

        foreach ($requiredFields as $field) {
            if (empty($config[$field])) {
                return response()->json([
                    'code' => 4002,
                    'message' => "银联支付配置缺少必要参数: {$field}"
                ]);
            }
        }

        // 检查证书文件是否存在
        if (!empty($config['cert_path']) && !file_exists($config['cert_path'])) {
            return response()->json([
                'code' => 4003,
                'message' => '银联支付证书文件不存在: ' . $config['cert_path']
            ]);
        }

        return response()->json([
            'code' => 200,
            'message' => '银联支付配置验证成功',
            'data' => [
                'mer_id' => $config['mer_id'],
                'cert_exists' => file_exists($config['cert_path'] ?? ''),
                'test_time' => now()->format('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * 保存配置
     */
    public function saveConfig(Request $request)
    {
        try {
            // 记录请求数据用于调试
            Log::info('保存支付配置请求', [
                'all_input' => $request->all(),
                'code' => $request->input('code'),
                'config' => $request->input('config')
            ]);

            $code = $request->input('code');
            $config = $request->input('config', []);

            if (!$code) {
                Log::warning('支付方式代码为空', ['request_data' => $request->all()]);
                return response()->json([
                    'code' => 4001,
                    'message' => '支付方式代码不能为空'
                ]);
            }

            // 查找或创建支付方式
            $payment = PaymentOption::where('code', $code)->first();

            if (!$payment) {
                // 如果不存在，创建新的支付方式
                $defaultNames = [
                    'alipay' => '支付宝',
                    'wechat_pay' => '微信支付',
                    'unionpay' => '银联支付',
                    'bank_transfer' => '银行转账'
                ];

                $payment = PaymentOption::create([
                    'name' => $defaultNames[$code] ?? ucfirst($code),
                    'code' => $code,
                    'description' => $this->getDefaultPaymentInfo($code)['description'],
                    'status' => 0,
                    'config' => json_encode($config),
                    'sort_order' => $this->getNextSortOrder()
                ]);
            } else {
                // 更新现有配置
                $existingConfig = [];
                if ($payment->config) {
                    $existingConfig = is_string($payment->config) ? json_decode($payment->config, true) : $payment->config;
                }

                // 合并配置
                $mergedConfig = array_merge($existingConfig, $config);
                $payment->config = json_encode($mergedConfig);
                $payment->save();
            }

            return response()->json([
                'code' => 200,
                'message' => '配置保存成功',
                'data' => [
                    'id' => $payment->id,
                    'code' => $payment->code,
                    'config' => json_decode($payment->config, true)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('保存国内支付配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'code' => $code,
                'config' => $config
            ]);
            return response()->json([
                'code' => 5001,
                'message' => '配置保存失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取下一个排序号
     */
    private function getNextSortOrder()
    {
        $maxSort = PaymentOption::max('sort_order');
        return ($maxSort ?? 0) + 10;
    }

    /**
     * 获取或创建国内支付数据
     */
    private function getOrCreateDomesticPaymentData()
    {
        // 从数据库获取真实的国内支付方式数据
        $domesticCodes = ['alipay', 'wechat_pay', 'unionpay', 'bank_transfer'];

        $payments = PaymentOption::whereIn('code', $domesticCodes)
            ->orderBy('sort_order', 'asc')
            ->get()
            ->map(function($payment) {
                $config = [];
                if ($payment->config) {
                    $config = is_string($payment->config) ? json_decode($payment->config, true) : $payment->config;
                }

                // 添加默认的显示信息
                $defaultInfo = $this->getDefaultPaymentInfo($payment->code);
                $config = array_merge($defaultInfo, $config);

                return [
                    'id' => $payment->id,
                    'name' => $payment->name,
                    'code' => $payment->code,
                    'description' => $payment->description ?: $defaultInfo['description'],
                    'config' => json_encode($config),
                    'status' => $payment->status,
                    'sort_order' => $payment->sort_order,
                    'created_at' => $payment->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $payment->updated_at->format('Y-m-d H:i:s'),
                ];
            })
            ->toArray();

        // 如果数据库中没有数据，返回默认数据
        if (empty($payments)) {
            return $this->getDefaultDomesticPaymentData();
        }

        return $payments;
    }

    /**
     * 获取默认支付方式信息
     */
    private function getDefaultPaymentInfo($code)
    {
        $defaults = [
            'alipay' => [
                'website' => 'https://www.alipay.com',
                'supported_countries' => '中国大陆',
                'supported_currencies' => 'CNY',
                'fees' => '0.55% - 1.2%',
                'settlement_time' => 'T+1',
                'features' => ['扫码支付', '网页支付', 'APP支付', '花呗分期', '余额支付'],
                'description' => '中国最大的第三方支付平台，支持扫码支付、网页支付、APP支付等多种方式。'
            ],
            'wechat_pay' => [
                'website' => 'https://pay.weixin.qq.com',
                'supported_countries' => '中国大陆',
                'supported_currencies' => 'CNY',
                'fees' => '0.6%',
                'settlement_time' => 'T+1',
                'features' => ['扫码支付', '小程序支付', '公众号支付', 'APP支付', '刷脸支付'],
                'description' => '腾讯旗下支付平台，中国用户首选支付方式，支持扫码、小程序、公众号支付。'
            ],
            'unionpay' => [
                'website' => 'https://www.unionpay.com',
                'supported_countries' => '中国大陆',
                'supported_currencies' => 'CNY',
                'fees' => '0.5% - 1.0%',
                'settlement_time' => 'T+1',
                'features' => ['银行卡支付', '网银支付', '快捷支付', '无卡支付'],
                'description' => '中国银联官方支付平台，支持所有银联卡在线支付。'
            ],
            'bank_transfer' => [
                'website' => '',
                'supported_countries' => '全球',
                'supported_currencies' => 'CNY, USD, EUR',
                'fees' => '银行手续费',
                'settlement_time' => '1-3个工作日',
                'features' => ['线下转账', '银行汇款', '支票支付', '现金支付'],
                'description' => '传统银行转账方式，适合大额支付，需要人工确认。'
            ],
            'offline_alipay' => [
                'website' => 'https://www.alipay.com',
                'supported_countries' => '中国大陆',
                'supported_currencies' => 'CNY',
                'fees' => '无手续费',
                'settlement_time' => '人工确认',
                'features' => ['扫码支付', '线下收款', '即时到账'],
                'description' => '线下支付宝扫码支付，客户扫码后需联系客服确认。'
            ],
            'offline_wechat' => [
                'website' => 'https://pay.weixin.qq.com',
                'supported_countries' => '中国大陆',
                'supported_currencies' => 'CNY',
                'fees' => '无手续费',
                'settlement_time' => '人工确认',
                'features' => ['扫码支付', '线下收款', '即时到账'],
                'description' => '线下微信扫码支付，客户扫码后需联系客服确认。'
            ],
            'offline_qq' => [
                'website' => 'https://qpay.qq.com',
                'supported_countries' => '中国大陆',
                'supported_currencies' => 'CNY',
                'fees' => '无手续费',
                'settlement_time' => '人工确认',
                'features' => ['扫码支付', '线下收款', 'QQ钱包'],
                'description' => '线下QQ钱包扫码支付，客户扫码后需联系客服确认。'
            ],
            'offline_jd' => [
                'website' => 'https://pay.jd.com',
                'supported_countries' => '中国大陆',
                'supported_currencies' => 'CNY',
                'fees' => '无手续费',
                'settlement_time' => '人工确认',
                'features' => ['扫码支付', '线下收款', '京东支付'],
                'description' => '线下京东支付扫码支付，客户扫码后需联系客服确认。'
            ],
            'offline_baidu' => [
                'website' => 'https://pay.baidu.com',
                'supported_countries' => '中国大陆',
                'supported_currencies' => 'CNY',
                'fees' => '无手续费',
                'settlement_time' => '人工确认',
                'features' => ['扫码支付', '线下收款', '百度钱包'],
                'description' => '线下百度钱包扫码支付，客户扫码后需联系客服确认。'
            ]
        ];

        return $defaults[$code] ?? [
            'website' => '',
            'supported_countries' => '中国大陆',
            'supported_currencies' => 'CNY',
            'fees' => '联系商务',
            'settlement_time' => 'T+1',
            'features' => ['在线支付'],
            'description' => '第三方支付平台'
        ];
    }

    /**
     * 获取默认的国内支付数据（当数据库为空时使用）
     */
    private function getDefaultDomesticPaymentData()
    {
        return [
            [
                'id' => 1,
                'name' => '支付宝',
                'code' => 'alipay',
                'description' => '中国最大的第三方支付平台，支持扫码支付、网页支付、APP支付等多种方式。',
                'config' => json_encode([
                    'website' => 'https://www.alipay.com',
                    'supported_countries' => '中国大陆',
                    'supported_currencies' => 'CNY',
                    'fees' => '0.55% - 1.2%',
                    'settlement_time' => 'T+1',
                    'features' => ['扫码支付', '网页支付', 'APP支付', '花呗分期', '余额支付']
                ]),
                'status' => 0,
                'sort_order' => 10,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 2,
                'name' => '微信支付',
                'code' => 'wechat_pay',
                'description' => '腾讯旗下支付平台，中国用户首选支付方式，支持扫码、小程序、公众号支付。',
                'config' => json_encode([
                    'website' => 'https://pay.weixin.qq.com',
                    'supported_countries' => '中国大陆',
                    'supported_currencies' => 'CNY',
                    'fees' => '0.6%',
                    'settlement_time' => 'T+1',
                    'features' => ['扫码支付', '小程序支付', '公众号支付', 'APP支付', '刷脸支付']
                ]),
                'status' => 0,
                'sort_order' => 20,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 3,
                'name' => '银联支付',
                'code' => 'unionpay',
                'description' => '中国银联官方支付平台，支持所有银联卡，安全可靠。',
                'config' => json_encode([
                    'website' => 'https://www.unionpay.com',
                    'supported_countries' => '中国大陆',
                    'supported_currencies' => 'CNY',
                    'fees' => '0.5% - 0.8%',
                    'settlement_time' => 'T+1',
                    'features' => ['银联卡支付', '云闪付', '手机银行', '网银支付', '快捷支付']
                ]),
                'status' => 0,
                'sort_order' => 30,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 4,
                'name' => 'QQ钱包',
                'code' => 'qq_pay',
                'description' => '腾讯QQ钱包支付，年轻用户群体首选，支持QQ内支付。',
                'config' => json_encode([
                    'website' => 'https://qpay.qq.com',
                    'supported_countries' => '中国大陆',
                    'supported_currencies' => 'CNY',
                    'fees' => '0.6%',
                    'settlement_time' => 'T+1',
                    'features' => ['QQ内支付', '扫码支付', '红包支付', '游戏支付']
                ]),
                'status' => 0,
                'sort_order' => 40,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 5,
                'name' => '京东支付',
                'code' => 'jd_pay',
                'description' => '京东金融旗下支付平台，支持京东白条、小金库等多种支付方式。',
                'config' => json_encode([
                    'website' => 'https://pay.jd.com',
                    'supported_countries' => '中国大陆',
                    'supported_currencies' => 'CNY',
                    'fees' => '0.6% - 1.0%',
                    'settlement_time' => 'T+1',
                    'features' => ['京东白条', '小金库支付', '银行卡支付', '扫码支付']
                ]),
                'status' => 0,
                'sort_order' => 50,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 6,
                'name' => '百度钱包',
                'code' => 'baidu_pay',
                'description' => '百度旗下支付平台，支持百度系产品内支付。',
                'config' => json_encode([
                    'website' => 'https://pay.baidu.com',
                    'supported_countries' => '中国大陆',
                    'supported_currencies' => 'CNY',
                    'fees' => '0.6%',
                    'settlement_time' => 'T+1',
                    'features' => ['百度系支付', '扫码支付', '银行卡支付']
                ]),
                'status' => 0,
                'sort_order' => 60,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 7,
                'name' => '苏宁支付',
                'code' => 'suning_pay',
                'description' => '苏宁易购旗下支付平台，支持苏宁任性付等特色功能。',
                'config' => json_encode([
                    'website' => 'https://pay.suning.com',
                    'supported_countries' => '中国大陆',
                    'supported_currencies' => 'CNY',
                    'fees' => '0.6% - 1.0%',
                    'settlement_time' => 'T+1',
                    'features' => ['任性付', '零钱支付', '银行卡支付', '扫码支付']
                ]),
                'status' => 0,
                'sort_order' => 70,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 8,
                'name' => 'Ping++',
                'code' => 'ping_plus',
                'description' => '聚合支付平台，一次接入支持多种支付方式，简化开发流程。',
                'config' => json_encode([
                    'website' => 'https://www.pingxx.com',
                    'supported_countries' => '中国大陆',
                    'supported_currencies' => 'CNY',
                    'fees' => '按渠道收费',
                    'settlement_time' => 'T+1',
                    'features' => ['聚合支付', '一次接入', '多渠道支持', '统一管理']
                ]),
                'status' => 0,
                'sort_order' => 80,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * 获取线下扫码支付数据
     */
    private function getOfflineQrPayments()
    {
        try {
            // 检查表是否存在
            if (!\Schema::hasTable('offline_qr_payments')) {
                return [];
            }

            return \App\Models\OfflineQrPayment::orderBy('sort_order', 'asc')
                                              ->orderBy('id', 'asc')
                                              ->get();
        } catch (\Exception $e) {
            \Log::error('获取线下扫码支付数据失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 线下扫码支付管理
     */
    public function offlineQrPayments()
    {
        $offlineQrPayments = $this->getOfflineQrPayments();
        return view('strongadmin.payment.domestic.offline-qr', [
            'offlineQrPayments' => $offlineQrPayments
        ]);
    }

    /**
     * 获取线下扫码支付数据API
     */
    public function getOfflineQrPaymentsData()
    {
        try {
            $payments = $this->getOfflineQrPayments();
            return response()->json([
                'code' => 200,
                'message' => 'Success',
                'data' => $payments
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取数据失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建线下扫码支付方式
     */
    public function storeOfflineQrPayment(\Illuminate\Http\Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'cover_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'qr_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'description' => 'nullable|string',
            'instructions' => 'nullable|string',
            'status' => 'required|integer|in:0,1',
            'sort_order' => 'nullable|integer'
        ]);

        try {
            $data = $request->only([
                'name', 'description', 'instructions', 'status'
            ]);

            // 处理封面图片上传（必须上传）
            if ($request->hasFile('cover_image')) {
                $file = $request->file('cover_image');
                $filename = 'cover_' . time() . '_' . $file->getClientOriginalName();

                // 保存到storage目录
                $path = $file->storeAs('qr_codes', $filename, 'public');

                // 确保public/storage/qr_codes目录存在
                $publicQrDir = public_path('storage/qr_codes');
                if (!file_exists($publicQrDir)) {
                    mkdir($publicQrDir, 0755, true);
                }

                // 复制文件到public目录
                $storagePath = storage_path('app/public/' . $path);
                $publicPath = public_path('storage/' . $path);
                if (file_exists($storagePath)) {
                    copy($storagePath, $publicPath);
                }

                $data['cover_image'] = '/storage/' . $path;
            }

            // 处理二维码图片上传（必须上传）
            if ($request->hasFile('qr_image')) {
                $file = $request->file('qr_image');
                $filename = 'qr_' . time() . '_' . $file->getClientOriginalName();

                // 保存到storage目录
                $path = $file->storeAs('qr_codes', $filename, 'public');

                // 确保public/storage/qr_codes目录存在
                $publicQrDir = public_path('storage/qr_codes');
                if (!file_exists($publicQrDir)) {
                    mkdir($publicQrDir, 0755, true);
                }

                // 复制文件到public目录
                $storagePath = storage_path('app/public/' . $path);
                $publicPath = public_path('storage/' . $path);
                if (file_exists($storagePath)) {
                    copy($storagePath, $publicPath);
                }

                $data['qr_image'] = '/storage/' . $path;
            }

            // 设置默认值
            $data['icon'] = 'fas fa-qrcode';
            $data['color'] = '#000000';
            $data['sort_order'] = $request->input('sort_order', 0);

            $payment = \App\Models\OfflineQrPayment::create($data);

            return response()->json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $payment
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新线下扫码支付状态
     */
    public function updateOfflineQrPaymentStatus(\Illuminate\Http\Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'status' => 'required|integer|in:0,1'
        ]);

        try {
            // 检查主开关状态
            $masterEnabled = \Illuminate\Support\Facades\Cache::get('offline_qr_master_enabled', true);

            // 如果要启用子支付方式，但主开关是禁用的，则不允许
            if ($request->status == 1 && !$masterEnabled) {
                return response()->json([
                    'code' => 400,
                    'message' => '请先启用线下扫码支付总开关'
                ]);
            }

            $payment = \App\Models\OfflineQrPayment::findOrFail($request->id);
            $payment->status = $request->status;
            $payment->save();

            $statusText = $request->status ? '启用' : '禁用';

            return response()->json([
                'code' => 200,
                'message' => $statusText . '成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '状态更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取单个线下扫码支付方式
     */
    public function getOfflineQrPayment($id)
    {
        try {
            $payment = \App\Models\OfflineQrPayment::findOrFail($id);

            return response()->json([
                'code' => 200,
                'message' => 'Success',
                'data' => $payment
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取数据失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新线下扫码支付方式
     */
    public function updateOfflineQrPayment(\Illuminate\Http\Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'qr_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'description' => 'nullable|string',
            'instructions' => 'nullable|string',
            'status' => 'required|integer|in:0,1',
            'sort_order' => 'nullable|integer'
        ]);

        try {
            $payment = \App\Models\OfflineQrPayment::findOrFail($id);

            $data = $request->only([
                'name', 'description', 'instructions', 'status'
            ]);

            // 处理排序字段
            $data['sort_order'] = $request->input('sort_order', 0);

            // 处理封面图片上传
            if ($request->hasFile('cover_image')) {
                // 删除旧封面图
                if ($payment->cover_image && file_exists(public_path($payment->cover_image))) {
                    unlink(public_path($payment->cover_image));
                }
                // 同时删除storage中的旧封面图
                if ($payment->cover_image) {
                    $oldStoragePath = storage_path('app/public' . str_replace('/storage', '', $payment->cover_image));
                    if (file_exists($oldStoragePath)) {
                        unlink($oldStoragePath);
                    }
                }

                $file = $request->file('cover_image');
                $filename = 'cover_' . time() . '_' . $file->getClientOriginalName();

                // 保存到storage目录
                $path = $file->storeAs('qr_codes', $filename, 'public');

                // 确保public/storage/qr_codes目录存在
                $publicQrDir = public_path('storage/qr_codes');
                if (!file_exists($publicQrDir)) {
                    mkdir($publicQrDir, 0755, true);
                }

                // 复制文件到public目录
                $storagePath = storage_path('app/public/' . $path);
                $publicPath = public_path('storage/' . $path);
                if (file_exists($storagePath)) {
                    copy($storagePath, $publicPath);
                }

                $data['cover_image'] = '/storage/' . $path;
            }

            // 处理二维码图片上传
            if ($request->hasFile('qr_image')) {
                // 删除旧二维码图片
                if ($payment->qr_image && file_exists(public_path($payment->qr_image))) {
                    unlink(public_path($payment->qr_image));
                }
                // 同时删除storage中的旧二维码图片
                if ($payment->qr_image) {
                    $oldStoragePath = storage_path('app/public' . str_replace('/storage', '', $payment->qr_image));
                    if (file_exists($oldStoragePath)) {
                        unlink($oldStoragePath);
                    }
                }

                $file = $request->file('qr_image');
                $filename = 'qr_' . time() . '_' . $file->getClientOriginalName();

                // 保存到storage目录
                $path = $file->storeAs('qr_codes', $filename, 'public');

                // 确保public/storage/qr_codes目录存在
                $publicQrDir = public_path('storage/qr_codes');
                if (!file_exists($publicQrDir)) {
                    mkdir($publicQrDir, 0755, true);
                }

                // 复制文件到public目录
                $storagePath = storage_path('app/public/' . $path);
                $publicPath = public_path('storage/' . $path);
                if (file_exists($storagePath)) {
                    copy($storagePath, $publicPath);
                }

                $data['qr_image'] = '/storage/' . $path;
            }

            $payment->update($data);

            return response()->json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $payment
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除线下扫码支付方式
     */
    public function deleteOfflineQrPayment($id)
    {
        try {
            $payment = \App\Models\OfflineQrPayment::findOrFail($id);

            // 删除二维码图片
            if ($payment->qr_image && file_exists(public_path($payment->qr_image))) {
                unlink(public_path($payment->qr_image));
            }

            $payment->delete();

            return response()->json([
                'code' => 200,
                'message' => '删除成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取线下扫码支付主开关状态
     */
    public function getOfflineQrMasterStatus()
    {
        try {
            // 从配置表或缓存中获取主开关状态
            $enabled = \Illuminate\Support\Facades\Cache::get('offline_qr_master_enabled', true);

            return response()->json([
                'code' => 200,
                'message' => 'Success',
                'data' => [
                    'enabled' => $enabled
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取状态失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新线下扫码支付主开关状态
     */
    public function updateOfflineQrMasterStatus(Request $request)
    {
        try {
            $enabled = $request->input('enabled', false);

            // 保存主开关状态到缓存
            \Illuminate\Support\Facades\Cache::forever('offline_qr_master_enabled', $enabled);

            // 如果禁用主开关，同时禁用所有子支付方式
            if (!$enabled) {
                \App\Models\OfflineQrPayment::query()->update(['status' => 0]);
            }

            return response()->json([
                'code' => 200,
                'message' => $enabled ? '线下扫码支付已启用' : '线下扫码支付已禁用'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }
}
