<?php

namespace App\Http\Controllers\Strongadmin;

use Illuminate\Http\Request;
use App\Models\OfflineQrPayment;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;

class OfflineQrPaymentController extends Controller
{
    /**
     * 线下扫码支付管理页面
     */
    public function index()
    {
        return view('strongadmin.offline-qr-payment.index');
    }
    
    /**
     * 获取支付方式列表
     */
    public function data(Request $request)
    {
        try {
            $payments = OfflineQrPayment::orderBy('sort_order', 'asc')
                                      ->orderBy('id', 'asc')
                                      ->get();
            
            return response()->json([
                'code' => 200,
                'message' => 'Success',
                'data' => $payments
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取数据失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 创建支付方式
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:20',
            'qr_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'description' => 'nullable|string',
            'instructions' => 'nullable|string',
            'account_info' => 'nullable|array',
            'customer_service' => 'nullable|array',
            'status' => 'required|integer|in:0,1',
            'sort_order' => 'nullable|integer'
        ]);
        
        try {
            $data = $request->only([
                'name', 'icon', 'color', 'description', 
                'instructions', 'account_info', 'customer_service', 
                'status', 'sort_order'
            ]);
            
            // 处理二维码图片上传
            if ($request->hasFile('qr_image')) {
                $file = $request->file('qr_image');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs('qr_codes', $filename, 'public');
                $data['qr_image'] = '/storage/' . $path;
            }
            
            // 设置默认值
            $data['icon'] = $data['icon'] ?: 'fas fa-qrcode';
            $data['color'] = $data['color'] ?: '#000000';
            $data['sort_order'] = $data['sort_order'] ?: 0;
            
            $payment = OfflineQrPayment::create($data);
            
            return response()->json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $payment
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 更新支付方式
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:20',
            'qr_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'description' => 'nullable|string',
            'instructions' => 'nullable|string',
            'account_info' => 'nullable|array',
            'customer_service' => 'nullable|array',
            'status' => 'required|integer|in:0,1',
            'sort_order' => 'nullable|integer'
        ]);
        
        try {
            $payment = OfflineQrPayment::findOrFail($id);
            
            $data = $request->only([
                'name', 'icon', 'color', 'description', 
                'instructions', 'account_info', 'customer_service', 
                'status', 'sort_order'
            ]);
            
            // 处理二维码图片上传
            if ($request->hasFile('qr_image')) {
                // 删除旧图片
                if ($payment->qr_image && file_exists(public_path($payment->qr_image))) {
                    unlink(public_path($payment->qr_image));
                }
                
                $file = $request->file('qr_image');
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs('qr_codes', $filename, 'public');
                $data['qr_image'] = '/storage/' . $path;
            }
            
            $payment->update($data);
            
            return response()->json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $payment
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 删除支付方式
     */
    public function destroy($id)
    {
        try {
            $payment = OfflineQrPayment::findOrFail($id);
            
            // 删除二维码图片
            if ($payment->qr_image && file_exists(public_path($payment->qr_image))) {
                unlink(public_path($payment->qr_image));
            }
            
            $payment->delete();
            
            return response()->json([
                'code' => 200,
                'message' => '删除成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 更新状态
     */
    public function updateStatus(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'status' => 'required|integer|in:0,1'
        ]);
        
        try {
            $payment = OfflineQrPayment::findOrFail($request->id);
            $payment->status = $request->status;
            $payment->save();
            
            $statusText = $request->status ? '启用' : '禁用';
            
            return response()->json([
                'code' => 200,
                'message' => $statusText . '成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '状态更新失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 更新排序
     */
    public function updateSort(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'sort_order' => 'required|integer'
        ]);
        
        try {
            $payment = OfflineQrPayment::findOrFail($request->id);
            $payment->sort_order = $request->sort_order;
            $payment->save();
            
            return response()->json([
                'code' => 200,
                'message' => '排序更新成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '排序更新失败：' . $e->getMessage()
            ]);
        }
    }
}
