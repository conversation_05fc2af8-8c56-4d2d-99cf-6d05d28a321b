<?php

namespace App\Http\Controllers\Strongadmin;

use Illuminate\Http\Request;
use App\Models\PaymentOption;
use Illuminate\Support\Facades\Log;

class PaymentCenterController extends \OpenStrong\StrongAdmin\Http\Controllers\BaseController
{
    /**
     * 支付管理中心首页
     */
    public function index()
    {
        return view('strongadmin.payment.index');
    }
    
    /**
     * 获取支付方式统计数据
     */
    public function data(Request $request)
    {
        try {
            // 获取所有支付方式 - 从数据库真实读取
            $allPayments = PaymentOption::orderBy('sort_order', 'asc')->orderBy('id', 'asc')->get();

            // 动态分类支付方式 - 不再硬编码
            $domesticPayments = collect();
            $internationalPayments = collect();

            foreach ($allPayments as $payment) {
                if ($this->isDomesticPayment($payment->code)) {
                    $domesticPayments->push($payment);
                } else {
                    $internationalPayments->push($payment);
                }
            }

            $domesticStats = [
                'total' => $domesticPayments->count(),
                'enabled' => $domesticPayments->where('status', 1)->count(),
                'payments' => $domesticPayments->map(function($payment) {
                    // 尝试不同的字段名
                    $name = $payment->name ?? $payment->title ?? $payment->code;
                    return [
                        'id' => $payment->id,
                        'name' => $name,
                        'code' => $payment->code,
                        'status' => $payment->status,
                        'logo' => $this->getPaymentLogo($payment->code),
                        'display_name' => $this->getPaymentDisplayName($payment->code)
                    ];
                })->toArray()
            ];

            $internationalStats = [
                'total' => $internationalPayments->count(),
                'enabled' => $internationalPayments->where('status', 1)->count(),
                'payments' => $internationalPayments->map(function($payment) {
                    // 尝试不同的字段名
                    $name = $payment->name ?? $payment->title ?? $payment->code;
                    return [
                        'id' => $payment->id,
                        'name' => $name,
                        'code' => $payment->code,
                        'status' => $payment->status,
                        'logo' => $this->getPaymentLogo($payment->code),
                        'display_name' => $this->getPaymentDisplayName($payment->code)
                    ];
                })->toArray()
            ];
            
            // 获取总体统计
            $totalStats = [
                'total_payments' => $domesticStats['total'] + $internationalStats['total'],
                'total_enabled' => $domesticStats['enabled'] + $internationalStats['enabled'],
                'domestic_ratio' => $domesticStats['total'] > 0 ? round(($domesticStats['enabled'] / $domesticStats['total']) * 100, 1) : 0,
                'international_ratio' => $internationalStats['total'] > 0 ? round(($internationalStats['enabled'] / $internationalStats['total']) * 100, 1) : 0
            ];
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'domestic' => $domesticStats,
                    'international' => $internationalStats,
                    'total' => $totalStats
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取支付中心数据失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '获取数据失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 判断是否为国内支付方式
     */
    private function isDomesticPayment($code)
    {
        $code = strtolower($code);

        // 明确的国际支付方式（优先判断）
        $internationalPatterns = [
            'stripe', 'paypal', 'visa', 'mastercard', 'amex', 'discover',
            'coinbase', 'bitcoin', 'crypto', 'international', 'global',
            'worldpay', 'square', 'braintree', 'adyen', 'klarna',
            'credit_card', 'creditcard'
        ];

        foreach ($internationalPatterns as $pattern) {
            if (strpos($code, $pattern) !== false) {
                return false;
            }
        }

        // 明确的国内支付方式
        $domesticPatterns = [
            'alipay', 'wechat', 'unionpay', 'qqpay', 'jdpay', 'baidu',
            'paondelivery', 'banktransfer', 'bank_transfer', 'cash', 'offline', 'cod',
            'tenpay', 'yeepay', 'chinapay', 'lakala', 'ping++',
            '支付宝', '微信', '银联', 'qq', '京东', '百度', '货到付款',
            '银行转账', '现金', '线下'
        ];

        foreach ($domesticPatterns as $pattern) {
            if (strpos($code, $pattern) !== false) {
                return true;
            }
        }

        // 对于无法明确分类的，检查更多特征
        // 如果包含中文，很可能是国内支付
        if (preg_match('/[\x{4e00}-\x{9fff}]/u', $code)) {
            return true;
        }

        // 默认归类为国内支付（因为这是中国的系统）
        return true;
    }

    /**
     * 获取支付方式Logo
     */
    private function getPaymentLogo($code)
    {
        $logos = [
            'alipay' => '支',
            'wechat_pay' => '微',
            'unionpay' => '银',
            'paondelivery' => '货',
            'banktransfer' => '转',
            'bank_transfer' => '转',
            'cash' => '现',
            'offline' => '线',
            'qqpay' => 'Q',
            'stripe' => 'S',
            'paypal' => 'P',
            'paypal_legacy' => 'P',
            'paypal_express' => 'P',
            'credit_card' => 'C',
            'alipay_international' => '支',
            'wechat_pay_international' => '微',
            'coinbase' => '₿'
        ];
        
        return $logos[$code] ?? substr(strtoupper($code), 0, 1);
    }
    
    /**
     * 获取支付方式显示名称
     */
    private function getPaymentDisplayName($code)
    {
        $names = [
            'alipay' => '支付宝',
            'wechat_pay' => '微信支付',
            'unionpay' => '银联支付',
            'paondelivery' => '货到付款',
            'banktransfer' => '银行转账',
            'cash' => '现金支付',
            'offline' => '线下支付',
            'qqpay' => 'QQ钱包',
            'stripe' => 'Stripe',
            'paypal' => 'PayPal(新)',
            'paypal_legacy' => 'PayPal(老)',
            'alipay_international' => '支付宝国际',
            'wechat_pay_international' => '微信国际',
            'coinbase' => 'Coinbase'
        ];
        
        return $names[$code] ?? ucfirst($code);
    }
}
