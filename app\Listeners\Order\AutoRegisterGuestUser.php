<?php

namespace App\Listeners\Order;

use App\Events\CreatedOrder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class AutoRegisterGuestUser
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  CreatedOrder  $event
     * @return array|false
     */
    public function handle(CreatedOrder $event)
    {
        $order = $event->order;

        // 如果订单已经有用户ID，跳过
        if ($order->user_id) {
            return false;
        }

        // 强制自动注册模式：游客下单必定自动注册账户
        Log::info('游客订单自动注册模式：强制启用');

        // 检查订单是否有邮箱信息
        if (empty($order->email)) {
            Log::warning('游客订单缺少邮箱信息，无法自动注册', ['order_id' => $order->id]);
            return false;
        }

        // 智能账户关联：检查邮箱是否已存在
        $existingUser = User::where('email', $order->email)->first();
        if ($existingUser) {
            // 邮箱已存在，检查是否可以关联
            $orderPhone = $order->phone ?? '';
            $userPhone = $existingUser->mobile ?? '';

            // 如果手机号匹配或其中一个为空，则关联账户
            if (empty($orderPhone) || empty($userPhone) || $orderPhone === $userPhone) {
                // 关联到现有账户
                $order->user_id = $existingUser->id;
                $order->save();

                Log::info('游客订单智能关联到现有账户', [
                    'order_id' => $order->id,
                    'user_id' => $existingUser->id,
                    'email' => $order->email,
                    'phone_match' => $orderPhone === $userPhone ? 'exact' : 'empty_allowed'
                ]);

                // 将账户信息存储到session中，用于支付页面显示
                session(['auto_registered_account' => [
                    'type' => 'existing',
                    'email' => $existingUser->email,
                    'message' => '订单已关联到您的现有账户'
                ]]);

                return ['type' => 'existing', 'user' => $existingUser];
            } else {
                // 手机号不匹配，这种情况在下单验证时应该已经被拦截
                Log::error('手机号不匹配的用户通过了下单验证', [
                    'order_id' => $order->id,
                    'email' => $order->email,
                    'order_phone' => $orderPhone,
                    'user_phone' => $userPhone
                ]);
                return false;
            }
        }

        try {
            // 创建新用户
            $user = new User();
            $user->email = $order->email;
            $user->first_name = $order->first_name ?? '';
            $user->last_name = $order->last_name ?? '';
            $user->mobile = $order->phone ?? ''; // 下单时已检查重复，这里直接设置

            // 生成随机密码
            $randomPassword = Str::random(12);
            $user->password = Hash::make($randomPassword);

            // 设置用户状态
            $user->email_verified_at = now(); // 自动验证邮箱
            $user->status = 1; // 激活状态

            $user->save();

            // 更新订单的用户ID
            $order->user_id = $user->id;
            $order->save();

            // 记录日志
            Log::info('游客订单自动注册用户成功', [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            // 将账户信息存储到session中，用于支付页面显示
            session(['auto_registered_account' => [
                'type' => 'new',
                'email' => $user->email,
                'password' => $randomPassword,
                'message' => '已为您自动创建账户'
            ]]);

            return ['type' => 'new', 'user' => $user, 'password' => $randomPassword];

        } catch (\Exception $e) {
            Log::error('自动注册用户失败', [
                'order_id' => $order->id,
                'email' => $order->email,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
    
    /**
     * 发送账户创建通知（可选实现）
     */
    private function sendAccountCreatedNotification($user, $password, $order)
    {
        // 这里可以实现发送邮件通知用户账户信息
        // 包括用户名、密码、订单信息等
    }
}
