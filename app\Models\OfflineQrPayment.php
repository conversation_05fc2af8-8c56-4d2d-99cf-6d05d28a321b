<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OfflineQrPayment extends Model
{
    // 直接指定完整表名，绕过前缀
    protected $table = 'offline_qr_payments';

    // 使用不带前缀的数据库连接
    protected $connection = 'mysql_no_prefix';
    
    protected $fillable = [
        'name',
        'icon',
        'color',
        'qr_image',
        'cover_image',
        'description',
        'instructions',
        'account_info',
        'customer_service',
        'status',
        'sort_order'
    ];
    
    protected $casts = [
        'account_info' => 'array',
        'customer_service' => 'array',
        'status' => 'integer',
        'sort_order' => 'integer'
    ];
    
    /**
     * 获取启用的支付方式
     */
    public static function getEnabled()
    {
        return self::where('status', 1)
                   ->orderBy('sort_order', 'asc')
                   ->orderBy('id', 'asc')
                   ->get();
    }
    
    /**
     * 获取二维码图片URL
     */
    public function getQrImageUrlAttribute()
    {
        if (empty($this->qr_image)) {
            return null;
        }

        // 如果是完整URL，直接返回
        if (filter_var($this->qr_image, FILTER_VALIDATE_URL)) {
            return $this->qr_image;
        }

        // 如果是相对路径，添加域名
        return url($this->qr_image);
    }

    /**
     * 获取封面图片URL
     */
    public function getCoverImageUrlAttribute()
    {
        if (empty($this->cover_image)) {
            return null;
        }

        // 如果是完整URL，直接返回
        if (filter_var($this->cover_image, FILTER_VALIDATE_URL)) {
            return $this->cover_image;
        }

        // 如果是相对路径，添加域名
        return url($this->cover_image);
    }
    
    /**
     * 获取支付步骤数组
     */
    public function getInstructionsArrayAttribute()
    {
        if (empty($this->instructions)) {
            return [
                '扫描上方二维码进行支付',
                '在转账备注中填写：订单号 + 您的联系方式',
                '完成支付后，请联系客服确认到账',
                '客服确认后，我们将尽快处理您的订单'
            ];
        }
        
        // 如果是JSON格式
        if (is_string($this->instructions) && json_decode($this->instructions)) {
            return json_decode($this->instructions, true);
        }
        
        // 如果是文本，按行分割
        return array_filter(explode("\n", $this->instructions));
    }
}
