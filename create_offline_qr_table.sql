-- 创建线下扫码支付管理表
CREATE TABLE IF NOT EXISTS `offline_qr_payments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '支付方式名称，如：支付宝付款码',
  `icon` varchar(100) DEFAULT 'fas fa-qrcode' COMMENT '图标class',
  `color` varchar(20) DEFAULT '#000000' COMMENT '主题颜色',
  `qr_image` varchar(500) DEFAULT NULL COMMENT '二维码图片路径',
  `description` text DEFAULT NULL COMMENT '支付说明',
  `instructions` text DEFAULT NULL COMMENT '支付步骤说明',
  `account_info` json DEFAULT NULL COMMENT '收款账户信息',
  `customer_service` json DEFAULT NULL COMMENT '客服联系方式',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_status_sort` (`status`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线下扫码支付管理表';

-- 插入示例数据
INSERT INTO `offline_qr_payments` (`name`, `icon`, `color`, `description`, `instructions`, `account_info`, `customer_service`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
('支付宝付款码', 'fab fa-alipay', '#1677FF', '付款时候记得备注协商订单号，以备我们客服核实否者无法确定您支付的金额，切记切记！', '扫描上方二维码进行支付\n在转账备注中填写：订单号 + 您的联系方式\n完成支付后，请联系客服确认到账\n客服确认后，我们将尽快处理您的订单', '{"收款账户": "<EMAIL>", "收款人": "全速达物流"}', '{"QQ": "*********", "微信": "Jagship", "电话": "***********", "邮箱": "<EMAIL>"}', 1, 1, NOW(), NOW()),
('微信付款码', 'fab fa-weixin', '#07C160', '付款时候记得备注协商订单号，以备我们客服核实否者无法确定您支付的金额，切记切记！', '扫描上方二维码进行支付\n在转账备注中填写：订单号 + 您的联系方式\n完成支付后，请联系客服确认到账\n客服确认后，我们将尽快处理您的订单', '{"收款账户": "Jagship", "收款人": "全速达物流"}', '{"QQ": "*********", "微信": "Jagship", "电话": "***********", "邮箱": "<EMAIL>"}', 1, 2, NOW(), NOW());

-- 查看创建结果
SELECT * FROM `offline_qr_payments`;
