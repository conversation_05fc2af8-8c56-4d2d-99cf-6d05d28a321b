<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOfflineQrPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('offline_qr_payments', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('支付方式名称，如：支付宝付款码');
            $table->string('icon')->default('fas fa-qrcode')->comment('图标class');
            $table->string('color')->default('#000000')->comment('主题颜色');
            $table->string('qr_image')->nullable()->comment('二维码图片路径');
            $table->text('description')->nullable()->comment('支付说明');
            $table->text('instructions')->nullable()->comment('支付步骤说明');
            $table->json('account_info')->nullable()->comment('收款账户信息');
            $table->json('customer_service')->nullable()->comment('客服联系方式');
            $table->tinyInteger('status')->default(1)->comment('状态：0=禁用，1=启用');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            
            $table->index(['status', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('offline_qr_payments');
    }
}
