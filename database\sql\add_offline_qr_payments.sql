-- 添加线下扫码支付方式到支付选项表
-- Add offline QR code payment methods to payment options

-- 插入线下扫码支付方式
INSERT INTO `st_payment_options` (`name`, `code`, `description`, `config`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
('支付宝扫码支付', 'offline_alipay', '线下支付宝扫码支付，扫码后请在备注中填写订单号', 
 JSON_OBJECT(
   'type', 'offline_qr',
   'payment_name', '支付宝扫码支付',
   'icon', 'fab fa-alipay',
   'color', '#1677FF',
   'qr_code_path', '/images/qr/alipay_qr.png',
   'account_info', JSON_OBJECT(
     '收款账户', '<EMAIL>',
     '收款人', '全速达物流'
   ),
   'instructions', JSON_ARRAY(
     '打开支付宝APP，扫描二维码',
     '在转账备注中填写：订单号 + 您的联系方式',
     '完成支付后，请联系客服确认到账',
     '客服会在1-2小时内处理您的订单'
   ),
   'customer_service', JSON_OBJECT(
     'qq', '*********',
     'wechat', 'Jagship',
     'phone', '***********',
     'email', '<EMAIL>'
   )
 ), 0, 100, NOW(), NOW()),

('微信扫码支付', 'offline_wechat', '线下微信扫码支付，扫码后请在备注中填写订单号', 
 JSON_OBJECT(
   'type', 'offline_qr',
   'payment_name', '微信扫码支付',
   'icon', 'fab fa-weixin',
   'color', '#07C160',
   'qr_code_path', '/images/qr/wechat_qr.png',
   'account_info', JSON_OBJECT(
     '收款账户', 'Jagship',
     '收款人', '全速达物流'
   ),
   'instructions', JSON_ARRAY(
     '打开微信APP，扫描二维码',
     '在转账备注中填写：订单号 + 您的联系方式',
     '完成支付后，请联系客服确认到账',
     '客服会在1-2小时内处理您的订单'
   ),
   'customer_service', JSON_OBJECT(
     'qq', '*********',
     'wechat', 'Jagship',
     'phone', '***********',
     'email', '<EMAIL>'
   )
 ), 0, 101, NOW(), NOW()),

('QQ钱包支付', 'offline_qq', '线下QQ钱包扫码支付，扫码后请在备注中填写订单号', 
 JSON_OBJECT(
   'type', 'offline_qr',
   'payment_name', 'QQ钱包支付',
   'icon', 'fab fa-qq',
   'color', '#12B7F5',
   'qr_code_path', '/images/qr/qq_qr.png',
   'account_info', JSON_OBJECT(
     'QQ号码', '*********',
     '收款人', '全速达物流'
   ),
   'instructions', JSON_ARRAY(
     '打开QQ或QQ钱包，扫描二维码',
     '在转账备注中填写：订单号 + 您的联系方式',
     '完成支付后，请联系客服确认到账',
     '客服会在1-2小时内处理您的订单'
   ),
   'customer_service', JSON_OBJECT(
     'qq', '*********',
     'wechat', 'Jagship',
     'phone', '***********',
     'email', '<EMAIL>'
   )
 ), 0, 102, NOW(), NOW()),

('京东支付', 'offline_jd', '线下京东支付扫码支付，扫码后请在备注中填写订单号', 
 JSON_OBJECT(
   'type', 'offline_qr',
   'payment_name', '京东支付',
   'icon', 'fas fa-shopping-cart',
   'color', '#E1251B',
   'qr_code_path', '/images/qr/jd_qr.png',
   'account_info', JSON_OBJECT(
     '收款账户', '<EMAIL>',
     '收款人', '全速达物流'
   ),
   'instructions', JSON_ARRAY(
     '打开京东APP，扫描二维码',
     '在转账备注中填写：订单号 + 您的联系方式',
     '完成支付后，请联系客服确认到账',
     '客服会在1-2小时内处理您的订单'
   ),
   'customer_service', JSON_OBJECT(
     'qq', '*********',
     'wechat', 'Jagship',
     'phone', '***********',
     'email', '<EMAIL>'
   )
 ), 0, 103, NOW(), NOW()),

('百度钱包', 'offline_baidu', '线下百度钱包扫码支付，扫码后请在备注中填写订单号', 
 JSON_OBJECT(
   'type', 'offline_qr',
   'payment_name', '百度钱包',
   'icon', 'fas fa-wallet',
   'color', '#2932E1',
   'qr_code_path', '/images/qr/baidu_qr.png',
   'account_info', JSON_OBJECT(
     '收款账户', '<EMAIL>',
     '收款人', '全速达物流'
   ),
   'instructions', JSON_ARRAY(
     '打开百度APP，扫描二维码',
     '在转账备注中填写：订单号 + 您的联系方式',
     '完成支付后，请联系客服确认到账',
     '客服会在1-2小时内处理您的订单'
   ),
   'customer_service', JSON_OBJECT(
     'qq', '*********',
     'wechat', 'Jagship',
     'phone', '***********',
     'email', '<EMAIL>'
   )
 ), 0, 104, NOW(), NOW())

ON DUPLICATE KEY UPDATE 
`config` = VALUES(`config`),
`updated_at` = NOW();

-- 查看插入结果
SELECT id, name, code, status, sort_order FROM st_payment_options WHERE code LIKE 'offline_%' ORDER BY sort_order;
