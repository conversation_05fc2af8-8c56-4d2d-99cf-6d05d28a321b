<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误处理测试</title>
    <link rel="stylesheet" href="https://unpkg.com/layui@2.6.8/dist/css/layui.css">
    <script src="https://unpkg.com/layui@2.6.8/dist/layui.js"></script>
    <style>
        body {
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .test-button {
            margin: 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .error-highlight {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-left: 4px solid #333;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            color: #333;
            font-size: 14px;
        }
        .error-highlight strong {
            color: #000;
            font-weight: 600;
        }
        .layui-layer.error-dialog {
            border-radius: 8px !important;
            overflow: hidden !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            margin: 0 !important;
            z-index: 99999999 !important;
        }
        .layui-layer-shade {
            z-index: 99999998 !important;
        }
        .layui-layer.error-dialog .layui-layer-main {
            position: relative !important;
        }
        .layui-layer.error-dialog .layui-layer-title {
            background: #333 !important;
            color: white !important;
            font-weight: 600 !important;
            border-radius: 0 !important;
            padding: 15px 20px !important;
            font-size: 16px !important;
            line-height: 1.4 !important;
            display: flex !important;
            align-items: center !important;
            min-height: 50px !important;
        }
        .layui-layer.error-dialog .layui-layer-content {
            padding: 25px !important;
            line-height: 1.6 !important;
            font-size: 14px !important;
            background: white !important;
            display: flex !important;
            align-items: center !important;
            min-height: 120px !important;
        }
        .layui-layer.error-dialog .layui-layer-btn {
            border-top: 1px solid #e9ecef !important;
            background: #f8f9fa !important;
            padding: 15px 20px !important;
            text-align: center !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
        }
        .layui-layer.error-dialog .layui-layer-btn a {
            background: #333 !important;
            color: white !important;
            border-radius: 4px !important;
            margin: 0 !important;
            padding: 0 24px !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
            line-height: 1 !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            height: 40px !important;
            text-decoration: none !important;
            vertical-align: middle !important;
            box-sizing: border-box !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
            font-size: 14px !important;
            letter-spacing: 0.5px !important;
        }
        .layui-layer.error-dialog .layui-layer-btn a:hover {
            background: #000 !important;
            transform: translateY(-1px) !important;
        }
        .error-highlight .phone-mask {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
        }
        .layui-layer.error-dialog .layui-layer-close {
            color: #999 !important;
            font-size: 18px !important;
        }
        .layui-layer.error-dialog .layui-layer-close:hover {
            color: white !important;
            background: rgba(255, 255, 255, 0.1) !important;
        }

        /* 强制重置按钮内部元素的样式 */
        .layui-layer.error-dialog .layui-layer-btn a * {
            line-height: inherit !important;
            vertical-align: baseline !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>错误处理测试</h1>
        <p>点击下面的按钮测试不同的错误提示：</p>
        
        <button class="test-button" onclick="testError(4007)">测试4007错误（邮箱已注册但手机号不匹配）</button>
        <button class="test-button" onclick="testError(4001)">测试4001错误（邮箱已注册）</button>
        <button class="test-button" onclick="testError(4002)">测试4002错误（手机号已注册）</button>
        <button class="test-button" onclick="testError(4000)">测试4000错误（参数错误）</button>
    </div>

    <script>
        layui.use(['layer'], function(){
            var layer = layui.layer;
            
            window.testError = function(code) {
                fetch('/debug/test-error/' + code)
                .then(response => response.json())
                .then(res => {
                    console.log('Error response:', res);
                    console.log('Error code:', res.code);
                    console.log('Error message:', res.message);
                    
                    if (res.code === 4007) {
                        // 邮箱已注册但手机号不匹配
                        var formattedMessage = res.message.replace(/\n/g, '<br>')
                                                         .replace(/(注册手机号：)(\d+\*+\d+)/g, '$1<span class="phone-mask">$2</span>');
                        layer.alert('<div class="error-highlight">' + formattedMessage + '</div>', {
                            icon: 0,
                            title: '注册信息不匹配',
                            btn: ['知道了'],
                            area: ['480px', 'auto'],
                            skin: 'error-dialog'
                        });
                    } else if (res.code === 4001) {
                        // 邮箱已存在
                        var formattedMessage = res.message.replace(/\n/g, '<br>');
                        layer.alert('<div class="error-highlight">' + formattedMessage + '</div>', {
                            icon: 0,
                            title: '邮箱已注册',
                            btn: ['知道了'],
                            area: ['420px', 'auto'],
                            skin: 'error-dialog'
                        });
                    } else if (res.code === 4002) {
                        // 手机号已存在
                        var formattedMessage = res.message.replace(/\n/g, '<br>');
                        layer.alert('<div class="error-highlight">' + formattedMessage + '</div>', {
                            icon: 0,
                            title: '手机号已注册',
                            btn: ['知道了'],
                            area: ['420px', 'auto'],
                            skin: 'error-dialog'
                        });
                    } else if (res.code >= 4000 && res.code < 5000) {
                        // 其他客户端错误
                        var formattedMessage = res.message.replace(/\n/g, '<br>');
                        layer.alert('<div class="error-highlight">' + formattedMessage + '</div>', {
                            icon: 0,
                            title: '提交失败',
                            btn: ['知道了'],
                            area: ['420px', 'auto'],
                            skin: 'error-dialog'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    layer.msg('网络错误', {icon: 2});
                });
            };
        });
    </script>
</body>
</html>
