{{-- 支付方式选择组件 --}}
<div class="payment-selector" id="paymentSelector">
    <div class="payment-header">
        <h3>选择支付方式</h3>
        <p class="payment-amount">支付金额：<span class="amount">¥{{ number_format($amount, 2) }}</span></p>
    </div>

    {{-- 国内支付方式 --}}
    @if($domesticPayments->count() > 0)
    <div class="payment-section">
        <h4 class="section-title">
            <i class="icon-china"></i>
            国内支付方式
        </h4>
        <div class="payment-methods">
            @foreach($domesticPayments as $payment)
            <div class="payment-method" data-code="{{ $payment->code }}" data-type="domestic">
                <input type="radio" name="payment_method" value="{{ $payment->code }}" id="payment_{{ $payment->code }}">
                <label for="payment_{{ $payment->code }}" class="payment-label">
                    <div class="payment-icon">
                        @if($payment->logo && file_exists(public_path($payment->logo)))
                            <img src="{{ $payment->logo }}" alt="{{ $payment->name }}">
                        @else
                            <div class="default-icon">{{ mb_substr($payment->name, 0, 1) }}</div>
                        @endif
                    </div>
                    <div class="payment-info">
                        <div class="payment-name">{{ $payment->name }}</div>
                        <div class="payment-desc">{{ $payment->description }}</div>
                        <div class="payment-features">
                            @if($payment->features)
                                @foreach(json_decode($payment->features, true) as $feature)
                                    <span class="feature-tag">{{ $feature }}</span>
                                @endforeach
                            @endif
                        </div>
                        <div class="payment-fees">
                            <span class="fee-label">手续费:</span>
                            <span class="fee-value">{{ $payment->fees }}</span>
                            <span class="settlement-label">结算:</span>
                            <span class="settlement-value">{{ $payment->settlement_time }}</span>
                        </div>
                    </div>
                    <div class="payment-status">
                        @if($payment->code === 'cod')
                            <span class="status-badge cod">货到付款</span>
                        @elseif($payment->code === 'cash')
                            <span class="status-badge cash">现金支付</span>
                        @elseif($payment->code === 'bank_transfer')
                            <span class="status-badge bank">银行转账</span>
                        @else
                            <span class="status-badge online">在线支付</span>
                        @endif
                    </div>
                </label>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    {{-- 国际支付方式 --}}
    @if($internationalPayments->count() > 0)
    <div class="payment-section">
        <h4 class="section-title">
            <i class="icon-global"></i>
            国际支付方式
        </h4>
        <div class="payment-methods">
            @foreach($internationalPayments as $payment)
            <div class="payment-method" data-code="{{ $payment->code }}" data-type="international">
                <input type="radio" name="payment_method" value="{{ $payment->code }}" id="payment_{{ $payment->code }}">
                <label for="payment_{{ $payment->code }}" class="payment-label">
                    <div class="payment-icon">
                        @if($payment->logo && file_exists(public_path($payment->logo)))
                            <img src="{{ $payment->logo }}" alt="{{ $payment->name }}">
                        @else
                            <div class="default-icon">{{ mb_substr($payment->name, 0, 1) }}</div>
                        @endif
                    </div>
                    <div class="payment-info">
                        <div class="payment-name">{{ $payment->name }}</div>
                        <div class="payment-desc">{{ $payment->description }}</div>
                        <div class="payment-features">
                            @if($payment->features)
                                @foreach(json_decode($payment->features, true) as $feature)
                                    <span class="feature-tag">{{ $feature }}</span>
                                @endforeach
                            @endif
                        </div>
                        <div class="payment-fees">
                            <span class="fee-label">手续费:</span>
                            <span class="fee-value">{{ $payment->fees }}</span>
                            <span class="settlement-label">结算:</span>
                            <span class="settlement-value">{{ $payment->settlement_time }}</span>
                        </div>
                    </div>
                    <div class="payment-status">
                        <span class="status-badge international">国际支付</span>
                    </div>
                </label>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    {{-- 支付按钮 --}}
    <div class="payment-actions">
        <button type="button" class="btn btn-primary btn-pay" onclick="processPayment()" disabled>
            <i class="icon-pay"></i>
            立即支付 ¥{{ number_format($amount, 2) }}
        </button>
        <button type="button" class="btn btn-secondary" onclick="cancelPayment()">
            取消
        </button>
    </div>

    {{-- 支付说明 --}}
    <div class="payment-notice">
        <h5>支付说明：</h5>
        <ul>
            <li>请选择您方便的支付方式完成付款</li>
            <li>支付完成后，系统将自动确认订单状态</li>
            <li>如遇支付问题，请联系客服：400-123-4567</li>
            <li>支付过程中请勿关闭页面或重复提交</li>
        </ul>
    </div>
</div>

<!-- 线下扫码支付弹窗 -->
<div id="offlineQrModal" class="offline-qr-modal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-container">
        <div class="modal-header">
            <h3>选择扫码支付方式</h3>
            <button type="button" class="modal-close" onclick="closeOfflineQrModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="orderInfo" class="order-info"></div>
            <div id="qrPaymentMethods" class="qr-payment-methods"></div>
        </div>
    </div>
</div>

<!-- 二维码显示弹窗 -->
<div id="qrCodeModal" class="qr-code-modal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-container">
        <div class="modal-header">
            <h3 id="qrModalTitle">扫码支付</h3>
            <button type="button" class="modal-close" onclick="closeQrCodeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="qrCodeContent" class="qr-code-content"></div>
            <div class="payment-instructions">
                <div class="important-notice">
                    <h4>⚠️ 重要提示</h4>
                    <ul>
                        <li>扫码支付后，请务必在转账备注中填写：<strong>订单号 + 您的联系方式</strong></li>
                        <li>完成支付后，请联系客服确认到账</li>
                        <li>客服确认后，我们将尽快处理您的订单</li>
                        <li>如有疑问，请保存好支付凭证</li>
                    </ul>
                </div>
                <div class="payment-actions">
                    <button type="button" class="btn btn-success" onclick="confirmOfflinePayment()">
                        确认已支付
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeQrCodeModal()">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.payment-selector {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.payment-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.payment-header h3 {
    margin: 0 0 10px 0;
    font-size: 24px;
}

.payment-amount {
    margin: 0;
    font-size: 16px;
}

.amount {
    font-size: 28px;
    font-weight: bold;
    color: #ffd700;
}

.payment-section {
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.section-title {
    display: flex;
    align-items: center;
    margin: 0 0 15px 0;
    font-size: 18px;
    color: #333;
}

.section-title i {
    margin-right: 8px;
    font-size: 20px;
}

.payment-methods {
    display: grid;
    gap: 12px;
}

.payment-method {
    position: relative;
}

.payment-method input[type="radio"] {
    display: none;
}

.payment-label {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.payment-label:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.payment-method input[type="radio"]:checked + .payment-label {
    border-color: #007bff;
    background: #e7f3ff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.payment-icon {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.payment-icon img {
    max-width: 40px;
    max-height: 40px;
}

.default-icon {
    font-size: 20px;
    font-weight: bold;
    color: #666;
}

.payment-info {
    flex: 1;
}

.payment-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.payment-desc {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.4;
}

.payment-features {
    margin-bottom: 8px;
}

.feature-tag {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    margin-right: 4px;
    margin-bottom: 2px;
}

.payment-fees {
    font-size: 12px;
    color: #666;
}

.fee-label, .settlement-label {
    margin-right: 4px;
}

.fee-value, .settlement-value {
    font-weight: bold;
    color: #333;
    margin-right: 12px;
}

.payment-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
}

.status-badge.online {
    background: #d4edda;
    color: #155724;
}

.status-badge.cod {
    background: #fff3cd;
    color: #856404;
}

.status-badge.cash {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.bank {
    background: #cce5ff;
    color: #004085;
}

.status-badge.international {
    background: #e2e3e5;
    color: #383d41;
}

.payment-actions {
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}

.btn-primary:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.payment-notice {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.payment-notice h5 {
    margin: 0 0 10px 0;
    color: #333;
}

.payment-notice ul {
    margin: 0;
    padding-left: 20px;
    color: #666;
    font-size: 13px;
}

.payment-notice li {
    margin-bottom: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .payment-label {
        flex-direction: column;
        text-align: center;
    }
    
    .payment-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .payment-status {
        margin-top: 10px;
        justify-content: center;
    }
}

/* 线下扫码支付弹窗样式 */
.offline-qr-modal, .qr-code-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
}

.offline-qr-modal .modal-container {
    width: 800px;
}

.qr-code-modal .modal-container {
    width: 500px;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    position: relative;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    line-height: 1;
}

.modal-close:hover {
    opacity: 0.8;
}

.modal-body {
    padding: 30px;
    max-height: 70vh;
    overflow-y: auto;
}

.order-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.order-info h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.order-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.order-amount {
    color: #e74c3c;
    font-size: 1.2em;
    font-weight: bold;
}

.qr-payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.qr-payment-item {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.qr-payment-item:hover {
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
    transform: translateY(-2px);
}

.qr-payment-item.selected {
    border-color: #28a745;
    background: #f8fff9;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.qr-cover-image {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    object-fit: cover;
    margin: 0 auto 15px;
    border: 1px solid #ddd;
}

.qr-payment-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.qr-payment-desc {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 15px;
}

.select-qr-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.select-qr-btn:hover {
    background: #0056b3;
}

.qr-code-content {
    text-align: center;
    margin-bottom: 30px;
}

.qr-code-image {
    width: 250px;
    height: 250px;
    border-radius: 8px;
    border: 1px solid #ddd;
    margin: 0 auto 20px;
}

.important-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.important-notice h4 {
    margin: 0 0 10px 0;
    color: #856404;
}

.important-notice ul {
    margin: 0;
    padding-left: 20px;
    color: #856404;
}

.important-notice li {
    margin-bottom: 8px;
}

.payment-actions {
    text-align: center;
}

.payment-actions .btn {
    margin: 0 10px;
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

@media (max-width: 768px) {
    .offline-qr-modal .modal-container,
    .qr-code-modal .modal-container {
        width: 95vw;
        margin: 20px;
    }

    .qr-payment-methods {
        grid-template-columns: 1fr;
    }

    .qr-cover-image {
        width: 100px;
        height: 100px;
    }

    .qr-code-image {
        width: 200px;
        height: 200px;
    }
}
</style>

<script>
// 支付方式选择逻辑
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const payButton = document.querySelector('.btn-pay');
    
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            if (this.checked) {
                payButton.disabled = false;
                
                // 更新支付按钮文本
                const methodName = this.closest('.payment-method').querySelector('.payment-name').textContent;
                payButton.innerHTML = `<i class="icon-pay"></i> 使用${methodName}支付 ¥{{ number_format($amount, 2) }}`;
            }
        });
    });
});

// 处理支付
function processPayment() {
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
    
    if (!selectedMethod) {
        alert('请选择支付方式');
        return;
    }
    
    const paymentCode = selectedMethod.value;
    const amount = {{ $amount }};
    const orderId = '{{ $orderId ?? '' }}';
    
    // 显示加载状态
    const payButton = document.querySelector('.btn-pay');
    const originalText = payButton.innerHTML;
    payButton.innerHTML = '<i class="icon-loading"></i> 正在跳转...';
    payButton.disabled = true;
    
    // 发送支付请求
    fetch('/payment/pay?orderId=' + orderId + '&paycode=' + paymentCode, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.type === 'offline_qr_modal') {
                // 显示线下扫码支付弹窗
                showOfflineQrModal(data.data);
                payButton.innerHTML = originalText;
                payButton.disabled = false;
            } else if (data.redirect_url) {
                // 跳转到支付页面
                window.location.href = data.redirect_url;
            } else if (data.payment_url) {
                // 打开新窗口进行支付
                window.open(data.payment_url, '_blank');
            } else {
                // 显示支付信息（如银行转账信息）
                showPaymentInfo(data);
                payButton.innerHTML = originalText;
                payButton.disabled = false;
            }
        } else {
            alert('支付创建失败：' + data.message);
            payButton.innerHTML = originalText;
            payButton.disabled = false;
        }
    })
    .catch(error => {
        console.error('支付请求失败:', error);
        alert('支付请求失败，请重试');
        payButton.innerHTML = originalText;
        payButton.disabled = false;
    });
}

// 显示支付信息
function showPaymentInfo(data) {
    // 这里可以显示银行转账信息、现金支付地址等
    alert('支付信息：\n' + JSON.stringify(data, null, 2));
}

// 取消支付
function cancelPayment() {
    if (confirm('确定要取消支付吗？')) {
        window.history.back();
    }
}

// 线下扫码支付弹窗相关函数
let currentOrder = null;
let selectedQrPaymentId = null;

function showOfflineQrModal(data) {
    currentOrder = data.order;
    const qrPayments = data.qrPayments;

    // 填充订单信息
    const orderInfoHtml = `
        <h4>📋 订单信息</h4>
        <div class="order-detail">
            <span>订单号：</span>
            <strong>${currentOrder.order_no}</strong>
        </div>
        <div class="order-detail">
            <span>订单金额：</span>
            <span class="order-amount">${currentOrder.currency_code} ${parseFloat(currentOrder.order_amount).toFixed(2)}</span>
        </div>
        <div class="order-detail">
            <span>联系邮箱：</span>
            <span>${currentOrder.email}</span>
        </div>
    `;
    document.getElementById('orderInfo').innerHTML = orderInfoHtml;

    // 填充支付方式
    let qrMethodsHtml = '';
    qrPayments.forEach(payment => {
        const coverImage = payment.cover_image ?
            `<img src="${payment.cover_image}" class="qr-cover-image" alt="${payment.name}">` :
            `<div class="qr-cover-image" style="background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                <i class="fas fa-image" style="font-size: 2rem; color: #ccc;"></i>
            </div>`;

        qrMethodsHtml += `
            <div class="qr-payment-item" data-id="${payment.id}" data-qr-image="${payment.qr_image || ''}" onclick="selectQrPayment(this)">
                ${coverImage}
                <div class="qr-payment-name">${payment.name}</div>
                <div class="qr-payment-desc">${payment.description || ''}</div>
                <button type="button" class="select-qr-btn">选择此方式</button>
            </div>
        `;
    });
    document.getElementById('qrPaymentMethods').innerHTML = qrMethodsHtml;

    // 显示弹窗
    document.getElementById('offlineQrModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function selectQrPayment(element) {
    // 移除其他选中状态
    document.querySelectorAll('.qr-payment-item').forEach(item => {
        item.classList.remove('selected');
    });

    // 添加选中状态
    element.classList.add('selected');
    selectedQrPaymentId = element.dataset.id;

    // 显示二维码弹窗
    const paymentName = element.querySelector('.qr-payment-name').textContent;
    const qrImageUrl = element.dataset.qrImage;

    showQrCodeModal(paymentName, qrImageUrl);
}

function showQrCodeModal(paymentName, qrImageUrl) {
    document.getElementById('qrModalTitle').textContent = paymentName;

    let qrContentHtml = `<h4>${paymentName}</h4>`;
    if (qrImageUrl) {
        qrContentHtml += `<img src="${qrImageUrl}" class="qr-code-image" alt="${paymentName}二维码">`;
        qrContentHtml += `<p style="color: #666;">请扫描上方二维码完成支付</p>`;
    } else {
        qrContentHtml += `
            <div class="qr-code-image" style="background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                <i class="fas fa-qrcode" style="font-size: 3rem; color: #ccc;"></i>
            </div>
            <p style="color: #999;">二维码暂未上传</p>
        `;
    }

    document.getElementById('qrCodeContent').innerHTML = qrContentHtml;
    document.getElementById('qrCodeModal').style.display = 'block';
}

function closeOfflineQrModal() {
    document.getElementById('offlineQrModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    selectedQrPaymentId = null;
}

function closeQrCodeModal() {
    document.getElementById('qrCodeModal').style.display = 'none';
    // 移除选中状态
    document.querySelectorAll('.qr-payment-item').forEach(item => {
        item.classList.remove('selected');
    });
    selectedQrPaymentId = null;
}

function confirmOfflinePayment() {
    if (!selectedQrPaymentId || !currentOrder) {
        alert('请选择支付方式');
        return;
    }

    // 发送确认请求
    fetch('/payment/offline-qr/confirm', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            order_id: currentOrder.id,
            qr_payment_id: selectedQrPaymentId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            closeQrCodeModal();
            closeOfflineQrModal();
            if (data.redirect) {
                window.location.href = data.redirect;
            } else {
                // 刷新页面或跳转到订单查询页面
                window.location.reload();
            }
        } else {
            alert('确认失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('网络错误，请稍后重试');
    });
}

// 点击弹窗外部关闭
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal-overlay')) {
        if (e.target.closest('#offlineQrModal')) {
            closeOfflineQrModal();
        } else if (e.target.closest('#qrCodeModal')) {
            closeQrCodeModal();
        }
    }
});
</script>
