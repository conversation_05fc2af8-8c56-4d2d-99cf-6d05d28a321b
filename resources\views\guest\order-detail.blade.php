@extends('themes.default.layouts.app')

@section('title', '订单详情')

@section('content')
<style>
.order-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 40px 0;
    font-size: 14px;
}

.order-wrapper {
    max-width: 800px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.order-header {
    background: #000000;
    color: #ffffff;
    padding: 32px;
    text-align: center;
}

.order-header h1 {
    font-size: 1.5rem;
    font-weight: 300;
    margin: 0;
    letter-spacing: 0.5px;
}

.order-header .subtitle {
    font-size: 14px;
    opacity: 0.8;
    margin-top: 8px;
    font-weight: 300;
}

.order-content {
    padding: 32px;
}

.order-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 24px;
    margin-bottom: 24px;
}

.section-title {
    color: #000000;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 8px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    padding: 8px 0;
    align-items: flex-start;
}

.info-item .label {
    font-weight: 500;
    color: #000000;
    width: 100px;
    min-width: 100px;
    padding-right: 12px;
    font-size: 14px;
    flex-shrink: 0;
}

.info-item .value {
    color: #495057;
    font-size: 14px;
    flex: 1;
    line-height: 1.4;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.status-10 {
    background: #fff3cd;
    color: #856404;
}

.status-12 {
    background: #d4edda;
    color: #155724;
}

.status-13, .status-14 {
    background: #f8d7da;
    color: #721c24;
}

.status-20 {
    background: #cce5ff;
    color: #004085;
}

.status-22 {
    background: #d1ecf1;
    color: #0c5460;
}

.status-24 {
    background: #fff3cd;
    color: #856404;
}

.status-26 {
    background: #cce5ff;
    color: #004085;
}

.status-30 {
    background: #d4edda;
    color: #155724;
}

.status-40, .status-42 {
    background: #e2e3e5;
    color: #383d41;
}

.product-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.product-item {
    display: flex;
    padding: 16px 0;
    border-bottom: 1px solid #e9ecef;
    align-items: center;
}

.product-item:last-child {
    border-bottom: none;
}

.product-image {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    object-fit: cover;
    margin-right: 16px;
}

.product-info {
    flex: 1;
}

.product-title {
    font-weight: 500;
    color: #000000;
    margin-bottom: 4px;
}

.product-specs {
    color: #6c757d;
    font-size: 12px;
    margin-bottom: 4px;
}

.product-price {
    color: #000000;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 32px;
}

.btn-modern {
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #000000;
    color: #ffffff;
    border-color: #000000;
}

.btn-primary:hover {
    background: #333333;
    border-color: #333333;
    color: #ffffff;
    text-decoration: none;
}

.btn-outline {
    background: #ffffff;
    color: #000000;
    border-color: #e9ecef;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #000000;
    color: #000000;
    text-decoration: none;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 24px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-dot {
    position: absolute;
    left: -23px;
    top: 4px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e9ecef;
    border: 2px solid #ffffff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-item.completed .timeline-dot {
    background: #000000;
    box-shadow: 0 0 0 2px #000000;
}

.timeline-content {
    padding-left: 8px;
}

.timeline-title {
    font-weight: 500;
    color: #000000;
    margin-bottom: 4px;
    font-size: 14px;
}

.timeline-time {
    color: #6c757d;
    font-size: 12px;
    margin-bottom: 2px;
}

.timeline-desc {
    color: #495057;
    font-size: 12px;
}

@media (max-width: 768px) {
    .order-container {
        padding: 20px 0;
    }
    
    .order-content {
        padding: 24px;
    }
    
    .order-section {
        padding: 20px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .product-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .product-image {
        margin-bottom: 12px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>

<div class="order-container">
    <div class="container">
        <div class="order-wrapper">
            <div class="order-header">
                <h1><i class="fas fa-receipt"></i> 订单详情</h1>
                <div class="subtitle">订单号：{{ $order->order_no }}</div>
            </div>
            
            <div class="order-content">
                <!-- 订单基本信息 -->
                <div class="order-section">
                    <div class="section-title">
                        <i class="fas fa-info-circle"></i> 订单信息
                    </div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="label">订单状态</div>
                            <div class="value">
                                <span class="status-badge status-{{ $order->order_status }}">
                                    {{ $order->order_status_label }}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="label">订单金额</div>
                            <div class="value">{{ $order->currency_code }} {{ number_format($order->order_amount, 2) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="label">下单时间</div>
                            <div class="value">{{ $order->created_at }}</div>
                        </div>
                        <div class="info-item">
                            <div class="label">支付方式</div>
                            <div class="value">{{ $order->paymentOption->name ?? '未知' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="label">配送方式</div>
                            <div class="value">{{ $order->shippingOption->title ?? '未知' }}</div>
                        </div>
                        @if($order->tracking_no)
                        <div class="info-item">
                            <div class="label">物流单号</div>
                            <div class="value">{{ $order->tracking_no }}</div>
                        </div>
                        @endif
                        @if($order->shipped_at)
                        <div class="info-item">
                            <div class="label">发货时间</div>
                            <div class="value">{{ $order->shipped_at }}</div>
                        </div>
                        @endif
                        @if($order->shipping_remark)
                        <div class="info-item">
                            <div class="label">配送备注</div>
                            <div class="value">{{ $order->shipping_remark }}</div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- 商品信息 -->
                <div class="order-section">
                    <div class="section-title">
                        <i class="fas fa-shopping-cart"></i> 商品信息
                    </div>
                    <div class="product-list">
                        @foreach($order->orderProducts as $product)
                        <div class="product-item">
                            @if($product->img_cover)
                                <img src="{{ $product->img_cover }}" alt="{{ $product->title }}" class="product-image">
                            @else
                                <div class="product-image" style="background: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-image" style="color: #ccc;"></i>
                                </div>
                            @endif
                            <div class="product-info">
                                <div class="product-title">{{ $product->title }}</div>
                                @if($product->specs)
                                    <div class="product-specs">规格：{{ $product->specs }}</div>
                                @endif
                                <div class="product-price">
                                    {{ $order->currency_code }} {{ number_format($product->sale_price, 2) }} × {{ $product->qty }}
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- 订单时间线 -->
                @if($order->order_status >= 12)
                <div class="order-section">
                    <div class="section-title">
                        <i class="fas fa-history"></i> 订单跟踪
                    </div>
                    <div class="timeline">
                        <div class="timeline-item {{ $order->order_status >= 10 ? 'completed' : '' }}">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">订单创建</div>
                                <div class="timeline-time">{{ $order->created_at }}</div>
                            </div>
                        </div>

                        @if($order->paid_at)
                        <div class="timeline-item {{ $order->order_status >= 12 ? 'completed' : '' }}">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">支付完成</div>
                                <div class="timeline-time">{{ $order->paid_at }}</div>
                            </div>
                        </div>
                        @endif

                        @if($order->shipped_at)
                        <div class="timeline-item {{ $order->order_status >= 20 ? 'completed' : '' }}">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">订单发货</div>
                                <div class="timeline-time">{{ $order->shipped_at }}</div>
                                @if($order->tracking_no)
                                    <div class="timeline-desc">物流单号：{{ $order->tracking_no }}</div>
                                @endif
                            </div>
                        </div>
                        @endif

                        @if($order->received_at)
                        <div class="timeline-item {{ $order->order_status >= 22 ? 'completed' : '' }}">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">确认收货</div>
                                <div class="timeline-time">{{ $order->received_at }}</div>
                            </div>
                        </div>
                        @endif

                        @if($order->order_status == 30)
                        <div class="timeline-item completed">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">订单完成</div>
                                <div class="timeline-time">{{ $order->updated_at }}</div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- 收货信息 -->
                <div class="order-section">
                    <div class="section-title">
                        <i class="fas fa-map-marker-alt"></i> 收货信息
                    </div>
                    <div class="info-item">
                        <div class="label">收货人</div>
                        <div class="value">{{ $order->first_name }} {{ $order->last_name }}</div>
                    </div>
                    <div class="info-item">
                        <div class="label">联系电话</div>
                        <div class="value">{{ $order->phone }}</div>
                    </div>
                    <div class="info-item">
                        <div class="label">邮箱地址</div>
                        <div class="value">{{ $order->email }}</div>
                    </div>
                    <div class="info-item">
                        <div class="label">收货地址</div>
                        <div class="value">
                            {{ $order->address_line_1 }}
                            @if($order->address_line_2), {{ $order->address_line_2 }}@endif
                            <br>{{ $order->city }}, {{ $order->state }} {{ $order->postal_code }}
                            <br>{{ $order->country }}
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <a href="{{ route('guest.order.lookup') }}" class="btn-modern btn-outline">
                        <i class="fas fa-search"></i> 查询其他订单
                    </a>
                    <a href="{{ url('/') }}" class="btn-modern btn-primary">
                        <i class="fas fa-home"></i> 返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
