@extends('themes.default.layouts.app')

@section('title', '订单查询')

@section('content')
<style>
.lookup-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 40px 0;
    font-size: 14px;
}

.lookup-wrapper {
    max-width: 500px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.lookup-header {
    background: #000000;
    color: #ffffff;
    padding: 32px;
    text-align: center;
}

.lookup-header h1 {
    font-size: 1.5rem;
    font-weight: 300;
    margin: 0;
    letter-spacing: 0.5px;
}

.lookup-header .subtitle {
    font-size: 14px;
    opacity: 0.8;
    margin-top: 8px;
    font-weight: 300;
}

.lookup-content {
    padding: 32px;
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    font-weight: 500;
    color: #000000;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: #ffffff;
}

.form-control:focus {
    outline: none;
    border-color: #000000;
    box-shadow: 0 0 0 2px rgba(0,0,0,0.1);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
}

.btn-submit {
    width: 100%;
    background: #000000;
    color: #ffffff;
    border: none;
    padding: 14px 24px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-submit:hover {
    background: #333333;
    color: #ffffff;
}

.error-alert {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 24px;
    color: #c53030;
    font-size: 14px;
}

.divider {
    height: 1px;
    background: #e9ecef;
    margin: 32px 0;
}

.other-actions {
    text-align: center;
}

.other-actions .title {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 16px;
}

.action-links {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.action-link {
    padding: 8px 16px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    color: #495057;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.action-link:hover {
    border-color: #000000;
    color: #000000;
    text-decoration: none;
}

@media (max-width: 768px) {
    .lookup-container {
        padding: 20px 0;
    }

    .lookup-content {
        padding: 24px;
    }

    .lookup-header {
        padding: 24px;
    }

    .action-links {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<div class="lookup-container">
    <div class="container">
        <div class="lookup-wrapper">
            <div class="lookup-header">
                <h1><i class="fas fa-search"></i> 订单查询</h1>
                <div class="subtitle">请输入您的订单号和邮箱地址</div>
            </div>

            <div class="lookup-content">
                @if ($errors->any())
                    <div class="error-alert">
                        @foreach ($errors->all() as $error)
                            <div>{{ $error }}</div>
                        @endforeach
                    </div>
                @endif

                <form method="POST" action="{{ route('guest.order.lookup') }}">
                    @csrf
                    <div class="form-group">
                        <label for="order_no" class="form-label">订单号</label>
                        <input type="text"
                               class="form-control @error('order_no') is-invalid @enderror"
                               id="order_no"
                               name="order_no"
                               value="{{ old('order_no', request('order_no')) }}"
                               placeholder="请输入订单号，例如：ST20250731153697251"
                               required>
                        @error('order_no')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">邮箱地址</label>
                        <input type="email"
                               class="form-control @error('email') is-invalid @enderror"
                               id="email"
                               name="email"
                               value="{{ old('email', request('email')) }}"
                               placeholder="请输入下单时使用的邮箱地址"
                               required>
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <button type="submit" class="btn-submit">
                        <i class="fas fa-search"></i> 查询订单
                    </button>
                </form>

                <div class="divider"></div>

                <div class="other-actions">
                    <div class="title">其他操作</div>
                    <div class="action-links">
                        <a href="{{ url('/') }}" class="action-link">
                            <i class="fas fa-home"></i> 返回首页
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
