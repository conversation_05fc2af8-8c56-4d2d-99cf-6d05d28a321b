@extends('themes.default.layouts.app')

@section('title', '银行转账支付')

@section('head')
<!-- 确保FontAwesome图标库加载 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
@endsection

@section('content')
<style>
.payment-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
    font-size: 14px;
}

.payment-wrapper {
    max-width: 1000px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.payment-header {
    background: #000000;
    color: #ffffff;
    padding: 24px;
    text-align: center;
}

.payment-header h1 {
    font-size: 1.5rem;
    font-weight: 300;
    margin: 0;
    letter-spacing: 0.5px;
}

.payment-header .subtitle {
    font-size: 14px;
    opacity: 0.8;
    margin-top: 6px;
    font-weight: 300;
}

.payment-content {
    padding: 24px;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 20px;
}

.content-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.section-title {
    color: #000000;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 8px;
}

.order-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.order-no {
    font-size: 16px;
    font-weight: 600;
    color: #000000;
}

.order-amount {
    font-size: 18px;
    font-weight: 700;
    color: #000000;
}

.order-date {
    color: #6c757d;
    font-size: 12px;
    margin-top: 4px;
}

.copy-btn {
    background: #000000;
    color: #ffffff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.copy-btn:hover {
    background: #333333;
    color: #ffffff;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-item {
    display: flex;
    padding: 8px 0;
    align-items: flex-start;
}

.info-item .label {
    font-weight: 500;
    color: #000000;
    width: 80px;
    min-width: 80px;
    padding-right: 12px;
    font-size: 14px;
    flex-shrink: 0;
}

.info-item .value {
    color: #495057;
    word-break: break-all;
    font-size: 14px;
    flex: 1;
    line-height: 1.4;
}



.action-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 24px;
}

.btn-modern {
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: center;
    min-width: 120px;
}

.btn-primary {
    background: #000000;
    color: #ffffff;
    border-color: #000000;
}

.btn-primary:hover {
    background: #333333;
    border-color: #333333;
    color: #ffffff;
    text-decoration: none;
}

.btn-outline {
    background: #ffffff;
    color: #000000;
    border-color: #e9ecef;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #000000;
    color: #000000;
    text-decoration: none;
}

@media (max-width: 768px) {
    .payment-container {
        padding: 20px 0;
    }

    .payment-content {
        padding: 20px;
    }

    .main-info, .bank-details, .instructions, .contact-section {
        padding: 20px;
    }

    .order-summary {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .bank-info-table .label {
        width: auto;
        min-width: auto;
        display: block;
        margin-bottom: 4px;
    }

    .bank-info-table td {
        display: block;
        padding: 8px 0;
    }

    .contact-list {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }
}
</style>

<div class="payment-container">
    <div class="container">
        <div class="payment-wrapper">
            <div class="payment-header">
                <h1><i class="fas fa-university"></i> 银行转账支付</h1>
                <div class="subtitle">请按照以下信息完成转账</div>
            </div>

            <div class="payment-content">
                <!-- 自动注册账户信息 -->
                @if(session('auto_registered_account'))
                    @php
                        $accountInfo = session('auto_registered_account');
                        // 显示后清除session，避免重复显示
                        session()->forget('auto_registered_account');
                    @endphp
                    <div class="content-section" style="margin-bottom: 20px; background: #f0f9ff; border-left: 4px solid #0ea5e9;">
                        <div class="section-title" style="color: #0ea5e9;">
                            <i class="fas fa-user-plus"></i> {{ $accountInfo['message'] }}
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                            <div style="display: flex; align-items: center;">
                                <span style="font-weight: 500; margin-right: 8px;">账户邮箱：</span>
                                <span>{{ $accountInfo['email'] }}</span>
                                <button type="button" class="copy-btn" onclick="copyText('{{ $accountInfo['email'] }}', '邮箱')" style="margin-left: 8px;">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                            @if($accountInfo['type'] === 'new' && isset($accountInfo['password']))
                            <div style="display: flex; align-items: center;">
                                <span style="font-weight: 500; margin-right: 8px;">登录密码：</span>
                                <span id="password-display" style="font-family: monospace; background: #e5e7eb; padding: 2px 6px; border-radius: 3px;">{{ $accountInfo['password'] }}</span>
                                <button type="button" class="copy-btn" onclick="copyText('{{ $accountInfo['password'] }}', '密码')" style="margin-left: 8px;">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                            @endif
                        </div>
                        <div style="margin-top: 12px; padding: 12px; background: rgba(14, 165, 233, 0.1); border-radius: 6px; font-size: 13px; color: #0369a1;">
                            @if($accountInfo['type'] === 'new')
                            <div style="margin-bottom: 6px;">
                                <i class="fas fa-info-circle"></i>
                                请保存好您的账户信息，您可以使用此邮箱和密码登录查看订单状态。
                            </div>
                            @else
                            <div style="margin-bottom: 6px;">
                                <i class="fas fa-info-circle"></i>
                                您的订单已关联到现有账户，可以登录查看订单状态。
                            </div>
                            @endif
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <i class="fas fa-key" style="font-size: 12px;"></i>
                                <span>如果忘记密码，可以通过</span>
                                <a href="{{ route('password.request') }}" target="_blank" style="color: #0369a1; text-decoration: underline; font-weight: 500;">邮箱找回密码</a>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- 订单信息 -->
                <div class="content-section" style="margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <div>
                            <span class="order-no">订单号：{{ $order->order_no ?? 'N/A' }}</span>
                            <button type="button" class="copy-btn" onclick="copyOrderNumber()" style="margin-left: 12px;">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                        <div class="order-amount">{{ $order->currency_code ?? 'USD' }} {{ number_format($order->order_amount ?? 0, 2) }}</div>
                    </div>
                    @if(!empty($order->email))
                    <div style="display: flex; align-items: center; color: #6c757d; font-size: 14px;">
                        <span>下单邮箱：{{ $order->email }}</span>
                        <button type="button" class="copy-btn" onclick="copyEmail()" style="margin-left: 12px;">
                            <i class="fas fa-copy"></i> 复制
                        </button>
                    </div>
                    @endif
                </div>

                <!-- 主要内容区域 -->
                <div class="content-grid">

                    <!-- 左列：银行账户信息 -->
                    <div class="content-section">
                        <div class="section-title">
                            <i class="fas fa-university"></i> 银行账户信息
                        </div>
                        @if(isset($bankInfo) && !empty($bankInfo))
                            <div class="info-list">
                                <div class="info-item">
                                    <div class="label">银行名称</div>
                                    <div class="value">{{ $bankInfo['bank_name'] ?? '请联系客服获取' }}</div>
                                </div>
                                <div class="info-item">
                                    <div class="label">账户名称</div>
                                    <div class="value">{{ $bankInfo['account_name'] ?? '请联系客服获取' }}</div>
                                </div>
                                <div class="info-item">
                                    <div class="label">账户号码</div>
                                    <div class="value">{{ $bankInfo['account_number'] ?? '请联系客服获取' }}</div>
                                </div>
                                @if(!empty($bankInfo['bank_address']))
                                <div class="info-item">
                                    <div class="label">开户行</div>
                                    <div class="value">{{ $bankInfo['bank_address'] }}</div>
                                </div>
                                @endif
                                @if(!empty($bankInfo['swift_code']))
                                <div class="info-item">
                                    <div class="label">SWIFT</div>
                                    <div class="value">{{ $bankInfo['swift_code'] }}</div>
                                </div>
                                @endif
                            </div>
                        @else
                            <p style="color: #6c757d; margin: 0;">银行账户信息暂未配置，请联系客服获取。</p>
                        @endif
                    </div>

                <!-- 转账步骤 -->
                <div class="content-section">
                    <div class="section-title"><i class="fas fa-list-ol"></i> 转账步骤</div>
                    <ol style="font-size: 14px; line-height: 1.6; padding-left: 20px;">
                        <li style="margin-bottom: 8px;">使用网银、手机银行或到银行柜台进行转账</li>
                        <li style="margin-bottom: 8px;"><strong style="color: #c50000;">重要：</strong>在转账备注中填写订单号：<span style="background: #fff2f2; color: #c50000; padding: 2px 6px; border-radius: 3px; font-weight: 600;">{{ $order->order_no ?? 'N/A' }}</span></li>
                        <li style="margin-bottom: 8px;">或在备注中填写您的联系方式（邮箱或电话）</li>
                        <li style="margin-bottom: 8px;">完成转账后，请联系客服确认付款</li>
                        <li style="margin-bottom: 8px;">客服确认后，我们将尽快处理您的订单</li>
                    </ol>

                    @if(isset($bankInfo['transfer_instructions']) && !empty($bankInfo['transfer_instructions']))
                        <div style="margin-top: 16px; padding: 12px; background: #fff2f2; border-left: 4px solid #c50000; border-radius: 4px;">
                            <strong style="color: #c50000;">特别说明：</strong>
                            <span style="color: #c50000;">{{ $bankInfo['transfer_instructions'] }}</span>
                        </div>
                    @endif
                </div>

                <!-- 联系客服 -->
                @if(!empty($bankInfo['contact_email']) || !empty($bankInfo['contact_phone']) || !empty($bankInfo['contact_wechat']) || !empty($bankInfo['contact_qq']))
                <div class="content-section">
                    <div class="section-title"><i class="fas fa-headset"></i> 联系客服</div>
                    <div style="color: #6c757d; font-size: 14px; margin-bottom: 16px;">转账完成后，请通过以下方式联系我们确认</div>

                    <div class="info-list">
                        @if(!empty($bankInfo['contact_email']))
                            <div class="info-item">
                                <div class="label">
                                    <i class="fas fa-envelope" style="margin-right: 6px;"></i>邮箱
                                </div>
                                <div class="value">{{ $bankInfo['contact_email'] }}</div>
                            </div>
                        @endif

                        @if(!empty($bankInfo['contact_phone']))
                            <div class="info-item">
                                <div class="label">
                                    <i class="fas fa-phone" style="margin-right: 6px;"></i>电话
                                </div>
                                <div class="value">{{ $bankInfo['contact_phone'] }}</div>
                            </div>
                        @endif

                        @if(!empty($bankInfo['contact_wechat']))
                            <div class="info-item">
                                <div class="label">
                                    <i class="fab fa-weixin" style="margin-right: 6px;"></i>微信
                                </div>
                                <div class="value">{{ $bankInfo['contact_wechat'] }}</div>
                            </div>
                        @endif

                        @if(!empty($bankInfo['contact_qq']))
                            <div class="info-item">
                                <div class="label">
                                    <i class="fab fa-qq" style="margin-right: 6px;"></i>QQ
                                </div>
                                <div class="value">{{ $bankInfo['contact_qq'] }}</div>
                            </div>
                        @endif

                        <div class="info-item">
                            <div class="label">
                                <i class="fas fa-clock" style="margin-right: 6px;"></i>服务时间
                            </div>
                            <div class="value">{{ $bankInfo['service_hours'] ?? '工作时间 9:00-18:00' }}</div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <a href="{{ url('/') }}" class="btn-modern btn-outline">
                        <i class="fas fa-home"></i> 返回首页
                    </a>
                    @if(!empty($order->order_no) && !empty($order->email))
                        <a href="{{ route('guest.order.lookup') }}?order_no={{ $order->order_no }}&email={{ $order->email }}&auto=1" class="btn-modern btn-primary">
                            <i class="fas fa-eye"></i> 查看订单
                        </a>
                    @else
                        <a href="{{ route('guest.order.lookup') }}" class="btn-modern btn-primary">
                            <i class="fas fa-search"></i> 查询订单
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<script>
function copyOrderNumber() {
    const orderNo = '{{ $order->order_no ?? "" }}';
    if (orderNo) {
        navigator.clipboard.writeText(orderNo).then(function() {
            showNotification('订单号已复制：' + orderNo);
        }).catch(function() {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = orderNo;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showNotification('订单号已复制：' + orderNo);
        });
    }
}

function copyEmail() {
    const email = '{{ $order->email ?? "" }}';
    if (email) {
        copyText(email, '邮箱');
    }
}

function copyText(text, type) {
    if (text) {
        navigator.clipboard.writeText(text).then(function() {
            showNotification(type + '已复制：' + text);
        }).catch(function() {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showNotification(type + '已复制：' + text);
        });
    }
}

function showNotification(message) {
    // 创建简洁的通知
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #000000;
        color: #ffffff;
        padding: 12px 20px;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-size: 0.9rem;
        transform: translateX(100%);
        transition: transform 0.2s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 200);
    }, 2000);
}
</script>
@endsection
