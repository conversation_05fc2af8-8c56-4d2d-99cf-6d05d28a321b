<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>线下支付 - {{ $config['payment_name'] ?? '线下支付' }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .payment-container {
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .payment-header {
            background: {{ $currentPayment['color'] }};
            color: white;
            padding: 30px;
            text-align: center;
        }

        .payment-header h1 {
            font-size: 1.8rem;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .payment-header .amount {
            font-size: 2.5rem;
            font-weight: 600;
            margin: 15px 0;
        }

        .payment-header .order-info {
            opacity: 0.9;
            font-size: 14px;
        }

        .payment-content {
            padding: 40px;
        }

        .payment-methods {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .payment-method {
            flex: 1;
            min-width: 120px;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #374151;
        }

        .payment-method:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .payment-method.active {
            border-color: {{ $currentPayment['color'] }};
            background: {{ $currentPayment['color'] }}10;
            color: {{ $currentPayment['color'] }};
        }

        .payment-method i {
            font-size: 1.5rem;
            margin-bottom: 8px;
            display: block;
        }

        .payment-method .name {
            font-size: 12px;
            font-weight: 500;
        }

        .qr-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9fafb;
        }

        .qr-code img {
            max-width: 180px;
            max-height: 180px;
            border-radius: 8px;
        }

        .qr-placeholder {
            color: #9ca3af;
            text-align: center;
        }

        .account-info {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .account-info h3 {
            color: #374151;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #6b7280;
            font-weight: 500;
        }

        .info-value {
            color: #374151;
            font-weight: 600;
        }

        .instructions {
            background: #fff7ed;
            border-left: 4px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .instructions h3 {
            color: #92400e;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .instructions ol {
            color: #92400e;
            padding-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .important-note {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .important-note h3 {
            color: #dc2626;
            margin-bottom: 10px;
        }

        .important-note p {
            color: #dc2626;
            font-weight: 500;
        }

        .copy-info {
            background: #f0f9ff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .copy-info h3 {
            color: #0369a1;
            margin-bottom: 15px;
        }

        .copy-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .copy-text {
            flex: 1;
            padding: 8px 12px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            font-family: monospace;
        }

        .copy-btn {
            padding: 8px 16px;
            background: #0369a1;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #0284c7;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            border: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: {{ $currentPayment['color'] }};
            color: white;
        }

        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        @media (max-width: 768px) {
            .payment-content {
                padding: 20px;
            }
            
            .payment-methods {
                flex-direction: column;
            }
            
            .payment-method {
                min-width: auto;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <!-- 支付头部 -->
        <div class="payment-header">
            <h1><i class="{{ $currentPayment['icon'] }}"></i> {{ $currentPayment['name'] }}</h1>
            <div class="amount">{{ $order->currency_code }} {{ number_format($order->order_amount, 2) }}</div>
            <div class="order-info">订单号：{{ $order->order_no }}</div>
        </div>

        <div class="payment-content">
            <!-- 支付方式选择 -->
            <div class="payment-methods">
                @foreach($offlinePayments as $code => $payment)
                <a href="{{ route('payment.offline', ['orderId' => $order->id]) }}?paycode={{ $code }}" 
                   class="payment-method {{ $paycode === $code ? 'active' : '' }}">
                    <i class="{{ $payment['icon'] }}"></i>
                    <div class="name">{{ $payment['name'] }}</div>
                </a>
                @endforeach
            </div>

            <!-- 二维码区域 -->
            <div class="qr-section">
                <h3>扫码支付</h3>
                <div class="qr-code">
                    @if(file_exists(public_path($currentPayment['qr_code'])))
                        <img src="{{ $currentPayment['qr_code'] }}" alt="{{ $currentPayment['name'] }}二维码">
                    @else
                        <div class="qr-placeholder">
                            <i class="fas fa-qrcode" style="font-size: 3rem; margin-bottom: 10px;"></i>
                            <div>请上传{{ $currentPayment['name'] }}二维码</div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- 账户信息 -->
            <div class="account-info">
                <h3><i class="fas fa-user-circle"></i> 收款账户信息</h3>
                @foreach($currentPayment['account_info'] as $label => $value)
                <div class="info-item">
                    <span class="info-label">{{ $label }}：</span>
                    <span class="info-value">{{ $value }}</span>
                </div>
                @endforeach
            </div>

            <!-- 重要提示 -->
            <div class="important-note">
                <h3><i class="fas fa-exclamation-triangle"></i> 重要提示</h3>
                <p>转账时请务必在备注中填写：<strong>订单号 + 您的联系方式</strong>，以便客服快速确认您的付款！</p>
            </div>

            <!-- 复制信息 -->
            <div class="copy-info">
                <h3><i class="fas fa-copy"></i> 一键复制信息</h3>
                <div class="copy-item">
                    <span style="width: 80px;">订单号：</span>
                    <input type="text" class="copy-text" value="{{ $order->order_no }}" readonly>
                    <button class="copy-btn" onclick="copyText('{{ $order->order_no }}', '订单号')">复制</button>
                </div>
                <div class="copy-item">
                    <span style="width: 80px;">联系邮箱：</span>
                    <input type="text" class="copy-text" value="{{ $order->email }}" readonly>
                    <button class="copy-btn" onclick="copyText('{{ $order->email }}', '邮箱')">复制</button>
                </div>
                @if($order->phone)
                <div class="copy-item">
                    <span style="width: 80px;">联系电话：</span>
                    <input type="text" class="copy-text" value="{{ $order->phone }}" readonly>
                    <button class="copy-btn" onclick="copyText('{{ $order->phone }}', '电话')">复制</button>
                </div>
                @endif
                <div class="copy-item">
                    <span style="width: 80px;">备注内容：</span>
                    <input type="text" class="copy-text" value="订单号：{{ $order->order_no }} 联系方式：{{ $order->email }}" readonly>
                    <button class="copy-btn" onclick="copyText('订单号：{{ $order->order_no }} 联系方式：{{ $order->email }}', '备注内容')">复制</button>
                </div>
            </div>

            <!-- 支付说明 -->
            <div class="instructions">
                <h3><i class="fas fa-list-ol"></i> 支付步骤</h3>
                <ol>
                    @foreach($currentPayment['instructions'] as $instruction)
                    <li>{{ $instruction }}</li>
                    @endforeach
                </ol>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="confirmPayment()">
                    <i class="fas fa-check"></i> 我已完成支付
                </button>
                <a href="{{ url('/') }}" class="btn btn-secondary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
            </div>
        </div>
    </div>

    <script>
        function copyText(text, type) {
            navigator.clipboard.writeText(text).then(function() {
                showNotification(type + '已复制：' + text);
            }).catch(function() {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification(type + '已复制：' + text);
            });
        }

        function showNotification(message) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-size: 14px;
                max-width: 300px;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            // 3秒后移除
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        function confirmPayment() {
            if (confirm('请确认您已完成支付并在备注中填写了订单号和联系方式？')) {
                fetch(`/payment/offline/{{ $order->id }}/confirm`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        alert('支付确认已提交！\n\n客服联系方式：\nQQ：' + data.data.contact_info.qq + '\n微信：' + data.data.contact_info.wechat + '\n电话：' + data.data.contact_info.phone + '\n\n请联系客服确认到账，我们会尽快处理您的订单。');
                    } else {
                        alert('提交失败：' + data.message);
                    }
                })
                .catch(error => {
                    alert('网络错误，请稍后重试');
                });
            }
        }
    </script>
</body>
</html>
