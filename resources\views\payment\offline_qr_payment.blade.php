<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>线下扫码支付</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
    </style>
</head>
<body>
<div class="container" style="max-width: 800px; margin: 40px auto; padding: 20px;">
    <!-- 订单信息 -->
    <div class="order-info" style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
        <h3 style="margin: 0 0 15px 0; color: #333;">📋 订单信息</h3>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>订单号：</span>
            <strong>{{ $order->order_no }}</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>订单金额：</span>
            <strong style="color: #e74c3c; font-size: 1.2em;">{{ $order->currency_code }} {{ number_format($order->order_amount, 2) }}</strong>
        </div>
        <div style="display: flex; justify-content: space-between;">
            <span>联系邮箱：</span>
            <span>{{ $order->email }}</span>
        </div>
    </div>

    <!-- 支付方式选择 -->
    <div class="payment-methods" style="background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 30px;">
        <h3 style="margin: 0 0 25px 0; color: #333; text-align: center;">💳 选择扫码支付方式</h3>
        
        <div class="qr-payments" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            @foreach($qrPayments as $qrPayment)
            <div class="qr-payment-item" data-id="{{ $qrPayment->id }}" data-qr-image="{{ $qrPayment->qr_image }}" style="border: 2px solid #e9ecef; border-radius: 8px; padding: 20px; cursor: pointer; transition: all 0.3s ease; text-align: center;">
                <!-- 封面图 -->
                <div class="cover-image" style="margin-bottom: 15px;">
                    @if($qrPayment->cover_image)
                        <img src="{{ $qrPayment->cover_image }}" alt="{{ $qrPayment->name }}" style="width: 200px; height: 200px; border-radius: 8px; object-fit: cover; border: 1px solid #ddd;">
                    @else
                        <div style="width: 200px; height: 200px; background: #f5f5f5; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto; border: 1px solid #ddd;">
                            <i class="fas fa-image" style="font-size: 3rem; color: #ccc;"></i>
                        </div>
                    @endif
                </div>
                
                <!-- 支付方式名称 -->
                <h4 style="margin: 0 0 10px 0; color: #333;">{{ $qrPayment->name }}</h4>
                
                <!-- 支付说明 -->
                @if($qrPayment->description)
                <p style="margin: 0 0 15px 0; color: #666; font-size: 14px;">{{ $qrPayment->description }}</p>
                @endif
                
                <!-- 选择按钮 -->
                <button class="select-payment-btn" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 14px; transition: background 0.3s ease;">
                    选择此方式
                </button>
            </div>
            @endforeach
        </div>
        
        <!-- 重要提示 -->
        <div class="important-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-top: 30px;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">⚠️ 重要提示</h4>
            <ul style="margin: 0; padding-left: 20px; color: #856404;">
                <li>扫码支付后，请务必在转账备注中填写：<strong>订单号 + 您的联系方式</strong></li>
                <li>完成支付后，请联系客服确认到账</li>
                <li>客服确认后，我们将尽快处理您的订单</li>
                <li>如有疑问，请保存好支付凭证</li>
            </ul>
        </div>
    </div>
</div>

<!-- 支付确认弹窗 -->
<div id="paymentModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; padding: 30px; max-width: 500px; width: 90%;">
        <h3 style="margin: 0 0 20px 0; text-align: center;">确认支付</h3>
        <div id="selectedPaymentInfo" style="text-align: center; margin-bottom: 20px;"></div>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
            <p style="margin: 0; color: #666; font-size: 14px;">
                请确认您已完成扫码支付，并在转账备注中填写了：<br>
                <strong>订单号：{{ $order->order_no }}</strong><br>
                <strong>联系方式：{{ $order->email }}</strong>
            </p>
        </div>
        <div style="text-align: center;">
            <button id="confirmPaymentBtn" style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 4px; margin-right: 10px; cursor: pointer;">
                确认已支付
            </button>
            <button onclick="closeModal()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 4px; cursor: pointer;">
                取消
            </button>
        </div>
    </div>
</div>

<style>
.qr-payment-item:hover {
    border-color: #007bff !important;
    box-shadow: 0 4px 15px rgba(0,123,255,0.2) !important;
    transform: translateY(-2px) !important;
}

.qr-payment-item.selected {
    border-color: #28a745 !important;
    background: #f8fff9 !important;
    box-shadow: 0 4px 15px rgba(40,167,69,0.2) !important;
}

.select-payment-btn:hover {
    background: #0056b3 !important;
}

@media (max-width: 768px) {
    .qr-payments {
        grid-template-columns: 1fr !important;
    }
    
    .qr-payment-item img {
        width: 150px !important;
        height: 150px !important;
    }
}
</style>

<script>
let selectedQrPaymentId = null;

document.addEventListener('DOMContentLoaded', function() {
    // 支付方式选择
    document.querySelectorAll('.qr-payment-item').forEach(item => {
        item.addEventListener('click', function() {
            // 移除其他选中状态
            document.querySelectorAll('.qr-payment-item').forEach(i => i.classList.remove('selected'));
            
            // 添加选中状态
            this.classList.add('selected');
            selectedQrPaymentId = this.dataset.id;
            
            // 显示确认弹窗
            showPaymentModal(this);
        });
    });
    
    // 确认支付按钮
    document.getElementById('confirmPaymentBtn').addEventListener('click', function() {
        if (!selectedQrPaymentId) {
            alert('请选择支付方式');
            return;
        }
        
        // 发送确认请求
        fetch('/payment/offline-qr/confirm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                order_id: {{ $order->id }},
                qr_payment_id: selectedQrPaymentId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            } else {
                alert('确认失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('网络错误，请稍后重试');
        });
    });
});

function showPaymentModal(selectedItem) {
    const paymentName = selectedItem.querySelector('h4').textContent;
    const qrImageUrl = selectedItem.dataset.qrImage;

    let modalContent = `<h4>${paymentName}</h4>`;
    if (qrImageUrl) {
        modalContent += `<img src="${qrImageUrl}" style="width: 200px; height: 200px; border-radius: 8px; margin: 15px 0; border: 1px solid #ddd;">`;
        modalContent += `<p style="color: #666; font-size: 14px;">请扫描上方二维码完成支付</p>`;
    } else {
        modalContent += `<div style="width: 200px; height: 200px; background: #f5f5f5; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 15px auto; border: 1px solid #ddd;">
            <i class="fas fa-qrcode" style="font-size: 3rem; color: #ccc;"></i>
        </div>`;
        modalContent += `<p style="color: #999; font-size: 14px;">二维码暂未上传</p>`;
    }

    document.getElementById('selectedPaymentInfo').innerHTML = modalContent;
    document.getElementById('paymentModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('paymentModal').style.display = 'none';
    // 移除选中状态
    document.querySelectorAll('.qr-payment-item').forEach(i => i.classList.remove('selected'));
    selectedQrPaymentId = null;
}

// 点击弹窗外部关闭
document.getElementById('paymentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
</body>
</html>
