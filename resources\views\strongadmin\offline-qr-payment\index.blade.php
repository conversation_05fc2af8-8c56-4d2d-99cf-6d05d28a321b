@extends('strongadmin::layouts.app')

@section('title', '线下扫码支付管理')

@section('content')
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .main-header {
            background: #343a40;
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        .qr-preview {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
        }
        .status-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .status-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #28a745;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .payment-icon {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-size: 1rem;
        }
        .modal-lg {
            max-width: 800px;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .btn-group-sm > .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>

<div class="main-header">
    <div class="container-fluid">
        <h1><i class="fas fa-qrcode"></i> 线下扫码支付管理</h1>
    </div>
</div>

<div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">支付方式列表</h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#paymentModal" onclick="openCreateModal()">
                    <i class="fas fa-plus"></i> 添加支付方式
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>图标</th>
                                <th>二维码</th>
                                <th>支付方式</th>
                                <th>描述</th>
                                <th>状态</th>
                                <th>排序</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="paymentList">
                            <!-- 数据将通过JavaScript加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">添加支付方式</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="paymentForm" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" id="paymentId" name="id">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">支付方式名称 *</label>
                                    <input type="text" class="form-control" id="name" name="name" required placeholder="如：支付宝付款码">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="icon">图标</label>
                                    <input type="text" class="form-control" id="icon" name="icon" placeholder="fas fa-qrcode">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="color">主题颜色</label>
                                    <input type="color" class="form-control" id="color" name="color" value="#000000">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="qr_image">二维码图片</label>
                                    <input type="file" class="form-control" id="qr_image" name="qr_image" accept="image/*">
                                    <small class="text-muted">支持 JPG、PNG、GIF 格式，最大 2MB</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="status">状态</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sort_order">排序</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description">支付说明</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="付款时候记得备注协商订单号，以备我们客服核实否者无法确定您支付的金额，切记切记！"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="instructions">支付步骤（每行一个步骤）</label>
                            <textarea class="form-control" id="instructions" name="instructions" rows="4" placeholder="扫描上方二维码进行支付&#10;在转账备注中填写：订单号 + 您的联系方式&#10;完成支付后，请联系客服确认到账&#10;客服确认后，我们将尽快处理您的订单"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>收款账户信息</h6>
                                <div id="accountInfoContainer">
                                    <div class="input-group mb-2">
                                        <input type="text" class="form-control" placeholder="标签" name="account_label[]">
                                        <input type="text" class="form-control" placeholder="值" name="account_value[]">
                                        <button type="button" class="btn btn-outline-danger" onclick="removeAccountInfo(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAccountInfo()">
                                    <i class="fas fa-plus"></i> 添加账户信息
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>客服联系方式</h6>
                                <div id="customerServiceContainer">
                                    <div class="input-group mb-2">
                                        <input type="text" class="form-control" placeholder="联系方式" name="service_label[]">
                                        <input type="text" class="form-control" placeholder="联系信息" name="service_value[]">
                                        <button type="button" class="btn btn-outline-danger" onclick="removeCustomerService(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addCustomerService()">
                                    <i class="fas fa-plus"></i> 添加客服信息
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script>
        // 页面加载完成后获取数据
        $(document).ready(function() {
            loadPaymentList();
        });

        // 加载支付方式列表
        function loadPaymentList() {
            $.get('/strongadmin/offline-qr-payment/data', function(response) {
                if (response.code === 200) {
                    renderPaymentList(response.data);
                } else {
                    alert('加载数据失败：' + response.message);
                }
            }).fail(function() {
                alert('网络错误，请稍后重试');
            });
        }

        // 渲染支付方式列表
        function renderPaymentList(payments) {
            const tbody = $('#paymentList');
            tbody.empty();

            if (payments.length === 0) {
                tbody.append('<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>');
                return;
            }

            payments.forEach(function(payment) {
                const qrImage = payment.qr_image ? 
                    `<img src="${payment.qr_image}" class="qr-preview" alt="二维码">` : 
                    '<span class="text-muted">未上传</span>';

                const statusSwitch = `
                    <label class="status-switch">
                        <input type="checkbox" ${payment.status ? 'checked' : ''} onchange="updateStatus(${payment.id}, this.checked)">
                        <span class="slider"></span>
                    </label>
                `;

                const row = `
                    <tr>
                        <td>${payment.id}</td>
                        <td>
                            <div class="payment-icon" style="background-color: ${payment.color}; color: white;">
                                <i class="${payment.icon}"></i>
                            </div>
                        </td>
                        <td>${qrImage}</td>
                        <td>${payment.name}</td>
                        <td>${payment.description ? payment.description.substring(0, 50) + '...' : '-'}</td>
                        <td>${statusSwitch}</td>
                        <td>
                            <input type="number" class="form-control form-control-sm" style="width: 80px;" 
                                   value="${payment.sort_order}" onchange="updateSort(${payment.id}, this.value)">
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="editPayment(${payment.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deletePayment(${payment.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // 打开创建模态框
        function openCreateModal() {
            $('#modalTitle').text('添加支付方式');
            $('#paymentForm')[0].reset();
            $('#paymentId').val('');
            clearDynamicFields();
            addAccountInfo();
            addCustomerService();
        }

        // 编辑支付方式
        function editPayment(id) {
            // 这里需要获取支付方式详情并填充表单
            // 为了简化，暂时不实现
            alert('编辑功能待实现');
        }

        // 删除支付方式
        function deletePayment(id) {
            if (confirm('确定要删除这个支付方式吗？')) {
                $.ajax({
                    url: `/strongadmin/offline-qr-payment/${id}`,
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.code === 200) {
                            alert('删除成功');
                            loadPaymentList();
                        } else {
                            alert('删除失败：' + response.message);
                        }
                    },
                    error: function() {
                        alert('网络错误，请稍后重试');
                    }
                });
            }
        }

        // 更新状态
        function updateStatus(id, status) {
            $.post('/strongadmin/offline-qr-payment/update-status', {
                id: id,
                status: status ? 1 : 0,
                _token: $('meta[name="csrf-token"]').attr('content')
            }, function(response) {
                if (response.code !== 200) {
                    alert('状态更新失败：' + response.message);
                    loadPaymentList(); // 重新加载以恢复状态
                }
            }).fail(function() {
                alert('网络错误，请稍后重试');
                loadPaymentList();
            });
        }

        // 更新排序
        function updateSort(id, sortOrder) {
            $.post('/strongadmin/offline-qr-payment/update-sort', {
                id: id,
                sort_order: sortOrder,
                _token: $('meta[name="csrf-token"]').attr('content')
            }, function(response) {
                if (response.code === 200) {
                    loadPaymentList(); // 重新加载以显示新排序
                } else {
                    alert('排序更新失败：' + response.message);
                }
            }).fail(function() {
                alert('网络错误，请稍后重试');
            });
        }

        // 添加账户信息
        function addAccountInfo() {
            const container = $('#accountInfoContainer');
            const html = `
                <div class="input-group mb-2">
                    <input type="text" class="form-control" placeholder="标签" name="account_label[]">
                    <input type="text" class="form-control" placeholder="值" name="account_value[]">
                    <button type="button" class="btn btn-outline-danger" onclick="removeAccountInfo(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            container.append(html);
        }

        // 移除账户信息
        function removeAccountInfo(btn) {
            $(btn).closest('.input-group').remove();
        }

        // 添加客服信息
        function addCustomerService() {
            const container = $('#customerServiceContainer');
            const html = `
                <div class="input-group mb-2">
                    <input type="text" class="form-control" placeholder="联系方式" name="service_label[]">
                    <input type="text" class="form-control" placeholder="联系信息" name="service_value[]">
                    <button type="button" class="btn btn-outline-danger" onclick="removeCustomerService(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            container.append(html);
        }

        // 移除客服信息
        function removeCustomerService(btn) {
            $(btn).closest('.input-group').remove();
        }

        // 清空动态字段
        function clearDynamicFields() {
            $('#accountInfoContainer').empty();
            $('#customerServiceContainer').empty();
        }

        // 表单提交
        $('#paymentForm').on('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            // 处理账户信息
            const accountInfo = {};
            const accountLabels = $('input[name="account_label[]"]');
            const accountValues = $('input[name="account_value[]"]');
            accountLabels.each(function(index) {
                const label = $(this).val().trim();
                const value = accountValues.eq(index).val().trim();
                if (label && value) {
                    accountInfo[label] = value;
                }
            });
            formData.append('account_info', JSON.stringify(accountInfo));
            
            // 处理客服信息
            const customerService = {};
            const serviceLabels = $('input[name="service_label[]"]');
            const serviceValues = $('input[name="service_value[]"]');
            serviceLabels.each(function(index) {
                const label = $(this).val().trim();
                const value = serviceValues.eq(index).val().trim();
                if (label && value) {
                    customerService[label] = value;
                }
            });
            formData.append('customer_service', JSON.stringify(customerService));
            
            const isEdit = $('#paymentId').val();
            const url = isEdit ? `/strongadmin/offline-qr-payment/${isEdit}` : '/strongadmin/offline-qr-payment';
            const method = isEdit ? 'PUT' : 'POST';
            
            if (isEdit) {
                formData.append('_method', 'PUT');
            }
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
            
            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.code === 200) {
                        alert(isEdit ? '更新成功' : '创建成功');
                        $('#paymentModal').modal('hide');
                        loadPaymentList();
                    } else {
                        alert((isEdit ? '更新' : '创建') + '失败：' + response.message);
                    }
                },
                error: function() {
                    alert('网络错误，请稍后重试');
                }
            });
        });
    </script>
@endsection

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
@endpush
