@extends('strongadmin::layouts.app')
@push('styles')
<style>
    td div.layui-table-cell{
        height:86px !important;
        /*line-height: normal !important;*/
        overflow: inherit;
        white-space: normal;
        overflow: hidden;
    }
</style>
@endpush
@push('scripts')
<script></script>
@endpush
@section('content')
<div class="st-h15"></div>
<form class="layui-form st-form-search" lay-filter="ST-FORM-SEARCH">
    <div class="layui-form-item"><div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('user_id')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="user_id" autocomplete="off" placeholder="email" class="layui-input">
            </div>
        </div><div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('order_no')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="order_no" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('order_status')}}</label>
            <div class="layui-input-inline">
                <select name="order_status">
                    <option value="">--</option>
                    @foreach($order_status as $status_key=>$status_val)
                    <option value="{{$status_key}}" @if(request('order_status')==$status_key) selected="" @endif >{{$status_val['name']}}</option>
                    @endforeach
                </select>  
            </div>
        </div> 
        <!-- <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('shipping_option_id')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="shipping_option_id" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <!-- <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('payment_option_id')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="payment_option_id" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <!-- <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('transaction_id')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="transaction_id" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <!-- <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('remark')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="remark" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <!-- <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('first_name')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="first_name" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <!-- <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('last_name')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="last_name" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <!-- <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('email')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="email" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <!-- <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('phone')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="phone" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <div class="layui-inline">
            <label class="layui-form-label">{{$model->getAttributeLabel('created_at')}}</label>
            <div class="layui-input-inline">
                <input type="text" name="created_at_begin" id="date" placeholder="年-月-日" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-input-inline">
                <input type="text" name="created_at_end" id="date2" placeholder="年-月-日" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <a class="layui-btn layui-btn-xs st-search-button">开始搜索</a>
        </div>
    </div>
</form>
<table class="layui-hide" id="ST-TABLE-LIST" lay-filter="ST-TABLE-LIST"></table>
<script type="text/html" id="ST-TOOL-BAR">
    <div class="layui-btn-container st-tool-bar">
        <a class="layui-btn layui-btn-xs" onclick="createMaximizedOrderWindow('/strongadmin/order/create', this.innerText);">添加</a>
        <a class="layui-btn layui-btn-xs" lay-event="batchDelete" data-href="/strongadmin/order/destroy">删除选中</a>
        <a class="layui-btn layui-btn-xs" lay-event="batchPrint" data-href="/strongadmin/order/print">批量打印</a>
    </div>
</script>
<script type="text/html" id="ST-OP-BUTTON">
    @verbatim
    <a class="layui-btn layui-btn-xs" onclick="createMaximizedOrderViewWindow('/strongadmin/order/show?id={{d.id}}', this.innerText);">查看</a>
    <a class="layui-btn layui-btn-xs" onclick="createMaximizedOrderWindow('/strongadmin/order/update?id={{d.id}}', this.innerText);">更新</a>
    <hr />
    <a class="layui-btn layui-btn-xs" href="/strongadmin/order/print?id={{d.id}}" target="_blank">打印</a>
    <a class="layui-btn layui-btn-xs" lay-event="more">更多 <i class="layui-icon layui-icon-down"></i></a>
    @endverbatim
</script>
@endsection
@push('scripts_bottom')        
<script>
!function () {
    //日期
    layui.laydate.render({
        elem: '#date'
    });
    layui.laydate.render({
        elem: '#date2'
    });
    //表格字段
    var cols = [
                {type: 'checkbox', fixed: 'left'}
                , {field: 'id', title: 'id', width: 60, fixed: 'left', unresize: true, totalRowText: '合计', sort: true}
                , {field: 'order_no', title: '{{$model->getAttributeLabel("order_no")}}', width: 180, fixed: 'left', sort: true, templet: function (res) {
                        var html = res.order_no + '<br/><br/>';
                        if (res.order_status_label && res.order_status_style) {
                            html += '<span class="layui-badge '+res.order_status_style+' order-status-badge" data-id="'+res.id+'" data-status="'+res.order_status+'" style="cursor:pointer;" title="点击编辑状态">'+res.order_status_label+'</span>';
                        } else if (res.order_status_label) {
                            html += '<span class="layui-badge layui-bg-gray order-status-badge" data-id="'+res.id+'" data-status="'+res.order_status+'" style="cursor:pointer;" title="点击编辑状态">'+res.order_status_label+'</span>';
                        } else {
                            html += '<span class="layui-badge layui-bg-gray order-status-badge" data-id="'+res.id+'" data-status="'+res.order_status+'" style="cursor:pointer;" title="点击编辑状态">未知状态</span>';
                        }
                        return html;
                }}
                , {field: 'created_at', title: '{{$model->getAttributeLabel("created_at")}}', width: 210,fixed: 'left', sort: true, templet: function (res) {
                        var html = res.buyer && res.buyer.email ? res.buyer.email : '未知用户';
                        html += '<br/><br/>' + res.created_at;
                        return  html;
                }}
                , {field: 'country', title: '收货信息', width: 310, templet: function (res) {
                        var html = '';
                        html += res.first_name + ' ' + res.last_name;
                        html += ' [' + res.country + ']';
                        html += '<hr/>';
                        html += res.address_line_1;
                        return  html;
                }}
                , {field: 'weight_total', title: '{{$model->getAttributeLabel("weight_total")}}', width: 100, sort: true, templet: function (res) {
                    return (res.weight_total/1000) + '千克';
                }}
                , {field: 'currency_code', title: '{{$model->getAttributeLabel("currency_code")}}', width: 120, sort: true, templet: function (res) {
                    return res.currency_code + '<hr/>' + 'Rate：' + res.currency_rate;
                }}
                , {field: 'order_amount', title: '{{$model->getAttributeLabel("order_amount")}}', width: 120, sort: true, templet: function (res) {
                        var defaultCurrencyBackend = '{{config("strongshop.defaultCurrencyBackend")}}';
                        var html = res.currency_code + ' ' + res.order_amount;
                        if(defaultCurrencyBackend === res.currency_code){
                            return html;
                        }
                        html += '<hr/>';
                        html += defaultCurrencyBackend + ' ' + (res.order_amount / res.currency_rate).toFixed({{config("strongshop.defaultDedimal")}});
                        return html;
                }}
                , {field: 'paid_amount', title: '{{$model->getAttributeLabel("paid_amount")}}', width: 120, sort: true}
//                , {field: 'products_amount', title: '{{$model->getAttributeLabel("products_amount")}}', width: 120, sort: true, templet: function (res) {
//                        var defaultCurrencyBackend = '{{config("strongshop.defaultCurrencyBackend")}}';
//                        var html = res.currency_code + ' ' + res.products_amount;
//                        if(defaultCurrencyBackend === res.currency_code){
//                            return html;
//                        }
//                        html += '<hr/>';
//                        html += defaultCurrencyBackend + ' ' + (res.products_amount / res.currency_rate).toFixed({{config("strongshop.defaultDedimal")}});
//                        return html;
//                }}
                , {field: 'source', title: '{{$model->getAttributeLabel("source")}}', width: 150, sort: true, templet:function(res){
                        var html = '';
                        if(res.source){
                            html += res.source;
                        }
                        if(res.http_referer){
                            html += '<hr/>'+res.http_referer;
                        }
                        return html;
                }}
                , {field: 'pay_remark', title: '{{$model->getAttributeLabel("pay_remark")}}', width: 150, sort: true}
                , {field: 'shipping_option_id', title: '{{$model->getAttributeLabel("shipping_option_id")}}', width: 150, sort: true, templet: function (res) {
                        var shippingTitle = res.shipping_option && res.shipping_option.title ? res.shipping_option.title : '未设置配送方式';
                        return shippingTitle +'<br/>' + '跟踪单号：' + (res.tracking_no || '无');
                }}
                , {field: 'payment_option_id', title: '{{$model->getAttributeLabel("payment_option_id")}}', width: 150, sort: true, templet: function (res) {
                        var paymentTitle = res.payment_option && res.payment_option.name ? res.payment_option.name : '未设置支付方式';
                        return paymentTitle + '<br/>' + '交易单号：' + (res.transaction_id || '无');
                }}
                , {field: 'remark', title: '{{$model->getAttributeLabel("remark")}}', width: 150, sort: true}
                , {field: 'shipped_at', title: '{{$model->getAttributeLabel("shipped_at")}}', width: 150, sort: true}
                , {field: 'received_at', title: '{{$model->getAttributeLabel("received_at")}}', width: 150, sort: true}
                , {field: 'canceled_at', title: '{{$model->getAttributeLabel("canceled_at")}}', width: 150, sort: true}
                , {field: 'returned_at', title: '{{$model->getAttributeLabel("returned_at")}}', width: 150, sort: true}
                , {fixed: 'right', title: '操作', toolbar: '#ST-OP-BUTTON', width: 150}
            ];
    var order_status = "{{request('order_status')}}";
    var tableConfig = {
        cols: [cols]
        ,where:{order_status:order_status}
    };
    var tableIns = Util.renderTable(tableConfig);
    
    var tableId = tableIns.config.id;
    //工具栏事件
    layui.table.on('toolbar(' + tableId + ')', function (obj) {
        var checkStatus = layui.table.checkStatus(obj.config.id);
        switch (obj.event) {
            case 'batchDelete':
                Util.batchDelete($(this).attr('data-href'), checkStatus.data);
                break;
            case 'ST-EXPORT-EXCEL':
                var data = layui.form.val(Util.tableConfigDefault.searchFormId);
                Util.exportFile(tableIns, data);
                break;
            case 'batchPrint':
                var arr = $.map(checkStatus.data, function (n, i) {
                    return n['id'];
                });
                var idStr = arr.join('|');
                window.open('/strongadmin/order/print?id='+idStr);
                break;
        }
        return;
    });
    
    //行工具事件
  layui.table.on('tool(ST-TABLE-LIST)', function(obj){
    var that = this,d = obj.data;
    console.log(d)
    if(obj.event === 'more'){
        //更多下拉菜单
        var dataOp = [];

        // 通用操作：编辑订单状态（所有状态都可以编辑）
        dataOp.push({title: '编辑状态',id: 'edit_status'});

        // 根据不同状态添加特定操作
        if(d.order_status === 10 || d.order_status === 13 || d.order_status === 14){
            // 待支付、支付失败、支付异常
            dataOp = dataOp.concat([
                {title: '确认已付款',id: 'paid'}
                ,{title: '关闭订单',id: 'close'}
            ]);
        }else if(d.order_status === 12){
            // 已支付
            dataOp = dataOp.concat([
                {title: '确认发货',id: 'shipped'}
            ]);
        }else if(d.order_status === 20){
            // 已发货
            dataOp = dataOp.concat([
                {title: '确认退货',id: 'returned'}
                ,{title: '确认完成',id: 'done'}
            ]);
        }else if(d.order_status === 22){
            // 已收获
            dataOp = dataOp.concat([
                {title: '确认完成',id: 'done'}
            ]);
        }else if(d.order_status === 24){
            // 退货中
            dataOp = dataOp.concat([
                {title: '确认已退货',id: 'returned'}
            ]);
        }else if(d.order_status === 26){
            // 已退货
            dataOp = dataOp.concat([
                {title: '重新处理',id: 'reprocess'}
            ]);
        }else if(d.order_status === 30){
            // 已完成
            dataOp = dataOp.concat([
                {title: '查看详情',id: 'view_detail'}
            ]);
        }else if(d.order_status === 40){
            // 已取消
            dataOp = dataOp.concat([
                {title: '重新激活',id: 'reactivate'}
            ]);
        }else if(d.order_status === 42){
            // 已关闭
            dataOp = dataOp.concat([
                {title: '删除',id: 'del'}
            ]);
        }
      layui.dropdown.render({
        elem: that
        ,show: true //外部事件触发即显示
        ,data: dataOp
        ,click: function(data, othis){
            // 编辑状态 - 直接打开编辑窗口，不需要确认
            if(data.id === 'edit_status'){
                createMaximizedOrderWindow('/strongadmin/order/update?id='+d.id, '编辑订单状态');
                return;
            }
            // 确认发货 - 打开发货表单
            if(data.id === 'shipped'){
                Util.createFormWindow('/strongadmin/order/confirm/shipped?id='+d.id, '确认发货');
                return;
            }
            // 查看详情 - 打开查看窗口
            if(data.id === 'view_detail'){
                createMaximizedOrderViewWindow('/strongadmin/order/show?id='+d.id, '订单详情');
                return;
            }

            layer.confirm('确定'+data.title+'?', function(index){
                //根据 id 做出不同操作
                if(data.id === 'del'){
                    $.post('/strongadmin/order/destroy', {id: d.id}).then(response => {
                        if (response.code === 200) {
                            layer.close(index);
                            layer.msg(response.message, {time: 1500},function(){
                                obj.del();
                            });
                        }else{
                            layer.msg(response.message, {anim: 6});
                        }
                    }).catch(error => {
                        console.error('删除失败:', error);
                        layer.msg('删除失败，请检查网络连接', {anim: 6});
                    });
                } else if(data.id === 'close'){
                    $.post('/strongadmin/order/confirm/close', {id:d.id}).then(response => {
                        console.log(obj);
                        if (response.code === 200) {
                            layer.close(index);
                            layer.msg(response.message, {time: 1500});
                            //重载刷新
                            tableIns.reload();
                        }else{
                            layer.msg(response.message, {anim: 6});
                        }
                    });
                } else if(data.id === 'paid'){
                    $.post('/strongadmin/order/confirm/paid', {id:d.id}).then(response => {
                        if (response.code === 200) {
                            layer.close(index);
                            layer.msg(response.message, {time: 1500});
                            //重载刷新
                            tableIns.reload();
                        }else{
                            layer.msg(response.message, {anim: 6});
                        }
                    });
                } else if(data.id === 'shipped'){
                    Util.createFormWindow('/strongadmin/order/confirm/shipped?id='+d.id, '确认发货');
                } else if(data.id === 'returned'){
                    $.post('/strongadmin/order/confirm/returned', {id:d.id}).then(response => {
                        if (response.code === 200) {
                            layer.close(index);
                            layer.msg(response.message, {time: 1500});
                            //重载刷新
                            tableIns.reload();
                        }else{
                            layer.msg(response.message, {anim: 6});
                        }
                    });
                } else if(data.id === 'done'){
                    $.post('/strongadmin/order/confirm/done', {id:d.id}).then(response => {
                        if (response.code === 200) {
                            layer.close(index);
                            layer.msg(response.message, {time: 1500});
                            //重载刷新
                            tableIns.reload();
                        }else{
                            layer.msg(response.message, {anim: 6});
                        }
                    });
                } else if(data.id === 'reprocess'){
                    // 重新处理订单 - 将状态改为待支付
                    $.post('/strongadmin/order/update', {id:d.id, order_status: 10}).then(response => {
                        if (response.code === 200) {
                            layer.close(index);
                            layer.msg('订单已重新激活为待支付状态', {time: 1500});
                            tableIns.reload();
                        }else{
                            layer.msg(response.message, {anim: 6});
                        }
                    });
                } else if(data.id === 'reactivate'){
                    // 重新激活订单 - 将状态改为待支付
                    $.post('/strongadmin/order/update', {id:d.id, order_status: 10}).then(response => {
                        if (response.code === 200) {
                            layer.close(index);
                            layer.msg('订单已重新激活为待支付状态', {time: 1500});
                            tableIns.reload();
                        }else{
                            layer.msg(response.message, {anim: 6});
                        }
                    });
                }
            });
        }
        ,style: 'margin-left: -45px; box-shadow: 1px 1px 10px rgb(0 0 0 / 12%);' //设置额外样式
      }); 
    }
  });
}();

// 状态标签点击编辑事件
$(document).on('click', '.order-status-badge', function(e) {
    e.stopPropagation();
    var orderId = $(this).data('id');
    var currentStatus = $(this).data('status');

    // 状态选项
    var statusOptions = [
        {value: 10, text: '待支付', style: 'layui-bg-orange'},
        {value: 12, text: '已支付', style: 'layui-bg-green'},
        {value: 13, text: '支付失败', style: 'layui-bg-red'},
        {value: 14, text: '支付异常', style: 'layui-bg-red'},
        {value: 20, text: '已发货', style: 'layui-bg-cyan'},
        {value: 22, text: '已收获', style: 'layui-bg-cyan'},
        {value: 24, text: '退货中', style: 'layui-bg-orange'},
        {value: 26, text: '已退货', style: 'layui-bg-cyan'},
        {value: 30, text: '已完成', style: 'layui-bg-green'},
        {value: 40, text: '已取消', style: 'layui-bg-gray'},
        {value: 42, text: '已关闭', style: 'layui-bg-gray'}
    ];

    // 构建选项HTML
    var optionsHtml = '';
    statusOptions.forEach(function(option) {
        var selected = option.value == currentStatus ? 'selected' : '';
        optionsHtml += '<option value="'+option.value+'" '+selected+'>'+option.text+'</option>';
    });

    // 弹出编辑框
    layer.open({
        type: 1,
        title: '快速编辑订单状态',
        content: '<div style="padding:20px;"><select id="status-select" lay-filter="status-select">'+optionsHtml+'</select></div>',
        btn: ['保存', '取消'],
        yes: function(index, layero) {
            var newStatus = layero.find('#status-select').val();
            if (newStatus && newStatus != currentStatus) {
                // 更新状态
                $.post('/strongadmin/order/update', {
                    id: orderId,
                    order_status: newStatus,
                    _token: $('meta[name="csrf-token"]').attr('content')
                }).then(response => {
                    if (response.code === 200) {
                        layer.close(index);
                        layer.msg('状态更新成功', {time: 1500});
                        tableIns.reload();
                    } else {
                        layer.msg(response.message, {anim: 6});
                    }
                }).catch(error => {
                    layer.msg('更新失败，请重试', {anim: 6});
                });
            } else {
                layer.close(index);
            }
        },
        success: function(layero, index) {
            // 渲染下拉框
            layui.form.render('select');
        }
    });
});

// 创建最大化的订单窗口
function createMaximizedOrderWindow(url, title = '信息') {
    var index = layer.open({
        type: 2,
        content: url,
        title: title,
        area: ['100%', '100%'], // 设置为全屏大小
        fixed: false,
        maxmin: true,
        resize: true,
        btn: ['保存', '取消'],
        yes: function (index, layero) {
            // 按钮【保存】的回调
            layer.getChildFrame('form.layui-form :submit', index).click();
            return false; // 禁止点击该按钮关闭窗口
        },
        btn2: function (index, layero) {
            // 按钮【取消】的回调
        },
        cancel: function () {
            // 右上角关闭回调
        },
        success: function(layero, index) {
            // 窗口打开成功后自动最大化
            layer.full(index);
        }
    });
}

// 创建最大化的订单查看窗口
function createMaximizedOrderViewWindow(url, title = '信息') {
    var index = layer.open({
        type: 2,
        content: url,
        title: title,
        area: ['100%', '100%'], // 设置为全屏大小
        fixed: false,
        maxmin: true,
        resize: true,
        btn: ['关闭'],
        yes: function (index, layero) {
            // 按钮【关闭】的回调
            layer.close(index);
        },
        cancel: function () {
            // 右上角关闭回调
        },
        success: function(layero, index) {
            // 窗口打开成功后自动最大化
            layer.full(index);
        }
    });
}
</script>
@endpush