@extends('strongadmin.layouts.app')

@section('title', '国内支付管理')

@section('content')
<meta name="csrf-token" content="{{ csrf_token() }}">
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>🇨🇳 国内支付方式管理 <p style="margin: 5px 0 0 0; color: #666; font-size: 13px;">管理中国大陆主流支付方式，支持支付宝、微信支付、银联等本土支付网关</p></h3>
                </div>
                <div class="layui-card-body">
                    
                    <!-- 操作按钮 -->
                    <div style="margin-bottom: 20px; text-align: center;">
                        <button type="button" class="layui-btn layui-btn-normal" onclick="refreshPaymentList()">
                            <i class="layui-icon layui-icon-refresh"></i> 刷新列表
                        </button>
                        <a href="/strongadmin/international-payment/index" class="layui-btn layui-btn-primary">
                            <i class="layui-icon layui-icon-website"></i> 国际支付管理
                        </a>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="showConfigGuide()">
                            <i class="layui-icon layui-icon-help"></i> 配置指南
                        </button>
                    </div>

                    <!-- 支付方式列表 -->
                    <div id="payment-methods-list">
                        <div style="text-align: center; padding: 40px; color: #999;">
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>
                            <p>正在加载支付方式...</p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- 线下扫码支付管理 -->
    <div class="layui-row layui-col-space15" style="margin-top: 15px;">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600; color: #333;">📱 线下扫码支付管理</h3>
                    <p style="margin: 0; color: #666; font-size: 14px; line-height: 1.5;">
                        管理自定义的线下扫码支付方式，客户扫码后需联系客服确认
                    </p>
                </div>
                <div class="layui-card-body">

                    <!-- 操作按钮 -->
                    <div style="margin-bottom: 20px;">
                        <button type="button" class="layui-btn layui-btn-normal" onclick="addOfflineQrPayment()">
                            <i class="layui-icon layui-icon-add-1"></i> 添加支付方式
                        </button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="refreshOfflineQrList()">
                            <i class="layui-icon layui-icon-refresh"></i> 刷新列表
                        </button>
                    </div>

                    <!-- 线下扫码支付列表 -->
                    <div id="offline-qr-list">
                        <div style="text-align: center; padding: 40px; color: #999;">
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>
                            <p>正在加载线下扫码支付方式...</p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- 线下扫码支付弹窗 -->
<div id="offlineQrModal" class="tutorial-modal" style="display: none;">
    <div class="tutorial-modal-content" style="max-width: 800px;">
        <div class="tutorial-modal-header">
            <span class="tutorial-close" onclick="closeOfflineQrModal()">&times;</span>
            <h3 id="offlineQrModalTitle">添加线下扫码支付</h3>
        </div>
        <div class="tutorial-modal-body">
            <form id="offlineQrForm" enctype="multipart/form-data">
                <input type="hidden" id="offlineQrId" name="id">

                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">支付方式名称 *</label>
                            <div class="layui-input-block">
                                <input type="text" id="offlineQrName" name="name" required lay-verify="required" placeholder="如：支付宝付款码" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">图标</label>
                            <div class="layui-input-block">
                                <input type="text" id="offlineQrIcon" name="icon" placeholder="fab fa-alipay" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">主题颜色</label>
                            <div class="layui-input-block">
                                <input type="color" id="offlineQrColor" name="color" value="#000000" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">二维码图片</label>
                            <div class="layui-input-block">
                                <input type="file" id="offlineQrImage" name="qr_image" accept="image/*" class="layui-input">
                                <div class="layui-form-mid layui-word-aux">支持 JPG、PNG、GIF 格式，最大 2MB</div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <select id="offlineQrStatus" name="status" class="layui-input">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">排序</label>
                            <div class="layui-input-block">
                                <input type="number" id="offlineQrSort" name="sort_order" value="0" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">支付说明</label>
                    <div class="layui-input-block">
                        <textarea id="offlineQrDescription" name="description" placeholder="付款时候记得备注协商订单号，以备我们客服核实否者无法确定您支付的金额，切记切记！" class="layui-textarea"></textarea>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">支付步骤</label>
                    <div class="layui-input-block">
                        <textarea id="offlineQrInstructions" name="instructions" placeholder="扫描上方二维码进行支付&#10;在转账备注中填写：订单号 + 您的联系方式&#10;完成支付后，请联系客服确认到账&#10;客服确认后，我们将尽快处理您的订单" class="layui-textarea"></textarea>
                        <div class="layui-form-mid layui-word-aux">每行一个步骤</div>
                    </div>
                </div>

                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">收款账户信息</label>
                            <div class="layui-input-block">
                                <div id="accountInfoContainer">
                                    <div class="layui-input-group" style="margin-bottom: 10px;">
                                        <input type="text" placeholder="标签" name="account_label[]" class="layui-input" style="width: 30%;">
                                        <input type="text" placeholder="值" name="account_value[]" class="layui-input" style="width: 60%;">
                                        <button type="button" class="layui-btn layui-btn-danger layui-btn-sm" onclick="removeAccountInfo(this)" style="width: 10%;">删除</button>
                                    </div>
                                </div>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addAccountInfo()">
                                    <i class="layui-icon layui-icon-add-1"></i> 添加账户信息
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">客服联系方式</label>
                            <div class="layui-input-block">
                                <div id="customerServiceContainer">
                                    <div class="layui-input-group" style="margin-bottom: 10px;">
                                        <input type="text" placeholder="联系方式" name="service_label[]" class="layui-input" style="width: 30%;">
                                        <input type="text" placeholder="联系信息" name="service_value[]" class="layui-input" style="width: 60%;">
                                        <button type="button" class="layui-btn layui-btn-danger layui-btn-sm" onclick="removeCustomerService(this)" style="width: 10%;">删除</button>
                                    </div>
                                </div>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addCustomerService()">
                                    <i class="layui-icon layui-icon-add-1"></i> 添加客服信息
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
                    <button type="button" class="layui-btn layui-btn-primary" onclick="closeOfflineQrModal()">取消</button>
                    <button type="submit" class="layui-btn layui-btn-normal">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 配置弹窗 -->
<div id="configModal" class="tutorial-modal">
    <div class="tutorial-modal-content">
        <div class="tutorial-modal-header">
            <span class="tutorial-close" onclick="closeConfigModal()">&times;</span>
            <h3 id="configTitle">支付方式配置</h3>
            <p id="configDescription">配置API密钥和参数</p>
        </div>
        <div class="tutorial-modal-body">
            <form id="paymentConfigForm">
                <div id="configFormContent">
                    <!-- 配置表单内容将在这里动态生成 -->
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" class="layui-btn layui-btn-normal" onclick="savePaymentConfig()">
                        <i class="layui-icon layui-icon-ok"></i> 保存配置
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="testCurrentPaymentConfig()">
                        <i class="layui-icon layui-icon-link"></i> 测试配置
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="generatePaymentEnv()">
                        <i class="layui-icon layui-icon-file"></i> 生成环境变量
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 教程弹窗 -->
<div id="tutorialModal" class="tutorial-modal">
    <div class="tutorial-modal-content">
        <div class="tutorial-modal-header">
            <span class="tutorial-close">&times;</span>
            <h3 id="tutorialTitle">支付方式对接教程</h3>
            <p id="tutorialDescription">详细的申请和配置指南</p>
        </div>
        <div class="tutorial-modal-body">
            <div class="tutorial-section">
                <h4>📝 申请步骤</h4>
                <ol id="tutorialSteps" class="tutorial-steps"></ol>
            </div>
            <div class="tutorial-section">
                <h4>⚙️ 环境变量配置</h4>
                <div id="tutorialConfig" class="tutorial-config"></div>
            </div>
            <div class="tutorial-section">
                <h4>💡 重要提示</h4>
                <ul id="tutorialNotes" class="tutorial-notes"></ul>
            </div>
        </div>
        <div class="tutorial-footer">
            <button onclick="closeTutorialModal()">关闭</button>
            <button onclick="copyConfig()">复制配置</button>
        </div>
    </div>
</div>

<style>
.payment-method-item {
    border-bottom: 1px solid #f0f0f0;
    padding: 20px;
    transition: background-color 0.3s;
}

.payment-method-item:hover {
    background-color: #f8f9fa;
}

.payment-method-item:first-child {
    border-top: 1px solid #f0f0f0;
}

.payment-logo {
    width: 50px;
    height: 35px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-weight: bold;
    color: white;
    font-size: 12px;
}

.payment-name {
    margin: 0;
    color: #495057;
    font-size: 18px;
    font-weight: bold;
}

.payment-status {
    margin-top: 5px;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.status-enabled {
    background: #d4edda;
    color: #155724;
}

.status-disabled {
    background: #f8d7da;
    color: #721c24;
}

.payment-description {
    margin: 10px 0;
    color: #666;
    line-height: 1.5;
}

.payment-info {
    font-size: 13px;
    color: #999;
    margin-bottom: 10px;
}

.payment-info strong {
    color: #666;
}

.payment-actions {
    text-align: right;
}

.btn {
    display: inline-block;
    padding: 6px 12px;
    margin: 2px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    text-decoration: none;
    transition: background-color 0.3s;
}

.btn-enable {
    background: #28a745;
    color: white;
}

.btn-enable:hover {
    background: #218838;
}

.btn-disable {
    background: #dc3545;
    color: white;
}

.btn-disable:hover {
    background: #c82333;
}

.btn-test {
    background: #007bff;
    color: white;
}

.btn-test:hover {
    background: #0056b3;
}

.btn-config {
    background: #ffc107;
    color: #212529;
}

.btn-config:hover {
    background: #e0a800;
}

.btn-info {
    background: #6c757d;
    color: white;
}

.btn-info:hover {
    background: #545b62;
}

/* 教程弹窗样式 */
.tutorial-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.tutorial-modal-content {
    background-color: #fefefe;
    margin: 2% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90%;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.tutorial-modal-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 20px;
    border-radius: 8px 8px 0 0;
    position: relative;
}

.tutorial-modal-header h3 {
    margin: 0;
    font-size: 24px;
}

.tutorial-modal-header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
}

.tutorial-close {
    position: absolute;
    right: 20px;
    top: 20px;
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.tutorial-close:hover {
    opacity: 0.7;
}

.tutorial-modal-body {
    padding: 30px;
}

.tutorial-section {
    margin-bottom: 30px;
}

.tutorial-section h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 8px;
}

.tutorial-steps {
    list-style: none;
    padding: 0;
}

.tutorial-steps li {
    background: #f8f9fa;
    margin: 8px 0;
    padding: 12px 15px;
    border-left: 4px solid #ff6b6b;
    border-radius: 4px;
}

.tutorial-config {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    overflow-x: auto;
}

.tutorial-notes {
    list-style: none;
    padding: 0;
}

.tutorial-notes li {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    margin: 8px 0;
    padding: 10px 15px;
    border-radius: 4px;
    color: #856404;
}

.tutorial-footer {
    background: #f8f9fa;
    padding: 20px;
    text-align: center;
    border-radius: 0 0 8px 8px;
    border-top: 1px solid #e9ecef;
}

.tutorial-footer button {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin: 0 5px;
}

.tutorial-footer button:hover {
    background: #ee5a24;
}
</style>

<script>
// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadPaymentMethods();
    loadOfflineQrPayments();
    initOfflineQrMasterSwitch();
});

// 加载支付方式列表
function loadPaymentMethods() {
    var container = document.getElementById('payment-methods-list');
    container.innerHTML = '<div style="text-align: center; padding: 40px; color: #999;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i><p>正在加载支付方式...</p></div>';

    fetch('/strongadmin/domestic-payment/data')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                renderPaymentMethods(data.data);
            } else {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #f56c6c;"><i class="layui-icon layui-icon-close" style="font-size: 30px;"></i><p>加载失败：' + data.message + '</p></div>';
            }
        })
        .catch(error => {
            container.innerHTML = '<div style="text-align: center; padding: 40px; color: #f56c6c;"><i class="layui-icon layui-icon-close" style="font-size: 30px;"></i><p>网络错误，请检查连接</p></div>';
        });
}

// 渲染支付方式列表
function renderPaymentMethods(payments) {
    var container = document.getElementById('payment-methods-list');

    if (!payments || payments.length === 0) {
        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #999;"><i class="layui-icon layui-icon-face-cry" style="font-size: 30px;"></i><p>暂无支付方式数据</p></div>';
        return;
    }

    var html = '';
    payments.forEach(function(payment, index) {
        var config = {};
        try {
            config = JSON.parse(payment.config || '{}');
        } catch (e) {
            config = {};
        }

        var statusClass = payment.status == 1 ? 'status-enabled' : 'status-disabled';
        var statusText = payment.status == 1 ? '已启用' : '已禁用';
        var toggleText = payment.status == 1 ? '禁用' : '启用';
        var toggleClass = payment.status == 1 ? 'btn-disable' : 'btn-enable';

        html += '<div class="payment-method-item">';
        html += '  <div style="display: flex; align-items: flex-start;">';
        html += '    <div class="payment-logo">' + payment.code.toUpperCase().substr(0, 3) + '</div>';
        html += '    <div style="flex: 1;">';
        html += '      <h4 class="payment-name">' + payment.name + '</h4>';
        html += '      <span class="payment-status ' + statusClass + '">' + statusText + '</span>';
        html += '      <p class="payment-description">' + (payment.description || '暂无描述') + '</p>';
        html += '      <div class="payment-info">';
        html += '        <strong>支持地区：</strong>' + (config.supported_countries || '中国大陆') + ' | ';
        html += '        <strong>支持货币：</strong>' + (config.supported_currencies || 'CNY') + ' | ';
        html += '        <strong>费率：</strong>' + (config.fees || '联系获取') + ' | ';
        html += '        <strong>结算时间：</strong>' + (config.settlement_time || '联系确认');
        html += '      </div>';
        if (config.website) {
            html += '      <p><a href="' + config.website + '" target="_blank" style="color: #ff6b6b;">官方网站 →</a></p>';
        }
        html += '    </div>';
        html += '    <div class="payment-actions">';
        html += '      <button class="btn ' + toggleClass + '" onclick="togglePaymentStatus(' + payment.id + ', ' + (payment.status == 1 ? 0 : 1) + ')">' + toggleText + '</button>';
        html += '      <button class="btn btn-test" onclick="testPaymentGateway(\'' + payment.code + '\')">测试连接</button>';
        html += '      <button class="btn btn-config" onclick="showPaymentConfig(\'' + payment.code + '\')">配置</button>';
        html += '      <button class="btn btn-info" onclick="showPaymentInfo(\'' + payment.code + '\')">详情</button>';
        html += '    </div>';
        html += '  </div>';
        html += '</div>';
    });

    container.innerHTML = html;
}

// 切换支付方式状态
function togglePaymentStatus(id, status) {
    var statusText = status == 1 ? '启用' : '禁用';

    if (confirm('确定要' + statusText + '这个支付方式吗？')) {
        fetch('/strongadmin/domestic-payment/update-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                id: id,
                status: status
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                alert(statusText + '成功');
                loadPaymentMethods(); // 重新加载列表
            } else {
                alert('操作失败：' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            alert('网络错误，请重试');
        });
    }
}

// 测试支付网关
function testPaymentGateway(code) {
    fetch('/strongadmin/domestic-payment/test-gateway', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            code: code
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            alert('连接测试成功');
        } else {
            alert('测试失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        alert('测试请求失败');
    });
}

// 刷新支付方式列表
function refreshPaymentList() {
    loadPaymentMethods();
}

// 显示支付方式配置
function showPaymentConfig(code) {
    console.log('showPaymentConfig 被调用，参数 code:', code);

    var configs = {
        'alipay': {
            name: '支付宝',
            description: '配置支付宝支付参数',
            fields: [
                {
                    label: 'APP ID',
                    name: 'alipay_app_id',
                    type: 'text',
                    placeholder: '2021xxxxxxxxxxxxxxxxx',
                    help: '支付宝开放平台应用ID'
                },
                {
                    label: '应用私钥',
                    name: 'alipay_private_key',
                    type: 'textarea',
                    placeholder: '-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----',
                    help: '应用私钥，用于签名'
                },
                {
                    label: '支付宝公钥',
                    name: 'alipay_public_key',
                    type: 'textarea',
                    placeholder: '-----BEGIN PUBLIC KEY-----\\n...\\n-----END PUBLIC KEY-----',
                    help: '支付宝公钥，用于验签'
                },
                {
                    label: '环境',
                    name: 'alipay_env',
                    type: 'select',
                    options: [
                        {value: 'sandbox', text: '沙盒环境 (sandbox)'},
                        {value: 'production', text: '正式环境 (production)'}
                    ],
                    help: '选择运行环境'
                }
            ]
        },
        'wechat_pay': {
            name: '微信支付',
            description: '配置微信支付参数',
            fields: [
                {
                    label: 'APP ID',
                    name: 'wechat_app_id',
                    type: 'text',
                    placeholder: 'wxxxxxxxxxxxxxxxxxxx',
                    help: '微信公众号或小程序的AppID'
                },
                {
                    label: '商户号',
                    name: 'wechat_mch_id',
                    type: 'text',
                    placeholder: '1234567890',
                    help: '微信支付商户号'
                },
                {
                    label: 'API密钥',
                    name: 'wechat_key',
                    type: 'password',
                    placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                    help: '微信支付API密钥'
                },
                {
                    label: '证书路径',
                    name: 'wechat_cert_path',
                    type: 'text',
                    placeholder: '/path/to/apiclient_cert.pem',
                    help: '商户证书文件路径'
                },
                {
                    label: '私钥路径',
                    name: 'wechat_key_path',
                    type: 'text',
                    placeholder: '/path/to/apiclient_key.pem',
                    help: '商户私钥文件路径'
                }
            ]
        },
        'unionpay': {
            name: '银联支付',
            description: '配置银联支付参数',
            fields: [
                {
                    label: '商户号',
                    name: 'unionpay_mer_id',
                    type: 'text',
                    placeholder: '777290058110048',
                    help: '银联分配的商户号'
                },
                {
                    label: '证书ID',
                    name: 'unionpay_cert_id',
                    type: 'text',
                    placeholder: '68759585097',
                    help: '证书序列号'
                },
                {
                    label: '私钥路径',
                    name: 'unionpay_private_key_path',
                    type: 'text',
                    placeholder: '/path/to/private.key',
                    help: '商户私钥文件路径'
                },
                {
                    label: '环境',
                    name: 'unionpay_env',
                    type: 'select',
                    options: [
                        {value: 'test', text: '测试环境'},
                        {value: 'production', text: '正式环境'}
                    ],
                    help: '选择运行环境'
                }
            ]
        },
        'qq_pay': {
            name: 'QQ钱包',
            description: '配置QQ钱包支付参数',
            fields: [
                {
                    label: '商户号',
                    name: 'qqpay_mch_id',
                    type: 'text',
                    placeholder: '1234567890',
                    help: 'QQ钱包商户号'
                },
                {
                    label: 'API密钥',
                    name: 'qqpay_key',
                    type: 'password',
                    placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                    help: 'QQ钱包API密钥'
                },
                {
                    label: 'APP ID',
                    name: 'qqpay_app_id',
                    type: 'text',
                    placeholder: '1234567890',
                    help: 'QQ应用ID'
                }
            ]
        },
        'jd_pay': {
            name: '京东支付',
            description: '配置京东支付参数',
            fields: [
                {
                    label: '商户号',
                    name: 'jdpay_mer_code',
                    type: 'text',
                    placeholder: '110123456789',
                    help: '京东支付商户号'
                },
                {
                    label: 'DES密钥',
                    name: 'jdpay_des_key',
                    type: 'password',
                    placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                    help: 'DES加密密钥'
                },
                {
                    label: 'MD5密钥',
                    name: 'jdpay_md5_key',
                    type: 'password',
                    placeholder: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                    help: 'MD5签名密钥'
                }
            ]
        },
        'ping_plus': {
            name: 'Ping++',
            description: '配置Ping++聚合支付参数',
            fields: [
                {
                    label: 'API Key',
                    name: 'pingpp_api_key',
                    type: 'password',
                    placeholder: 'sk_test_xxxxxxxxxxxxxxxxxxxxxxxxxx',
                    help: 'Ping++ API密钥'
                },
                {
                    label: 'App ID',
                    name: 'pingpp_app_id',
                    type: 'text',
                    placeholder: 'app_xxxxxxxxxxxxxxxxxx',
                    help: 'Ping++ 应用ID'
                },
                {
                    label: 'RSA私钥',
                    name: 'pingpp_private_key',
                    type: 'textarea',
                    placeholder: '-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----',
                    help: 'RSA私钥，用于签名'
                }
            ]
        },
        'bank_transfer': {
            name: '银行转账',
            description: '配置银行转账收款信息',
            fields: [
                {
                    label: '银行名称',
                    name: 'bank_name',
                    type: 'text',
                    placeholder: '例如：中国工商银行',
                    help: '收款银行的名称'
                },
                {
                    label: '账户名称',
                    name: 'account_name',
                    type: 'text',
                    placeholder: '公司名称或个人姓名',
                    help: '银行账户的户名'
                },
                {
                    label: '账户号码',
                    name: 'account_number',
                    type: 'text',
                    placeholder: '银行账户号码',
                    help: '完整的银行账户号码'
                },
                {
                    label: '开户行地址',
                    name: 'bank_address',
                    type: 'text',
                    placeholder: '具体开户行地址',
                    help: '开户行的详细地址（可选）'
                },
                {
                    label: 'SWIFT代码',
                    name: 'swift_code',
                    type: 'text',
                    placeholder: '国际汇款SWIFT代码',
                    help: '用于国际汇款的SWIFT代码（可选）'
                },
                {
                    label: '支持货币',
                    name: 'supported_currencies',
                    type: 'text',
                    placeholder: 'CNY, USD, EUR',
                    help: '支持的货币类型，用逗号分隔'
                },
                {
                    label: '转账说明',
                    name: 'transfer_instructions',
                    type: 'textarea',
                    placeholder: '请在转账备注中填写订单号，转账后请联系客服确认...',
                    help: '客户转账时的注意事项和说明'
                },
                {
                    label: '客服邮箱',
                    name: 'contact_email',
                    type: 'text',
                    placeholder: '<EMAIL>',
                    help: '客户联系的邮箱地址'
                },
                {
                    label: '客服微信',
                    name: 'contact_wechat',
                    type: 'text',
                    placeholder: 'WeChat_ID 或微信号',
                    help: '客服微信号或微信ID'
                },
                {
                    label: '微信二维码',
                    name: 'wechat_qrcode',
                    type: 'file',
                    accept: 'image/*',
                    help: '上传微信二维码图片，客户可扫码添加客服微信'
                },
                {
                    label: '客服电话',
                    name: 'contact_phone',
                    type: 'text',
                    placeholder: '+86 138-0000-0000',
                    help: '客服联系电话'
                },
                {
                    label: '在线客服时间',
                    name: 'service_hours',
                    type: 'text',
                    placeholder: '工作时间 9:00-18:00',
                    help: '在线客服的工作时间'
                },
                {
                    label: 'QQ客服',
                    name: 'contact_qq',
                    type: 'text',
                    placeholder: 'QQ号码',
                    help: 'QQ客服号码（可选）'
                }
            ]
        }
    };

    var config = configs[code];
    console.log('找到的配置:', config);

    if (!config) {
        console.error('未找到支付方式配置:', code);
        alert('暂不支持该支付方式的配置');
        return;
    }

    console.log('调用 showConfigModal，参数:', config, code);
    showConfigModal(config, code);
}

// 显示配置弹窗
function showConfigModal(config, code) {
    document.getElementById('configTitle').textContent = config.name + ' 配置';
    document.getElementById('configDescription').textContent = config.description;

    var formContent = document.getElementById('configFormContent');
    var html = '';

    config.fields.forEach(function(field) {
        html += '<div class="layui-form-item" style="margin-bottom: 20px;">';
        html += '  <label class="layui-form-label" style="width: 120px; font-weight: bold;">' + field.label + '</label>';
        html += '  <div class="layui-input-block" style="margin-left: 140px;">';

        if (field.type === 'select') {
            html += '    <select name="' + field.name + '" class="layui-input">';
            field.options.forEach(function(option) {
                html += '      <option value="' + option.value + '">' + option.text + '</option>';
            });
            html += '    </select>';
        } else if (field.type === 'textarea') {
            html += '    <textarea name="' + field.name + '" placeholder="' + field.placeholder + '" class="layui-textarea" rows="5"></textarea>';
        } else if (field.type === 'file') {
            html += '    <input type="file" name="' + field.name + '" class="layui-input" accept="' + (field.accept || '*') + '" onchange="previewImage(this, \'' + field.name + '\')">';
            html += '    <div id="preview_' + field.name + '" style="margin-top: 10px;"></div>';
        } else {
            html += '    <input type="' + field.type + '" name="' + field.name + '" placeholder="' + field.placeholder + '" class="layui-input">';
        }

        if (field.help) {
            html += '    <div class="layui-form-mid layui-word-aux" style="color: #999; font-size: 12px;">' + field.help + '</div>';
        }
        html += '  </div>';
        html += '</div>';
    });

    formContent.innerHTML = html;

    // 保存当前配置的支付方式代码
    window.currentConfigCode = code;
    console.log('设置 window.currentConfigCode 为:', code);

    // 加载已保存的配置
    setTimeout(() => loadSavedConfig(code), 100);

    // 显示弹窗
    document.getElementById('configModal').style.display = 'block';
}

// 关闭配置弹窗
function closeConfigModal() {
    document.getElementById('configModal').style.display = 'none';
}

// 加载已保存的配置
function loadSavedConfig(code) {
    fetch('/debug/all-payment-options')
        .then(response => response.json())
        .then(data => {
            if (data.all_payments) {
                const payment = data.all_payments.find(p => p.code === code);
                if (payment && payment.config) {
                    try {
                        // 处理双重JSON编码的问题
                        let config = payment.config;
                        if (typeof config === 'string') {
                            config = JSON.parse(config);
                        }
                        if (typeof config === 'string') {
                            config = JSON.parse(config);
                        }

                        console.log('加载已保存的配置:', config);

                        // 填充表单字段
                        Object.keys(config).forEach(key => {
                            const field = document.querySelector(`[name="${key}"]`);
                            if (field) {
                                if (field.type === 'select-one') {
                                    field.value = config[key];
                                } else if (field.type === 'textarea') {
                                    field.value = config[key];
                                } else {
                                    field.value = config[key];
                                }
                                console.log('填充字段:', key, '=', config[key]);
                            } else {
                                console.log('未找到字段:', key);
                            }
                        });
                    } catch (e) {
                        console.error('解析配置失败:', e, payment.config);
                    }
                }
            }
        })
        .catch(error => {
            console.error('加载配置失败:', error);
        });
}

// 加载已保存的配置
function loadSavedConfig(code) {
    fetch('/debug/all-payment-options')
        .then(response => response.json())
        .then(data => {
            if (data.all_payments) {
                const payment = data.all_payments.find(p => p.code === code);
                if (payment && payment.config) {
                    try {
                        const config = typeof payment.config === 'string' ? JSON.parse(payment.config) : payment.config;
                        console.log('加载已保存的配置:', config);

                        // 填充表单字段
                        Object.keys(config).forEach(key => {
                            const field = document.querySelector(`[name="${key}"]`);
                            if (field) {
                                field.value = config[key];
                                console.log('填充字段:', key, '=', config[key]);
                            }
                        });
                    } catch (e) {
                        console.error('解析配置失败:', e);
                    }
                }
            }
        })
        .catch(error => {
            console.error('加载配置失败:', error);
        });
}

// 保存支付配置
function savePaymentConfig() {
    var formData = new FormData(document.getElementById('paymentConfigForm'));
    var configData = {};

    for (var pair of formData.entries()) {
        configData[pair[0]] = pair[1];
    }

    var requestData = {
        code: window.currentConfigCode,
        config: configData
    };

    // 验证数据
    if (!window.currentConfigCode) {
        alert('错误：未选择支付方式，请重新打开配置窗口');
        return;
    }

    console.log('保存配置 - 支付方式:', window.currentConfigCode);
    console.log('保存配置 - 数据:', requestData);

    fetch('/test/payment-config-save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            alert('配置保存成功！');
            closeConfigModal();
        } else {
            alert('保存失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        alert('保存失败：网络错误');
    });
}

// 测试当前支付配置
function testCurrentPaymentConfig() {
    if (!window.currentConfigCode) {
        alert('请先选择支付方式');
        return;
    }

    testPaymentGateway(window.currentConfigCode);
}

// 生成当前支付方式的环境变量
function generatePaymentEnv() {
    var formData = new FormData(document.getElementById('paymentConfigForm'));
    var envContent = '# ' + window.currentConfigCode.toUpperCase() + ' 配置\\n';

    for (var pair of formData.entries()) {
        if (pair[1]) {
            if (pair[0].includes('private_key') || pair[0].includes('public_key')) {
                envContent += pair[0].toUpperCase() + '="' + pair[1].replace(/\\n/g, '\\\\n') + '"\\n';
            } else {
                envContent += pair[0].toUpperCase() + '=' + pair[1] + '\\n';
            }
        }
    }

    navigator.clipboard.writeText(envContent).then(function() {
        alert('环境变量配置已复制到剪贴板！\\n\\n请将内容添加到项目根目录的 .env 文件中。');
    }).catch(function() {
        var textArea = document.createElement('textarea');
        textArea.value = envContent;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('环境变量配置已复制到剪贴板！\\n\\n请将内容添加到项目根目录的 .env 文件中。');
    });
}

// 显示支付方式详情
function showPaymentInfo(code) {
    var tutorials = {
        'alipay': {
            name: '支付宝',
            description: '中国最大的第三方支付平台',
            steps: [
                '1. 访问支付宝开放平台：https://open.alipay.com',
                '2. 注册开发者账户并完成实名认证',
                '3. 创建应用，选择"网页&移动应用"',
                '4. 填写应用信息（名称、图标、描述等）',
                '5. 配置应用功能（选择支付功能）',
                '6. 上传企业资质（营业执照、法人身份证等）',
                '7. 等待审核通过（通常3-5个工作日）',
                '8. 获取 APPID 和配置密钥对'
            ],
            config: [
                'ALIPAY_APP_ID=2021xxxxxxxxxxxxxxxxx',
                'ALIPAY_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----"',
                'ALIPAY_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\\n...\\n-----END PUBLIC KEY-----"',
                'ALIPAY_ENV=sandbox'
            ],
            notes: [
                '• 需要中国大陆企业资质才能申请',
                '• 支持扫码支付、网页支付、APP支付等',
                '• 手续费：0.55%-1.2%（根据行业不同）',
                '• 结算时间：T+1',
                '• 提供花呗分期、余额支付等特色功能'
            ]
        },
        'wechat_pay': {
            name: '微信支付',
            description: '腾讯旗下支付平台',
            steps: [
                '1. 访问微信支付商户平台：https://pay.weixin.qq.com',
                '2. 点击"立即接入"开始申请',
                '3. 选择主体类型（企业/个体工商户）',
                '4. 填写企业信息和联系人信息',
                '5. 上传资质文件（营业执照、开户许可证等）',
                '6. 签署协议并等待审核',
                '7. 审核通过后获得商户号',
                '8. 下载商户证书和配置API密钥'
            ],
            config: [
                'WECHAT_APP_ID=wxxxxxxxxxxxxxxxxxxx',
                'WECHAT_MCH_ID=1234567890',
                'WECHAT_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                'WECHAT_CERT_PATH=/path/to/apiclient_cert.pem',
                'WECHAT_KEY_PATH=/path/to/apiclient_key.pem'
            ],
            notes: [
                '• 需要中国大陆企业资质',
                '• 支持扫码支付、小程序支付、公众号支付',
                '• 手续费：0.6%',
                '• 结算时间：T+1',
                '• 支持刷脸支付等新技术'
            ]
        },
        'unionpay': {
            name: '银联支付',
            description: '中国银联官方支付平台',
            steps: [
                '1. 访问银联商务官网：https://www.unionpay.com',
                '2. 联系当地银联商务分公司',
                '3. 提交入网申请资料',
                '4. 签署银联卡收单协议',
                '5. 等待资质审核',
                '6. 获得商户号和终端号',
                '7. 下载开发文档和证书',
                '8. 进行技术对接测试'
            ],
            config: [
                'UNIONPAY_MER_ID=777290058110048',
                'UNIONPAY_CERT_ID=68759585097',
                'UNIONPAY_PRIVATE_KEY_PATH=/path/to/private.key',
                'UNIONPAY_ENV=test'
            ],
            notes: [
                '• 支持所有银联卡支付',
                '• 手续费：0.5%-0.8%',
                '• 结算时间：T+1',
                '• 支持云闪付、手机银行等',
                '• 安全性最高的支付方式'
            ]
        },
        'ping_plus': {
            name: 'Ping++',
            description: '聚合支付平台',
            steps: [
                '1. 访问 Ping++ 官网：https://www.pingxx.com',
                '2. 注册开发者账户',
                '3. 创建应用',
                '4. 选择需要的支付渠道',
                '5. 配置各渠道参数',
                '6. 获取 API Key 和 App ID',
                '7. 下载 SDK 和文档',
                '8. 进行集成测试'
            ],
            config: [
                'PINGPP_API_KEY=sk_test_xxxxxxxxxxxxxxxxxxxxxxxxxx',
                'PINGPP_APP_ID=app_xxxxxxxxxxxxxxxxxx',
                'PINGPP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----\\n...\\n-----END RSA PRIVATE KEY-----"'
            ],
            notes: [
                '• 一次接入支持多种支付方式',
                '• 费率按各渠道标准收取',
                '• 提供统一的API接口',
                '• 简化开发和维护成本',
                '• 支持实时对账和数据分析'
            ]
        },
        'bank_transfer': {
            name: '银行转账',
            description: '传统银行转账支付方式',
            steps: [
                '1. 准备企业银行账户信息',
                '2. 确认银行账户可以正常收款',
                '3. 准备详细的银行信息（银行名称、账户名、账号等）',
                '4. 如需支持国际汇款，准备SWIFT代码',
                '5. 制定转账确认流程',
                '6. 配置客户转账说明',
                '7. 测试转账流程',
                '8. 启用银行转账支付方式'
            ],
            config: [
                'BANK_NAME=中国工商银行',
                'ACCOUNT_NAME=公司名称',
                'ACCOUNT_NUMBER=1234567890123456789',
                'BANK_ADDRESS=北京市朝阳区xxx支行',
                'SWIFT_CODE=ICBKCNBJ（可选）',
                'SUPPORTED_CURRENCIES=CNY,USD,EUR',
                'CONTACT_EMAIL=<EMAIL>',
                'CONTACT_WECHAT=your_wechat_id',
                'CONTACT_PHONE=+86 138-0000-0000',
                'SERVICE_HOURS=工作时间 9:00-18:00'
            ],
            notes: [
                '• 适合大额支付和B2B交易',
                '• 需要人工确认到账',
                '• 支持多种货币',
                '• 银行手续费由客户承担',
                '• 到账时间：1-3个工作日',
                '• 建议提供详细的转账说明'
            ]
        }
    };

    var tutorial = tutorials[code];
    if (!tutorial) {
        alert('暂无该支付方式的详细教程');
        return;
    }

    showTutorialModal(tutorial);
}

// 显示教程弹窗
function showTutorialModal(tutorial) {
    document.getElementById('tutorialTitle').textContent = tutorial.name + ' 对接申请教程';
    document.getElementById('tutorialDescription').textContent = tutorial.description;

    // 填充申请步骤
    var stepsContainer = document.getElementById('tutorialSteps');
    stepsContainer.innerHTML = '';
    tutorial.steps.forEach(function(step) {
        var li = document.createElement('li');
        li.textContent = step;
        stepsContainer.appendChild(li);
    });

    // 填充配置信息
    var configContainer = document.getElementById('tutorialConfig');
    configContainer.textContent = tutorial.config.join('\\n');

    // 填充重要提示
    var notesContainer = document.getElementById('tutorialNotes');
    notesContainer.innerHTML = '';
    tutorial.notes.forEach(function(note) {
        var li = document.createElement('li');
        li.textContent = note;
        notesContainer.appendChild(li);
    });

    // 显示弹窗
    document.getElementById('tutorialModal').style.display = 'block';

    // 保存当前配置到全局变量，供复制功能使用
    window.currentConfig = tutorial.config.join('\\n');
}

// 关闭教程弹窗
function closeTutorialModal() {
    document.getElementById('tutorialModal').style.display = 'none';
}

// 复制配置到剪贴板
function copyConfig() {
    if (window.currentConfig) {
        navigator.clipboard.writeText(window.currentConfig).then(function() {
            alert('配置已复制到剪贴板！');
        }).catch(function() {
            var textArea = document.createElement('textarea');
            textArea.value = window.currentConfig;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('配置已复制到剪贴板！');
        });
    }
}

// 显示配置指南
function showConfigGuide() {
    var guide = '国内支付配置指南：\\n\\n';
    guide += '1. 选择支付方式：根据用户群体选择合适的支付方式\\n';
    guide += '2. 申请商户账号：按照详情教程申请相应的商户账号\\n';
    guide += '3. 获取API密钥：在商户后台获取必要的配置参数\\n';
    guide += '4. 配置环境变量：将参数添加到.env文件中\\n';
    guide += '5. 测试连接：使用测试功能验证配置是否正确\\n';
    guide += '6. 启用支付方式：确认无误后启用支付方式\\n\\n';
    guide += '推荐配置顺序：\\n';
    guide += '• 支付宝 + 微信支付（覆盖95%用户）\\n';
    guide += '• 银联支付（银行卡用户）\\n';
    guide += '• Ping++（聚合支付，简化开发）';

    alert(guide);
}

// ==================== 线下扫码支付管理 ====================

// 加载线下扫码支付列表
function loadOfflineQrPayments() {
    fetch('/strongadmin/domestic-payment/offline-qr-data')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                renderOfflineQrList(data.data);
            } else {
                document.getElementById('offline-qr-list').innerHTML =
                    '<div style="text-align: center; padding: 40px; color: #f56c6c;">加载失败：' + data.message + '</div>';
            }
        })
        .catch(error => {
            console.error('加载线下扫码支付失败:', error);
            document.getElementById('offline-qr-list').innerHTML =
                '<div style="text-align: center; padding: 40px; color: #f56c6c;">网络错误，请稍后重试</div>';
        });
}

// 渲染线下扫码支付列表
function renderOfflineQrList(payments) {
    const container = document.getElementById('offline-qr-list');

    if (!payments || payments.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #999;">
                <i class="layui-icon layui-icon-face-cry" style="font-size: 30px;"></i>
                <p>暂无线下扫码支付方式</p>
                <button type="button" class="layui-btn layui-btn-normal" onclick="addOfflineQrPayment()">
                    <i class="layui-icon layui-icon-add-1"></i> 添加第一个支付方式
                </button>
            </div>
        `;
        return;
    }

    let html = '<div class="layui-row layui-col-space15">';

    payments.forEach(payment => {
        const statusClass = payment.status ? 'layui-bg-green' : 'layui-bg-gray';
        const statusText = payment.status ? '已启用' : '已禁用';
        const coverImage = payment.cover_image ?
            `<img src="${payment.cover_image}" style="width: 60px; height: 60px; border-radius: 4px; object-fit: cover;">` :
            '<div style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #999;"><i class="layui-icon layui-icon-picture" style="font-size: 20px;"></i></div>';

        html += `
            <div class="layui-col-md6">
                <div class="layui-card" style="margin-bottom: 15px;">
                    <div class="layui-card-body">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="flex-shrink: 0;">
                                ${coverImage}
                            </div>
                            <div style="flex: 1;">
                                <h4 style="margin: 0 0 5px 0; color: #333;">
                                    <i class="fas fa-qrcode"></i> ${payment.name}
                                </h4>
                                <p style="margin: 0 0 10px 0; color: #666; font-size: 13px;">
                                    ${payment.description ? payment.description.substring(0, 50) + '...' : '暂无描述'}
                                </p>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <span class="layui-badge ${statusClass}">${statusText}</span>
                                    <span style="color: #999; font-size: 12px;">排序: ${payment.sort_order}</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 10px; margin-top: 5px;">
                                    ${payment.cover_image ? '<span style="color: #5FB878; font-size: 12px;"><i class="layui-icon layui-icon-picture"></i> 已上传封面</span>' : '<span style="color: #FF5722; font-size: 12px;"><i class="layui-icon layui-icon-picture"></i> 未上传封面</span>'}
                                    ${payment.qr_image ? '<span style="color: #5FB878; font-size: 12px;"><i class="layui-icon layui-icon-picture"></i> 已上传二维码</span>' : '<span style="color: #FF5722; font-size: 12px;"><i class="layui-icon layui-icon-picture"></i> 未上传二维码</span>'}
                                </div>
                            </div>
                            <div style="flex-shrink: 0;">
                                <div style="display: flex; flex-direction: column; gap: 8px; align-items: flex-end;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="font-size: 12px; color: #666;">启用:</span>
                                        <input type="checkbox" class="offline-qr-child-switch" data-payment-id="${payment.id}"
                                               lay-skin="switch" lay-text="ON|OFF" lay-filter="offlineQrChildSwitch"
                                               ${payment.status ? 'checked' : ''}>
                                    </div>
                                    <div class="layui-btn-group">
                                        <button class="layui-btn layui-btn-sm layui-btn-primary" onclick="editOfflineQrPayment(${payment.id})">
                                            编辑
                                        </button>
                                        <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="deleteOfflineQrPayment(${payment.id})">
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;

    // 重新渲染layui表单组件
    layui.use('form', function(){
        var form = layui.form;
        form.render();

        // 监听子开关变化
        form.on('switch(offlineQrChildSwitch)', function(data){
            var paymentId = data.elem.getAttribute('data-payment-id');
            var isEnabled = data.elem.checked;
            toggleOfflineQrStatus(paymentId, isEnabled ? 1 : 0);
        });
    });

    // 检查主开关状态，更新子开关可用性
    var masterSwitch = document.getElementById('offlineQrMasterSwitch');
    if (masterSwitch && !masterSwitch.checked) {
        updateChildSwitchesState(false);
    }
}

// 刷新线下扫码支付列表
function refreshOfflineQrList() {
    document.getElementById('offline-qr-list').innerHTML =
        '<div style="text-align: center; padding: 40px; color: #999;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i><p>正在刷新...</p></div>';
    loadOfflineQrPayments();
}

// 添加线下扫码支付方式
function addOfflineQrPayment() {
    // 创建简单的添加表单弹窗
    layer.open({
        type: 1,
        title: '添加线下扫码支付',
        area: ['600px', '500px'],
        content: `
            <div style="padding: 20px;">
                <form id="addOfflineQrForm" enctype="multipart/form-data">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">支付方式名称 *</label>
                        <input type="text" name="name" placeholder="如：支付宝付款码" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">封面图片 *</label>
                        <input type="file" name="cover_image" accept="image/*" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">请上传支付方式的封面图片，如支付宝LOGO（支持JPG、PNG、GIF格式，最大2MB）</div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">二维码图片 *</label>
                        <input type="file" name="qr_image" accept="image/*" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">请上传您的收款二维码图片（支持JPG、PNG、GIF格式，最大2MB）</div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">支付说明</label>
                        <textarea name="description" placeholder="付款时候记得备注协商订单号，以备我们客服核实否者无法确定您支付的金额，切记切记！" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 80px;"></textarea>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">状态</label>
                        <select name="status" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px;">排序</label>
                        <input type="number" name="sort_order" value="0" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" placeholder="数字越小排序越靠前">
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" style="background: #1E9FFF; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin-right: 10px;">保存</button>
                        <button type="button" onclick="layer.closeAll()" style="background: #ccc; color: white; padding: 10px 20px; border: none; border-radius: 4px;">取消</button>
                    </div>
                </form>
            </div>
        `,
        success: function() {
            // 绑定表单提交事件
            document.getElementById('addOfflineQrForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // 显示加载提示
                const loadingIndex = layer.load(1, {shade: [0.1,'#fff']});

                const formData = new FormData(this);

                // 调试：打印FormData内容
                console.log('FormData内容:');
                for (let pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                fetch('/strongadmin/domestic-payment/offline-qr-create', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    layer.close(loadingIndex);
                    console.log('响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('响应数据:', data);
                    if (data.code === 200) {
                        layer.msg('添加成功', {icon: 1});
                        layer.closeAll();
                        loadOfflineQrPayments(); // 重新加载列表
                    } else {
                        layer.msg('添加失败：' + data.message, {icon: 2});
                    }
                })
                .catch(error => {
                    layer.close(loadingIndex);
                    console.error('添加失败详情:', error);
                    layer.msg('网络错误：' + error.message, {icon: 2});
                });
            });
        }
    });
}

// 编辑线下扫码支付方式
function editOfflineQrPayment(id) {
    // 先获取当前数据
    fetch(`/strongadmin/domestic-payment/offline-qr-get/${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                const payment = data.data;

                // 创建编辑表单弹窗
                layer.open({
                    type: 1,
                    title: '编辑线下扫码支付',
                    area: ['600px', '500px'],
                    content: `
                        <div style="padding: 20px;">
                            <form id="editOfflineQrForm" enctype="multipart/form-data">
                                <input type="hidden" name="id" value="${payment.id}">
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px;">支付方式名称 *</label>
                                    <input type="text" name="name" value="${payment.name}" placeholder="如：支付宝付款码" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px;">当前封面图</label>
                                    ${payment.cover_image ? `<img src="${payment.cover_image}" style="width: 100px; height: 100px; border: 1px solid #ddd; border-radius: 4px; object-fit: cover; margin-bottom: 10px;">` : '<div style="color: #999;">暂无封面图</div>'}
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px;">更换封面图片</label>
                                    <input type="file" name="cover_image" accept="image/*" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <div style="font-size: 12px; color: #666; margin-top: 5px;">如需更换封面图，请选择新图片（支持JPG、PNG、GIF格式，最大2MB）</div>
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px;">当前二维码</label>
                                    ${payment.qr_image ? `<img src="${payment.qr_image}" style="width: 100px; height: 100px; border: 1px solid #ddd; border-radius: 4px; object-fit: cover; margin-bottom: 10px;">` : '<div style="color: #999;">暂无二维码</div>'}
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px;">更换二维码图片</label>
                                    <input type="file" name="qr_image" accept="image/*" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <div style="font-size: 12px; color: #666; margin-top: 5px;">如需更换二维码，请选择新图片（支持JPG、PNG、GIF格式，最大2MB）</div>
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px;">支付说明</label>
                                    <textarea name="description" placeholder="付款时候记得备注协商订单号..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 80px;">${payment.description || ''}</textarea>
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px;">状态</label>
                                    <select name="status" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                        <option value="1" ${payment.status == 1 ? 'selected' : ''}>启用</option>
                                        <option value="0" ${payment.status == 0 ? 'selected' : ''}>禁用</option>
                                    </select>
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <label style="display: block; margin-bottom: 5px;">排序</label>
                                    <input type="number" name="sort_order" value="${payment.sort_order || 0}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" placeholder="数字越小排序越靠前">
                                </div>
                                <div style="text-align: center; margin-top: 20px;">
                                    <button type="submit" style="background: #1E9FFF; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin-right: 10px;">保存</button>
                                    <button type="button" onclick="layer.closeAll()" style="background: #ccc; color: white; padding: 10px 20px; border: none; border-radius: 4px;">取消</button>
                                </div>
                            </form>
                        </div>
                    `,
                    success: function() {
                        // 绑定表单提交事件
                        document.getElementById('editOfflineQrForm').addEventListener('submit', function(e) {
                            e.preventDefault();

                            // 显示加载提示
                            const loadingIndex = layer.load(1, {shade: [0.1,'#fff']});

                            const formData = new FormData(this);

                            // 调试：打印FormData内容
                            console.log('FormData内容:');
                            for (let pair of formData.entries()) {
                                console.log(pair[0] + ': ' + pair[1]);
                            }

                            fetch(`/strongadmin/domestic-payment/offline-qr-update/${id}`, {
                                method: 'POST',
                                body: formData,
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                }
                                // 不设置Content-Type，让浏览器自动设置multipart/form-data
                            })
                            .then(response => {
                                layer.close(loadingIndex);
                                console.log('响应状态:', response.status);
                                if (!response.ok) {
                                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                                }
                                return response.json();
                            })
                            .then(data => {
                                console.log('响应数据:', data);
                                if (data.code === 200) {
                                    layer.msg('更新成功', {icon: 1});
                                    layer.closeAll();
                                    loadOfflineQrPayments(); // 重新加载列表
                                } else {
                                    layer.msg('更新失败：' + data.message, {icon: 2});
                                }
                            })
                            .catch(error => {
                                layer.close(loadingIndex);
                                console.error('更新失败详情:', error);
                                layer.msg('网络错误：' + error.message, {icon: 2});
                            });
                        });
                    }
                });
            } else {
                layer.msg('获取数据失败：' + data.message, {icon: 2});
            }
        })
        .catch(error => {
            console.error('获取数据失败:', error);
            layer.msg('网络错误，请稍后重试', {icon: 2});
        });
}

// 切换线下扫码支付状态
function toggleOfflineQrStatus(id, status) {
    fetch('/strongadmin/domestic-payment/offline-qr-status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            id: id,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            layer.msg(data.message, {icon: 1});
            loadOfflineQrPayments(); // 重新加载列表
        } else {
            layer.msg('操作失败：' + data.message, {icon: 2});
        }
    })
    .catch(error => {
        console.error('状态切换失败:', error);
        layer.msg('网络错误，请稍后重试', {icon: 2});
    });
}

// 删除线下扫码支付方式
function deleteOfflineQrPayment(id) {
    layer.confirm('确定要删除这个支付方式吗？', {
        btn: ['确定', '取消']
    }, function(index) {
        fetch(`/strongadmin/domestic-payment/offline-qr-delete/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                layer.msg('删除成功', {icon: 1});
                loadOfflineQrPayments(); // 重新加载列表
            } else {
                layer.msg('删除失败：' + data.message, {icon: 2});
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            layer.msg('网络错误，请稍后重试', {icon: 2});
        });
        layer.close(index);
    });
}

// 点击弹窗外部关闭
document.addEventListener('DOMContentLoaded', function() {
    window.onclick = function(event) {
        var tutorialModal = document.getElementById('tutorialModal');
        var configModal = document.getElementById('configModal');

        if (event.target == tutorialModal) {
            closeTutorialModal();
        }
        if (event.target == configModal) {
            closeConfigModal();
        }
    };
});

// ==================== 线下扫码支付主开关管理 ====================

// 初始化主开关
function initOfflineQrMasterSwitch() {
    // 加载主开关状态
    loadOfflineQrMasterStatus();

    // 监听主开关变化
    layui.use('form', function(){
        var form = layui.form;

        // 监听主开关
        form.on('switch(offlineQrMasterSwitch)', function(data){
            var isEnabled = data.elem.checked;
            updateOfflineQrMasterStatus(isEnabled);
        });
    });
}

// 加载主开关状态
function loadOfflineQrMasterStatus() {
    fetch('/strongadmin/domestic-payment/offline-qr-master-status')
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                var masterSwitch = document.getElementById('offlineQrMasterSwitch');
                masterSwitch.checked = data.data.enabled;

                // 重新渲染layui表单
                layui.use('form', function(){
                    layui.form.render('checkbox');
                });

                // 更新子开关状态
                updateChildSwitchesState(data.data.enabled);
            }
        })
        .catch(error => {
            console.error('加载主开关状态失败:', error);
        });
}

// 更新主开关状态
function updateOfflineQrMasterStatus(enabled) {
    fetch('/strongadmin/domestic-payment/offline-qr-master-status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            enabled: enabled
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            layer.msg(enabled ? '线下扫码支付已启用' : '线下扫码支付已禁用', {icon: 1});

            // 更新子开关状态
            updateChildSwitchesState(enabled);

            // 刷新列表
            loadOfflineQrPayments();
        } else {
            layer.msg('操作失败：' + data.message, {icon: 2});

            // 恢复开关状态
            var masterSwitch = document.getElementById('offlineQrMasterSwitch');
            masterSwitch.checked = !enabled;
            layui.use('form', function(){
                layui.form.render('checkbox');
            });
        }
    })
    .catch(error => {
        console.error('更新主开关状态失败:', error);
        layer.msg('网络错误，请稍后重试', {icon: 2});

        // 恢复开关状态
        var masterSwitch = document.getElementById('offlineQrMasterSwitch');
        masterSwitch.checked = !enabled;
        layui.use('form', function(){
            layui.form.render('checkbox');
        });
    });
}

// 更新子开关状态
function updateChildSwitchesState(masterEnabled) {
    // 更新所有子支付方式的开关状态
    var childSwitches = document.querySelectorAll('.offline-qr-child-switch');
    childSwitches.forEach(function(switchElem) {
        if (!masterEnabled) {
            // 主开关禁用时，禁用所有子开关
            switchElem.disabled = true;
            switchElem.checked = false;
        } else {
            // 主开关启用时，恢复子开关的可用性
            switchElem.disabled = false;
        }
    });

    // 重新渲染layui表单
    layui.use('form', function(){
        layui.form.render('checkbox');
    });

    // 更新添加按钮状态
    var addButton = document.querySelector('button[onclick="addOfflineQrPayment()"]');
    if (addButton) {
        if (masterEnabled) {
            addButton.disabled = false;
            addButton.classList.remove('layui-btn-disabled');
        } else {
            addButton.disabled = true;
            addButton.classList.add('layui-btn-disabled');
        }
    }
}

</script>
@endsection
