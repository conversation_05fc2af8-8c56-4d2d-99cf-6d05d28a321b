<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一支付管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <style>
        body {
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .main-header {
            background: #343a40;
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        .card-header {
            background: #fff;
            border-bottom: 1px solid #e9ecef;
            padding: 1.25rem;
        }
        .info-box {
            background: #fff;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .info-box-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            float: left;
            margin-right: 1rem;
        }
        .info-box-content {
            overflow: hidden;
        }
        .info-box-text {
            display: block;
            font-size: 0.875rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }
        .info-box-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 600;
            color: #495057;
        }
        .bg-info { background-color: #17a2b8 !important; }
        .bg-success { background-color: #28a745 !important; }
        .bg-warning { background-color: #ffc107 !important; }
        .bg-danger { background-color: #dc3545 !important; }
        .table {
            background: white;
        }
        .badge {
            font-size: 0.75rem;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .payment-logo {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-size: 1rem;
        }
        .status-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .status-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #28a745;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container-fluid">
            <h1><i class="fas fa-credit-card"></i> 统一支付管理</h1>
        </div>
    </div>

    <div class="container-fluid"
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-credit-card"></i>
                        统一支付管理
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-primary" onclick="showCreateModal()">
                            <i class="fas fa-plus"></i> 添加支付方式
                        </button>
                        <button type="button" class="btn btn-sm btn-info" onclick="refreshData()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 统计信息 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-credit-card"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总支付方式</span>
                                    <span class="info-box-number" id="total-payments">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">已启用</span>
                                    <span class="info-box-number" id="enabled-payments">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-history"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">原有系统</span>
                                    <span class="info-box-number" id="legacy-payments">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary"><i class="fas fa-globe"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">国际支付</span>
                                    <span class="info-box-number" id="international-payments">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 支付方式列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="payments-table">
                            <thead>
                                <tr>
                                    <th width="60">排序</th>
                                    <th>名称</th>
                                    <th>代码</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>配置</th>
                                    <th>描述</th>
                                    <th>更新时间</th>
                                    <th width="200">操作</th>
                                </tr>
                            </thead>
                            <tbody id="payments-tbody">
                                <!-- 数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置模态框 -->
<div class="modal fade" id="configModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">支付方式配置</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="config-form">
                    <input type="hidden" id="config-payment-id">
                    <div id="config-fields">
                        <!-- 配置字段将动态生成 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" onclick="testGateway()">
                    <i class="fas fa-vial"></i> 测试连接
                </button>
                <button type="button" class="btn btn-primary" onclick="saveConfig()">
                    <i class="fas fa-save"></i> 保存配置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 创建支付方式模态框 -->
<div class="modal fade" id="createModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">添加支付方式</h4>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="create-form">
                    <div class="form-group">
                        <label>支付方式名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="create-name" placeholder="如：微信支付">
                    </div>
                    <div class="form-group">
                        <label>支付代码 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="create-code" placeholder="如：wechatpay">
                        <small class="form-text text-muted">英文字母和下划线，用于系统识别</small>
                    </div>
                    <div class="form-group">
                        <label>描述</label>
                        <textarea class="form-control" id="create-description" rows="3" placeholder="支付方式描述"></textarea>
                    </div>
                    <div class="form-group">
                        <label>排序</label>
                        <input type="number" class="form-control" id="create-sort" value="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createPayment()">
                    <i class="fas fa-plus"></i> 创建
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
let paymentsData = [];

$(document).ready(function() {
    loadPayments();
});

function loadPayments() {
    $.get('{{ url("strongadmin/unified-payment/data") }}', function(response) {
        if (response.code === 200) {
            paymentsData = response.data;
            renderPaymentsTable();
            updateStatistics();
        } else {
            toastr.error(response.message || '加载失败');
        }
    }).fail(function() {
        toastr.error('网络错误');
    });
}

function renderPaymentsTable() {
    const tbody = $('#payments-tbody');
    tbody.empty();
    
    paymentsData.forEach(function(payment) {
        const typeClass = getTypeClass(payment.type);
        const statusClass = payment.status ? 'badge-success' : 'badge-secondary';
        const configClass = getConfigClass(payment.config_status);
        
        const row = `
            <tr>
                <td>
                    <input type="number" class="form-control form-control-sm" 
                           value="${payment.sort_order}" 
                           onchange="updateSort(${payment.id}, this.value)"
                           style="width: 60px;">
                </td>
                <td>
                    <strong>${payment.name}</strong>
                    ${payment.code === 'paypal' || payment.code === 'paypal_new' ? 
                        '<span class="badge badge-warning badge-sm ml-1">PayPal</span>' : ''}
                </td>
                <td><code>${payment.code}</code></td>
                <td><span class="badge ${typeClass}">${getTypeText(payment.type)}</span></td>
                <td>
                    <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input" 
                               id="status-${payment.id}" 
                               ${payment.status ? 'checked' : ''}
                               onchange="toggleStatus(${payment.id}, this.checked)">
                        <label class="custom-control-label" for="status-${payment.id}"></label>
                    </div>
                </td>
                <td><span class="badge ${configClass}">${getConfigText(payment.config_status)}</span></td>
                <td>${payment.description || '-'}</td>
                <td>${payment.updated_at || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="showConfigModal(${payment.id})" title="配置">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="testGatewayDirect(${payment.id})" title="测试">
                        <i class="fas fa-vial"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deletePayment(${payment.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function updateStatistics() {
    const total = paymentsData.length;
    const enabled = paymentsData.filter(p => p.status).length;
    const legacy = paymentsData.filter(p => p.type === 'legacy').length;
    const international = paymentsData.filter(p => p.type === 'international').length;
    
    $('#total-payments').text(total);
    $('#enabled-payments').text(enabled);
    $('#legacy-payments').text(legacy);
    $('#international-payments').text(international);
}

function getTypeClass(type) {
    const classes = {
        'legacy': 'badge-warning',
        'international': 'badge-primary',
        'custom': 'badge-info'
    };
    return classes[type] || 'badge-secondary';
}

function getTypeText(type) {
    const texts = {
        'legacy': '原有系统',
        'international': '国际支付',
        'custom': '自定义'
    };
    return texts[type] || '未知';
}

function getConfigClass(status) {
    const classes = {
        'configured': 'badge-success',
        'partial': 'badge-warning',
        'unconfigured': 'badge-danger'
    };
    return classes[status] || 'badge-secondary';
}

function getConfigText(status) {
    const texts = {
        'configured': '已配置',
        'partial': '部分配置',
        'unconfigured': '未配置'
    };
    return texts[status] || '未知';
}

function toggleStatus(id, checked) {
    const status = checked ? 1 : 0;
    
    $.post('{{ url("strongadmin/unified-payment/update-status") }}', {
        id: id,
        status: status,
        _token: '{{ csrf_token() }}'
    }, function(response) {
        if (response.code === 200) {
            toastr.success(response.message);
            loadPayments(); // 重新加载以更新PayPal状态
        } else {
            toastr.error(response.message);
            loadPayments(); // 恢复状态
        }
    }).fail(function() {
        toastr.error('网络错误');
        loadPayments();
    });
}

function updateSort(id, sortOrder) {
    $.post('{{ url("strongadmin/unified-payment/update-sort") }}', {
        id: id,
        sort_order: sortOrder,
        _token: '{{ csrf_token() }}'
    }, function(response) {
        if (response.code === 200) {
            toastr.success(response.message);
        } else {
            toastr.error(response.message);
        }
    });
}

function showConfigModal(id) {
    $.get('{{ url("strongadmin/unified-payment/get-config") }}', {id: id}, function(response) {
        if (response.code === 200) {
            const data = response.data;
            $('#config-payment-id').val(data.id);
            $('.modal-title').text(data.name + ' - 配置');
            
            generateConfigFields(data.config_template, data.config);
            $('#configModal').modal('show');
        } else {
            toastr.error(response.message);
        }
    });
}

function generateConfigFields(template, config) {
    const container = $('#config-fields');
    container.empty();
    
    if (Object.keys(template).length === 0) {
        container.html('<p class="text-muted">此支付方式暂无可配置项</p>');
        return;
    }
    
    Object.keys(template).forEach(function(key) {
        const field = template[key];
        const value = config[key] || field.default || '';
        
        let input = '';
        if (field.type === 'select') {
            input = `<select class="form-control" name="${key}">`;
            Object.keys(field.options).forEach(function(optKey) {
                const selected = value === optKey ? 'selected' : '';
                input += `<option value="${optKey}" ${selected}>${field.options[optKey]}</option>`;
            });
            input += '</select>';
        } else if (field.type === 'textarea') {
            input = `<textarea class="form-control" name="${key}" rows="3">${value}</textarea>`;
        } else if (field.type === 'password') {
            input = `<input type="password" class="form-control" name="${key}" value="${value}">`;
        } else {
            input = `<input type="text" class="form-control" name="${key}" value="${value}">`;
        }
        
        const required = field.required ? '<span class="text-danger">*</span>' : '';
        
        container.append(`
            <div class="form-group">
                <label>${field.label} ${required}</label>
                ${input}
            </div>
        `);
    });
}

function saveConfig() {
    const id = $('#config-payment-id').val();
    const formData = {};
    
    $('#config-fields input, #config-fields select, #config-fields textarea').each(function() {
        formData[$(this).attr('name')] = $(this).val();
    });
    
    $.post('{{ url("strongadmin/unified-payment/save-config") }}', {
        id: id,
        config: formData,
        _token: '{{ csrf_token() }}'
    }, function(response) {
        if (response.code === 200) {
            toastr.success(response.message);
            $('#configModal').modal('hide');
            loadPayments();
        } else {
            toastr.error(response.message);
        }
    });
}

function testGateway() {
    const id = $('#config-payment-id').val();
    
    $.post('{{ url("strongadmin/unified-payment/test-gateway") }}', {
        id: id,
        _token: '{{ csrf_token() }}'
    }, function(response) {
        if (response.code === 200) {
            toastr.success('测试成功: ' + response.data.message);
        } else {
            toastr.error(response.message);
        }
    });
}

function testGatewayDirect(id) {
    $.post('{{ url("strongadmin/unified-payment/test-gateway") }}', {
        id: id,
        _token: '{{ csrf_token() }}'
    }, function(response) {
        if (response.code === 200) {
            toastr.success('测试成功: ' + response.data.message);
        } else {
            toastr.error(response.message);
        }
    });
}

function showCreateModal() {
    $('#create-form')[0].reset();
    $('#createModal').modal('show');
}

function createPayment() {
    const data = {
        name: $('#create-name').val(),
        code: $('#create-code').val(),
        description: $('#create-description').val(),
        sort_order: $('#create-sort').val(),
        _token: '{{ csrf_token() }}'
    };
    
    $.post('{{ url("strongadmin/unified-payment/create") }}', data, function(response) {
        if (response.code === 200) {
            toastr.success(response.message);
            $('#createModal').modal('hide');
            loadPayments();
        } else {
            toastr.error(response.message);
        }
    });
}

function deletePayment(id) {
    if (!confirm('确定要删除这个支付方式吗？')) {
        return;
    }
    
    $.post('{{ url("strongadmin/unified-payment/delete") }}', {
        id: id,
        _token: '{{ csrf_token() }}'
    }, function(response) {
        if (response.code === 200) {
            toastr.success(response.message);
            loadPayments();
        } else {
            toastr.error(response.message);
        }
    });
}

function refreshData() {
    loadPayments();
    toastr.info('数据已刷新');
}
</script>
@endsection
