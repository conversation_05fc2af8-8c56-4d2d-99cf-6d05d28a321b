@extends('themes.default.layouts.app')

@section('content')
<div class="container" style="min-height: 400px; display: flex; align-items: center; justify-content: center;">
    <div style="text-align: center; max-width: 600px; padding: 40px;">
        <!-- 图标 -->
        <div style="font-size: 4rem; color: #ffc107; margin-bottom: 20px;">
            🛒
        </div>

        <!-- 标题 -->
        <h2 style="color: #333; margin-bottom: 15px; font-size: 1.8rem; font-weight: 600;">
            {{ $exception->getMessage() }}
        </h2>

        <!-- 描述 -->
        <p style="color: #666; margin-bottom: 30px; font-size: 1.1rem; line-height: 1.5;">
            您的购物车当前没有商品，请先添加商品到购物车再进行结账。
        </p>

        <!-- 按钮组 -->
        <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
            <a href="/" class="btn btn-primary" style="padding: 12px 24px; font-size: 1rem; border-radius: 6px; text-decoration: none; background: #007bff; color: white; border: none; transition: all 0.3s ease;">
                🏠 返回首页
            </a>
            <button onclick="goBack()" class="btn btn-secondary" style="padding: 12px 24px; font-size: 1rem; border-radius: 6px; background: #6c757d; color: white; border: none; cursor: pointer; transition: all 0.3s ease;">
                ← 返回上页
            </button>
            <a href="/products" class="btn btn-success" style="padding: 12px 24px; font-size: 1rem; border-radius: 6px; text-decoration: none; background: #28a745; color: white; border: none; transition: all 0.3s ease;">
                🛍️ 浏览商品
            </a>
        </div>
    </div>
</div>

<style>
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

@media (max-width: 768px) {
    .container > div {
        padding: 20px !important;
    }

    .container h2 {
        font-size: 1.5rem !important;
    }

    .container p {
        font-size: 1rem !important;
    }

    .container > div > div:last-child {
        flex-direction: column !important;
    }

    .btn {
        width: 100% !important;
        margin-bottom: 10px !important;
    }
}
</style>

<script>
function goBack() {
    // 检查是否有浏览历史
    if (window.history.length > 1) {
        window.history.back();
    } else {
        // 如果没有历史记录，跳转到首页
        window.location.href = '/';
    }
}
</script>
@endsection