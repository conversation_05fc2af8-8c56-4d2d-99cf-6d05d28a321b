@extends('themes.default.layouts.app')

@section('content')
@include('layouts.includes.breadcrumb')

<!--大气黑白风格结账页面-->
<div class="st-checkout-elegant">
    <div class="container">
        <!-- 页面标题 - 大气版 -->
        <div class="checkout-header-large">
            <h1 class="checkout-title-large">@lang('Checkout')</h1>
            <p class="checkout-subtitle">@lang('Complete your order securely')</p>
        </div>

        <form action="{{route('shoppingcart.createOrder')}}" method="post" id="ST-FORM">
            @csrf
            <input name="buyNow" type="hidden" value="{{request('buyNow',0)}}" />

            <!-- 隐藏的账单地址字段 - 当"账单地址与配送地址相同"时使用 -->
            <input type="hidden" name="billing_first_name" value="{{$default_address->first_name ?? ''}}" />
            <input type="hidden" name="billing_last_name" value="{{$default_address->last_name ?? ''}}" />
            <input type="hidden" name="billing_email" value="{{$default_address->email ?? ''}}" />
            <input type="hidden" name="billing_phone" value="{{$default_address->phone ?? '13800000000'}}" />
            <input type="hidden" name="billing_fax" value="{{$default_address->fax ?? ''}}" />
            <input type="hidden" name="billing_country_code" value="{{$default_address->country_code ?? ''}}" />
            <input type="hidden" name="billing_state_code" value="{{$default_address->state_code ?? ''}}" />
            <input type="hidden" name="billing_state_other" value="{{$default_address->state_other ?? 'N/A'}}" />
            <input type="hidden" name="billing_city" value="{{$default_address->city ?? ''}}" />
            <input type="hidden" name="billing_address_line_1" value="{{$default_address->address_line_1 ?? ''}}" />
            <input type="hidden" name="billing_address_line_2" value="{{$default_address->address_line_2 ?? ''}}" />
            <input type="hidden" name="billing_postal_code" value="{{$default_address->postal_code ?? ''}}" />

            <div class="row">
                <!-- 左侧：地址信息 - 大气版 -->
                <div class="col-md-8">
                    <!-- 配送地址 -->
                    <div class="checkout-panel-large">
                        <div class="panel-header-large">
                            <h2>📍 @lang('Shipping Address')</h2>
                        </div>
                        <div class="panel-content-large">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('Country')<span class="required-large">*</span></label>
                                        <select class="form-control-large" name="country_code">
                                            <option value="">@lang('Select your country')</option>
                                            @foreach($countries as $countryCode=>$country)
                                            <option value="{{$countryCode}}" @if($countryCode === ($default_address->country_code ?? '')) selected @endif>{{$country}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('First Name')<span class="required-large">*</span></label>
                                        <input type="text" name="first_name" class="form-control-large" placeholder="@lang('Enter your first name')" value="{{$default_address->first_name ?? ''}}" onchange="$('input[name=billing_first_name]').val(this.value)">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('Last Name')<span class="required-large">*</span></label>
                                        <input type="text" name="last_name" class="form-control-large" placeholder="@lang('Enter your last name')" value="{{$default_address->last_name ?? ''}}" onchange="$('input[name=billing_last_name]').val(this.value)">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('Email Address')</label>
                                        <input type="email" name="email" class="form-control-large" placeholder="@lang('Enter your email address')" value="{{$default_address->email ?? ''}}" onchange="$('input[name=billing_email]').val(this.value)">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('Phone Number')<span class="required-large">*</span></label>
                                        <input type="tel" name="phone" class="form-control-large" placeholder="@lang('Enter your phone number')" value="{{$default_address->phone ?? '13800000000'}}" onchange="$('input[name=billing_phone]').val(this.value)">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('Address Line 1')<span class="required-large">*</span></label>
                                        <input type="text" name="address_line_1" class="form-control-large" placeholder="@lang('Street address, P.O. box, company name')" value="{{$default_address->address_line_1 ?? ''}}" onchange="$('input[name=billing_address_line_1]').val(this.value)">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('Address Line 2')</label>
                                        <input type="text" name="address_line_2" class="form-control-large" placeholder="@lang('Apartment, suite, unit, building, floor')" value="{{$default_address->address_line_2 ?? ''}}" onchange="$('input[name=billing_address_line_2]').val(this.value)">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('Town/City')<span class="required-large">*</span></label>
                                        <input type="text" name="city" class="form-control-large" placeholder="@lang('City')" value="{{$default_address->city ?? ''}}" onchange="$('input[name=billing_city]').val(this.value)">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('State/Province')<span class="required-large">*</span></label>
                                        <select class="form-control-large" name="state_code" onchange="updateStateFields(this.value)">
                                            <option value="">@lang('Select state')</option>
                                            @foreach($states as $stateCode=>$state)
                                            <option value="{{$stateCode}}" @if($stateCode === ($default_address->state_code ?? '')) selected @endif>{{$state}}</option>
                                            @endforeach
                                        </select>
                                        <input type="hidden" name="state_other" value="{{$default_address->state_other ?? 'N/A'}}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group-large">
                                        <label class="label-large">@lang('Zip/Postal Code')<span class="required-large">*</span></label>
                                        <input type="text" name="postal_code" class="form-control-large" placeholder="@lang('Postal Code')" value="{{$default_address->postal_code ?? ''}}" onchange="$('input[name=billing_postal_code]').val(this.value)">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="checkbox-large">
                                        <input type="checkbox" name="billing_this_address" id="billingThisAddress" checked>
                                        <label for="billingThisAddress" class="checkbox-label-large">@lang('Billing address same as shipping address')</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 配送选项 -->
                    <div class="checkout-panel-large">
                        <div class="panel-header-large">
                            <h2>🚚 @lang('Shipping Options')</h2>
                        </div>
                        <div class="panel-content-large">
                            <div id="ST-SHIPPING_OPTIONS">
                                @include('layouts.includes.shippingOptions')
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：订单摘要和支付 -->
                <div class="col-md-4">
                    <!-- 订单摘要 -->
                    <div class="checkout-panel-large order-summary-large">
                        <div class="panel-header-large">
                            <h2>📋 @lang('Order Summary')</h2>
                        </div>
                        <div class="panel-content-large">
                            <!-- 购物车商品 -->
                            <div class="cart-items-large">
                                @foreach($cart['rows'] as $row)
                                <div class="cart-item-large">
                                    <div class="item-image-large">
                                        <img src="{{$row['product']['img_cover']}}" alt="{{$row['product']['title']}}">
                                        <span class="item-qty-large">{{$row['qty']}}</span>
                                    </div>
                                    <div class="item-details-large">
                                        <h4 class="item-title-large">{{$row['product']['title']}}</h4>
                                        <p class="item-sku-large">SKU: {{$row['product']['sku']}}</p>
                                        @if($row['product']['specs'])
                                        <p class="item-specs-large">
                                            @foreach($row['product']['specs'] as $spec)
                                            {{$spec['name']}}: {{$spec['pivot']['spec_value']}}@if(!$loop->last), @endif
                                            @endforeach
                                        </p>
                                        @endif
                                        <div class="item-price-large">{{$_current_currency_name}} {{$row['product_price_now']}}</div>
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            <!-- 订单总计 -->
                            <div class="order-totals-large">
                                <div class="total-line-large">
                                    <span class="total-label-large">{{trans_choice('[0,1] Item total(:count item)|[2,99999]Item total(:count items)', $cart['total']['cart_qty_total'], ['count'=>$cart['total']['cart_qty_total']])}}:</span>
                                    <span class="total-value-large">{{$_current_currency_name}} {{$cart['total']['cart_total']}}</span>
                                </div>
                                <div class="total-line-large">
                                    <span class="total-label-large">@lang('Shipping Cost'):</span>
                                    <span class="total-value-large">{{$_current_currency_name}} <span id="ST-shipping_fee">0.00</span></span>
                                </div>
                                <div class="total-line-large">
                                    <span class="total-label-large">@lang('Handling Cost'):</span>
                                    <span class="total-value-large">{{$_current_currency_name}} <span id="ST-handling_fee">0.00</span></span>
                                </div>
                                <div class="total-line-large">
                                    <span class="total-label-large">@lang('Tax Cost'):</span>
                                    <span class="total-value-large">{{$_current_currency_name}} <span id="ST-tax_fee">0.00</span></span>
                                </div>
                                @if(app('strongshop')->getShopConfig('use_credits'))
                                <div class="total-line-large">
                                    <span class="total-label-large">@lang('Use Credits'):</span>
                                    <span class="total-value-large">{{$_current_currency_name}} <span id="ST-used_credits_amount">0.00</span></span>
                                </div>
                                @endif
                                <div class="total-line-large total-final-large">
                                    <span class="total-label-large">@lang('Order Total'):</span>
                                    <span class="total-value-large">{{$_current_currency_name}} <span id="ST-ORDER-TOTAL">{{$cart['total']['cart_total']}}</span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 支付选项 -->
                    <div class="checkout-panel-large">
                        <div class="panel-header-large">
                            <h2>💳 @lang('Payment Options')</h2>
                        </div>
                        <div class="panel-content-large">


                            <div class="payment-options-large">
                                @if(count($payment_options) > 0)
                                    @foreach($payment_options as $payment_option)
                                    <div class="payment-option-large">
                                        <input type="radio" name="payment_option_id" id="payment_{{$payment_option->id}}" value="{{$payment_option->id}}" @if ($loop->first)checked @endif>
                                        <label for="payment_{{$payment_option->id}}" class="payment-label-large">
                                            <div class="payment-info-large">
                                                <span class="payment-name-large">{{$payment_option->name ?? $payment_option->title ?? 'Unknown Payment'}}</span>
                                                <span class="payment-desc-large">{{$payment_option->description ?? $payment_option->desc ?? 'No description'}}</span>
                                            </div>
                                            <div class="payment-check-large"></div>
                                        </label>
                                    </div>
                                    @endforeach
                                @else
                                    <div class="no-payment-options" style="text-align: center; padding: 40px; color: #666;">
                                        <h4>@lang('No payment options available')</h4>
                                        <p>@lang('Please contact administrator to configure payment methods.')</p>
                                    </div>
                                @endif
                            </div>

                            @if(app('strongshop')->getShopConfig('use_credits'))
                            <div class="credits-section-large">
                                <label class="label-large">@lang('Use Credits'):</label>
                                <input class="form-control-large credits-input-large" value="" type="number" name="use_credits" placeholder="0" />
                                <p class="credits-help-large">
                                    @lang('You have <b>:have_credits</b> credits, The maximum credits for the order that you can use are <b>:viable_credits</b> credits.', ['have_credits'=>auth()->user()->pay_credits,'viable_credits'=>$viable_credits])
                                </p>
                            </div>
                            @endif

                            <!-- 订单备注 -->
                            <div class="order-notes-large">
                                <label class="label-large">@lang('Order Remark')</label>
                                <textarea class="form-control-large textarea-large" rows="4" name="remark" placeholder="@lang('Special instructions for your order...')"></textarea>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="submit-section-large">
                                <button class="btn-checkout-large" type="button" name="submit_name" value="place_order">
                                    <span class="btn-icon-large">🔒</span>
                                    <span class="btn-text-large">@lang('PLACE MY ORDER')</span>
                                </button>


                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@endsection

@push('styles')
<style>
/* 重置所有样式 - 参考mostxx.com */
.st-checkout-elegant * {
    box-sizing: border-box;
}

.st-checkout-elegant {
    background: #fafafa;
    min-height: 100vh;
    padding: 40px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
}

.st-checkout-elegant .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 强制重置Bootstrap */
.st-checkout-elegant .row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin-left: -15px !important;
    margin-right: -15px !important;
}

.st-checkout-elegant .col-md-8 {
    flex: 0 0 66.666667% !important;
    max-width: 66.666667% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.st-checkout-elegant .col-md-4 {
    flex: 0 0 33.333333% !important;
    max-width: 33.333333% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.st-checkout-elegant .col-md-6 {
    flex: 0 0 50% !important;
    max-width: 50% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.st-checkout-elegant .col-md-12 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

@media (max-width: 768px) {
    .st-checkout-elegant .col-md-8,
    .st-checkout-elegant .col-md-4,
    .st-checkout-elegant .col-md-6 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }
}

/* 简洁标题 */
.checkout-header-large {
    text-align: center;
    margin-bottom: 40px;
}

.checkout-title-large {
    font-size: 2.2rem;
    font-weight: 300;
    color: #1a1a1a;
    margin-bottom: 10px;
}

.checkout-subtitle {
    font-size: 1rem;
    color: #666;
    margin: 0;
}



/* 简洁面板 */
.checkout-panel-large {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    overflow: hidden;
}

/* 订单摘要 */
.order-summary-large {
    position: sticky;
    top: 20px;
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    overflow: hidden;
}

/* 面板标题 */
.panel-header-large {
    background: #f8f9fa;
    color: #1a1a1a;
    padding: 18px 25px;
    border-bottom: 1px solid #e1e5e9;
    margin: 0;
}

.panel-header-large h2 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
}

/* 面板内容 */
.panel-content-large {
    padding: 25px;
}

/* 简洁表单 */
.form-group-large {
    margin-bottom: 20px;
}

.label-large {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
    display: block;
}

.required-large {
    color: #ef4444;
    margin-left: 3px;
}

.form-control-large {
    border: 1px solid #d1d5db;
    border-radius: 5px;
    padding: 10px 14px;
    font-size: 14px;
    background: #ffffff;
    width: 100%;
    box-sizing: border-box;
    transition: border-color 0.15s ease;
}

.form-control-large:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.form-control-large:focus {
    outline: none;
    border-bottom-color: #000000;
    box-shadow: none;
    background: transparent;
}

.textarea-large {
    min-height: 120px;
    resize: vertical;
}

.checkbox-large {
    margin-top: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.checkbox-large input[type="checkbox"] {
    width: 22px;
    height: 22px;
    accent-color: #000000;
    transform: scale(1.2);
}

.checkbox-label-large {
    margin: 0;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 500;
    color: #333333;
}

/* 购物车商品样式 - 极简版 */
.cart-items-large {
    border-bottom: none;
    margin-bottom: 60px;
    padding-bottom: 0;
}

.cart-item-large {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 30px 0;
    border-bottom: 1px solid #f0f0f0;
    position: relative; /* 确保角标定位正确 */
}

.cart-item-large:last-child {
    border-bottom: none;
}

.item-image-large {
    position: relative;
    width: 100px;
    height: 100px;
    border: none;
    overflow: visible; /* 改为visible确保角标显示 */
    flex-shrink: 0;
}

.item-image-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.item-qty-large {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #000000;
    color: #ffffff;
    border-radius: 50%;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    font-weight: 500;
    border: 3px solid #ffffff;
    box-shadow: 0 3px 8px rgba(0,0,0,0.3);
    z-index: 100;
    min-width: 26px;
    min-height: 26px;
    line-height: 1;
    text-align: center;
}

.item-details-large {
    flex: 1;
}

.item-title-large {
    font-size: 1.4rem;
    font-weight: 400;
    margin: 0 0 10px 0;
    color: #000000;
    line-height: 1.4;
}

.item-sku-large,
.item-specs-large {
    font-size: 1.1rem;
    color: #666666;
    margin: 6px 0;
    font-weight: 400;
}

.item-price-large {
    font-weight: 400;
    color: #000000;
    font-size: 1.4rem;
    margin-top: 10px;
}

/* 订单总计样式 - 极简版 */
.order-totals-large {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 40px;
}

.total-line-large {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    font-size: 1.3rem;
}

.total-final-large {
    border-top: 1px solid #cccccc;
    padding-top: 25px;
    margin-top: 20px;
    font-weight: 400;
    font-size: 1.6rem;
}

.total-label-large {
    color: #000000;
    font-weight: 300;
    letter-spacing: 1px;
}

.total-value-large {
    color: #000000;
    font-weight: 300;
    letter-spacing: 1px;
}

/* 支付选项样式 - 极简版 */
.payment-options-large {
    display: flex;
    flex-direction: column;
    gap: 0;
    margin-bottom: 40px;
}

.payment-option-large {
    position: relative;
}

.payment-option-large input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

/* 优雅支付选项 */
.payment-label-large {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 24px;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #ffffff;
    font-size: 15px;
    font-weight: 400;
    margin-bottom: 15px;
}

.payment-label-large:hover {
    border-color: #3b82f6;
    background: #f8faff;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.08);
}

.payment-option-large input[type="radio"]:checked + .payment-label-large {
    border-color: #3b82f6;
    background: #eff6ff;
    box-shadow: 0 2px 12px rgba(59, 130, 246, 0.12);
}

.payment-info-large {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.payment-name-large {
    font-weight: 400;
    color: #000000;
    font-size: 1.4rem;
}

.payment-desc-large {
    font-size: 1.1rem;
    color: #666666;
    font-weight: 400;
}

.payment-check-large {
    width: 14px;
    height: 14px;
    border: 2px solid #cccccc;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
    background: #ffffff;
}

.payment-option-large input[type="radio"]:checked + .payment-label-large .payment-check-large {
    border-color: #000000;
    background: #000000;
    box-shadow: none;
}

.payment-option-large input[type="radio"]:checked + .payment-label-large .payment-check-large::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 4px;
    background: #ffffff;
    border-radius: 50%;
    animation: checkScale 0.2s ease-in-out;
}

@keyframes checkScale {
    0% {
        transform: translate(-50%, -50%) scale(0);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 积分和备注样式 - 极简版 */
.credits-section-large,
.order-notes-large {
    margin-bottom: 30px;
}

.credits-input-large {
    max-width: 200px;
}

.credits-help-large {
    font-size: 1.1rem;
    color: #666666;
    margin-top: 15px;
    margin-bottom: 0;
    line-height: 1.5;
    font-weight: 400;
}

/* 优雅提交按钮 */
.submit-section-large {
    margin-top: 40px;
}

.btn-checkout-large {
    width: 100%;
    background: #1a1a1a;
    color: #ffffff;
    border: none;
    padding: 18px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: inherit;
    letter-spacing: 0.25px;
    border-radius: 6px;
    box-shadow: 0 4px 14px rgba(0,0,0,0.15);
    pointer-events: auto;
    z-index: 10;
    position: relative;
}

.btn-checkout-large:hover {
    background: #333333;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

.btn-checkout-large:active {
    transform: translateY(0);
}

.btn-icon-large {
    font-size: 1rem;
}

/* 订单摘要现代化样式 */
.cart-items-large {
    margin-bottom: 24px;
}

.cart-item-large {
    display: flex;
    gap: 12px;
    padding: 16px 0;
    border-bottom: 1px solid #333;
}

.cart-item-large:last-child {
    border-bottom: none;
}

.item-image-large {
    position: relative;
    flex-shrink: 0;
}

.item-image-large img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
}

.item-qty-large {
    position: absolute;
    top: -6px;
    right: -6px;
    background: #ffffff;
    color: #1a1a1a;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

.item-details-large {
    flex: 1;
    min-width: 0;
}

.item-title-large {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.item-sku-large,
.item-specs-large {
    color: #9ca3af;
    font-size: 0.75rem;
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.item-price-large {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
}

/* 订单总计 */
.order-totals-large {
    border-top: 1px solid #333;
    padding-top: 16px;
}

.total-line-large {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.total-line-large:last-child {
    margin-bottom: 0;
    padding-top: 12px;
    border-top: 1px solid #333;
    font-weight: 600;
}

.total-label-large {
    color: #d1d5db;
    font-size: 0.875rem;
}

.total-value-large {
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
}

.total-line-large:last-child .total-label-large,
.total-line-large:last-child .total-value-large {
    font-size: 1rem;
    font-weight: 600;
}

.btn-text-large {
    font-weight: 400;
}

/* 复选框样式 - 极简版 */
.checkbox-large {
    margin-top: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.checkbox-large input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #000000;
    transform: none;
}

.checkbox-label-large {
    margin: 0;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: 400;
    color: #000000;
}

/* 响应式设计 - 大气版 */
@media (max-width: 768px) {
    .checkout-title-large {
        font-size: 2.5rem;
        letter-spacing: 2px;
    }

    .checkout-subtitle {
        font-size: 1.1rem;
    }

    .checkout-header-large {
        padding: 40px 20px;
        margin-bottom: 40px;
    }

    .panel-content-large {
        padding: 25px 20px;
    }

    .panel-header-large {
        padding: 20px 25px;
    }

    .panel-header-large h2 {
        font-size: 1.4rem;
    }

    .cart-item-large {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        text-align: left;
    }

    .item-image-large {
        width: 100px;
        height: 100px;
    }

    .payment-label-large {
        padding: 20px 25px;
    }

    .btn-checkout-large {
        padding: 20px 25px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .st-checkout-elegant {
        padding: 30px 0;
    }

    .checkout-title-large {
        font-size: 2rem;
    }

    .checkout-header-large {
        padding: 30px 15px;
        margin-bottom: 30px;
    }

    .panel-content-large {
        padding: 20px 15px;
    }

    .form-control-large {
        padding: 15px 18px;
        font-size: 1rem;
    }

    .label-large {
        font-size: 1.1rem;
    }
}

/* 加载动画 - 大气版 */
.loading {
    opacity: 0.7;
}

.loading .btn-checkout-large {
    pointer-events: none;
}

.loading .btn-checkout-large {
    position: relative;
}

.loading .btn-checkout-large::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 25px;
    height: 25px;
    border: 3px solid transparent;
    border-top: 3px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 隐藏账单地址部分 */
.st-checkout-address-billing {
    display: none;
}

.st-checkout-address-billing.show {
    display: block;
    margin-top: 30px;
    padding-top: 30px;
    border-top: 2px solid #000000;
}

/* 表单验证错误样式 */
.form-control-large.error {
    border-bottom-color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.05);
    animation: shake 0.5s ease-in-out;
}

.form-group-large.error .label-large {
    color: #dc3545;
}

.error-message {
    color: #dc3545;
    font-size: 1rem;
    margin-top: 8px;
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

.panel-header-large.error {
    background: #dc3545;
    animation: pulse 0.5s ease-in-out;
}

.checkout-section.error {
    border-left: 4px solid #dc3545;
    padding-left: 20px;
}

/* 动画效果 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { background: #dc3545; }
    50% { background: #c82333; }
    100% { background: #dc3545; }
}

/* 成功状态样式 */
.form-control-large.success {
    border-bottom-color: #28a745;
    background-color: rgba(40, 167, 69, 0.05);
}

.form-group-large.success .label-large {
    color: #28a745;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .item-image-large {
        width: 80px;
        height: 80px;
    }

    .item-qty-large {
        width: 22px;
        height: 22px;
        font-size: 11px;
        top: -8px;
        right: -8px;
        border: 2px solid #ffffff;
        min-width: 22px;
        min-height: 22px;
    }

    .cart-item-large {
        gap: 20px;
        padding: 20px 0;
    }

    .payment-label-large {
        padding: 20px 15px;
        font-size: 1.1rem;
    }

    .payment-check-large {
        width: 12px;
        height: 12px;
    }

    .payment-option-large input[type="radio"]:checked + .payment-label-large .payment-check-large::after {
        width: 3px;
        height: 3px;
    }
}

/* 线下扫码支付弹窗样式 - 黑白现代化风格 */
.offline-qr-modal, .qr-code-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    backdrop-filter: blur(8px);
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    animation: fadeIn 0.3s ease;
}

.modal-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: slideUp 0.4s ease;
}

.offline-qr-modal .modal-container {
    width: 900px;
    max-width: 95vw;
}

.qr-code-modal .modal-container {
    width: 650px;
    max-width: 95vw;
    max-height: 95vh;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
}

.modal-header {
    background: #fff;
    color: #333;
    padding: 20px 24px;
    position: relative;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: -0.02em;
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 24px;
    background: #f5f5f5;
    border: none;
    color: #666;
    font-size: 1.5rem;
    cursor: pointer;
    line-height: 1;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e5e5e5;
    color: #333;
    transform: scale(1.05);
}

.modal-body {
    padding: 12px 16px 0 16px;
    font-size: 14px;
    line-height: 1.4;
    flex: 1;
    overflow-y: auto;
    max-height: calc(95vh - 160px);
    display: flex;
    flex-direction: column;
}

.order-info {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 20px;
}

.order-info h4 {
    margin: 0 0 20px 0;
    color: #1a1a1a;
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.order-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
}

.order-detail:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
}

.order-detail span:first-child {
    color: #666;
    font-size: 14px;
}

.order-detail span:last-child,
.order-detail strong {
    color: #1a1a1a;
    font-weight: 500;
    font-size: 14px;
}

.order-amount {
    color: #000;
    font-size: 1.3em;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.qr-payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.qr-payment-item {
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.25s ease;
    background: #fff;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 12px;
    min-height: 80px;
}

.qr-payment-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: transparent;
    transition: all 0.25s ease;
}

.qr-payment-item:hover {
    border-color: #333;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    backface-visibility: hidden;
    perspective: 1000px;
}

.qr-payment-item:hover::before {
    background: #333;
}

.qr-payment-item:hover .qr-payment-name,
.qr-payment-item:hover .qr-payment-desc {
    transform: translateZ(0);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.qr-payment-item.selected {
    border-color: #000;
    background: #fafafa;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    transform: translateY(-3px);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    backface-visibility: hidden;
    perspective: 1000px;
}

.qr-payment-item.selected::before {
    background: #000;
}

.qr-payment-item.selected .qr-payment-name,
.qr-payment-item.selected .qr-payment-desc {
    transform: translateZ(0);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.qr-cover-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid #f0f0f0;
    transition: all 0.25s ease;
    flex-shrink: 0;
}

.qr-payment-item:hover .qr-cover-image {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.qr-payment-content {
    flex: 1;
    text-align: left;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.qr-payment-name {
    font-size: 14px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 4px;
    letter-spacing: -0.01em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.qr-payment-desc {
    font-size: 13px;
    color: #666;
    margin-bottom: 0;
    line-height: 1.3;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.select-qr-btn {
    background: #333;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.25s ease;
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0.02em;
    flex-shrink: 0;
    white-space: nowrap;
}

.select-qr-btn:hover {
    background: #000;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.qr-code-content {
    text-align: center;
    margin-bottom: 12px;
    padding: 12px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
}

.qr-code-content h4 {
    margin: 0 0 8px 0;
    color: #1a1a1a;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.qr-code-image {
    width: 180px;
    height: 180px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    margin: 0 auto 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.qr-code-image:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.important-notice {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-left: 4px solid #333;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 12px;
}

.important-notice h4 {
    margin: 0 0 8px 0;
    color: #000;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.important-notice ul {
    margin: 0;
    padding-left: 16px;
    color: #333;
    line-height: 1.4;
}

.important-notice li {
    margin-bottom: 6px;
    font-size: 14px;
}

.important-notice strong {
    color: #000;
    font-weight: 600;
}

.payment-actions {
    text-align: center;
    padding: 20px;
    margin: 0;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.payment-actions .btn {
    margin: 0 6px;
    padding: 10px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.02em;
    transition: all 0.25s ease;
}

.btn-success {
    background: #333;
    color: white;
}

.btn-success:hover {
    background: #000;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #e8e8e8;
}

.btn-secondary:hover {
    background: #e8e8e8;
    color: #333;
    transform: translateY(-1px);
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translate(-50%, -40%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* 复制按钮样式 */
.copy-btn {
    background: #333 !important;
    color: white !important;
    border: none !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
}

.copy-btn:hover {
    background: #000 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.copy-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2) !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .offline-qr-modal .modal-container,
    .qr-code-modal .modal-container {
        width: 95vw;
        max-width: 95vw;
        margin: 0 auto;
        left: 50% !important;
        right: auto !important;
        transform: translate(-50%, -50%) !important;
    }

    .modal-header {
        padding: 20px 24px;
    }

    .modal-header h3 {
        font-size: 1.3rem;
    }

    .qr-payment-methods {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .qr-payment-item {
        padding: 12px;
        min-height: 70px;
    }

    .qr-cover-image {
        width: 50px;
        height: 50px;
    }

    .qr-payment-name {
        font-size: 14px !important;
        font-weight: 700 !important;
    }

    .qr-payment-desc {
        font-size: 12px !important;
    }

    .select-qr-btn {
        padding: 6px 12px !important;
        font-size: 12px !important;
    }

    .qr-code-image {
        width: 200px;
        height: 200px;
        max-width: 90vw;
        max-height: 200px;
    }

    .order-info,
    .qr-code-content,
    .important-notice {
        padding: 12px;
        margin-bottom: 10px;
    }

    .qr-code-content h4 {
        font-size: 1rem !important;
        margin-bottom: 10px !important;
    }

    .important-notice h4 {
        font-size: 14px !important;
        margin-bottom: 8px !important;
    }

    .important-notice li {
        font-size: 13px !important;
        margin-bottom: 6px !important;
    }

    .copy-btn {
        padding: 4px 8px !important;
        font-size: 11px !important;
    }

    /* 移动端按钮优化 */
    .qr-code-content button {
        padding: 6px 12px !important;
        font-size: 12px !important;
        margin: 2px !important;
    }

    .modal-body {
        padding: 12px 16px 0 16px !important;
    }
}

/* 错误提示样式优化 - 黑白风格 */
.layui-layer-dialog .layui-layer-content {
    line-height: 1.6;
    font-size: 14px;
    color: #333;
}

.layui-layer-dialog .layui-layer-title {
    background: #333;
    color: white;
    font-weight: 600;
}

.error-highlight {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-left: 4px solid #333;
    border-radius: 4px;
    padding: 15px;
    margin: 10px 0;
    color: #333;
    font-size: 14px;
}

.error-highlight strong {
    color: #000;
    font-weight: 600;
}

/* 错误对话框自定义样式 - 黑白风格 */
.layui-layer.error-dialog {
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    z-index: 99999999 !important;
}

/* 确保遮罩层也有足够高的z-index */
.layui-layer-shade {
    z-index: 99999998 !important;
}

/* 修复弹窗在某些情况下的定位问题 */
.layui-layer.error-dialog .layui-layer-main {
    position: relative !important;
}

.layui-layer.error-dialog .layui-layer-title {
    background: #333 !important;
    color: white !important;
    font-weight: 600 !important;
    border-radius: 0 !important;
    padding: 15px 20px !important;
    font-size: 16px !important;
    line-height: 1.4 !important;
    display: flex !important;
    align-items: center !important;
    min-height: 50px !important;
}

.layui-layer.error-dialog .layui-layer-content {
    padding: 25px !important;
    line-height: 1.6 !important;
    font-size: 14px !important;
    background: white !important;
    display: flex !important;
    align-items: center !important;
    min-height: 120px !important;
}

.layui-layer.error-dialog .layui-layer-btn {
    border-top: 1px solid #e9ecef !important;
    background: #f8f9fa !important;
    padding: 15px 20px !important;
    text-align: center !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.layui-layer.error-dialog .layui-layer-btn a {
    background: #333 !important;
    color: white !important;
    border-radius: 4px !important;
    margin: 0 !important;
    padding: 0 24px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    line-height: 1 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 40px !important;
    text-decoration: none !important;
    vertical-align: middle !important;
    box-sizing: border-box !important;
}

.layui-layer.error-dialog .layui-layer-btn a:hover {
    background: #000 !important;
    transform: translateY(-1px) !important;
}

/* 修复按钮文字基线对齐 */
.layui-layer.error-dialog .layui-layer-btn a span,
.layui-layer.error-dialog .layui-layer-btn a {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
    font-size: 14px !important;
    letter-spacing: 0.5px !important;
}

/* 强制重置按钮内部元素的样式 */
.layui-layer.error-dialog .layui-layer-btn a * {
    line-height: inherit !important;
    vertical-align: baseline !important;
}

/* 最强制的文字居中方案 */
.layui-layer.error-dialog .layui-layer-btn a::before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

.layui-layer.error-dialog .layui-layer-btn a > * {
    display: inline-block !important;
    vertical-align: middle !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 如果layui在按钮内添加了额外的元素，强制重置 */
.layui-layer.error-dialog .layui-layer-btn .layui-layer-btn0 {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 40px !important;
    line-height: 1 !important;
}

/* 错误提示中的特殊文本样式 */
.error-highlight .phone-mask {
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 600;
}

/* 错误对话框关闭按钮样式 */
.layui-layer.error-dialog .layui-layer-close {
    color: #999 !important;
    font-size: 18px !important;
}

.layui-layer.error-dialog .layui-layer-close:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1) !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .layui-layer.error-dialog {
        width: 90vw !important;
        max-width: 400px !important;
        margin: 0 !important;
    }

    .layui-layer.error-dialog .layui-layer-title {
        padding: 12px 15px !important;
        font-size: 15px !important;
        min-height: 45px !important;
    }

    .layui-layer.error-dialog .layui-layer-content {
        padding: 20px 15px !important;
        min-height: 100px !important;
    }

    .layui-layer.error-dialog .layui-layer-btn {
        padding: 12px 15px !important;
    }

    .layui-layer.error-dialog .layui-layer-btn a {
        padding: 0 20px !important;
        height: 36px !important;
        font-size: 14px !important;
        line-height: 1 !important;
    }
}
</style>
@endpush

<!-- 线下扫码支付弹窗 -->
<div id="offlineQrModal" class="offline-qr-modal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-container">
        <div class="modal-header">
            <h3>选择扫码支付方式</h3>
            <button type="button" class="modal-close" onclick="closeOfflineQrModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="orderInfo" class="order-info"></div>
            <div id="qrPaymentMethods" class="qr-payment-methods"></div>
        </div>
    </div>
</div>

<!-- 二维码显示弹窗 -->
<div id="qrCodeModal" class="qr-code-modal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-container">
        <div class="modal-header">
            <h3 id="qrModalTitle">扫码支付</h3>
            <button type="button" class="modal-close" onclick="closeQrCodeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="qrCodeContent" class="qr-code-content"></div>
            <div class="payment-instructions">
                <div class="important-notice">
                    <h4>重要提示</h4>
                    <ul>
                        <li>扫码支付时，请在转账备注中填写<strong>订单号</strong>和<strong>联系邮箱</strong></li>
                        <li>支付完成后，请联系客服确认到账状态</li>
                        <li>客服确认后，我们将立即处理您的订单</li>
                        <li>请保存支付凭证，如有疑问可随时联系客服</li>
                    </ul>
                </div>
                <div class="payment-actions">
                    <button type="button" class="btn btn-success" onclick="confirmOfflinePayment()">
                        确认已支付
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeQrCodeModal()">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts_bottom')
<script>
!function () {
    console.log('Checkout page JavaScript loaded'); // 调试信息

    // 订单总计计算
    Util.getOrderTotal();

    // 配送方式和支付方式变化时重新计算
    var timerOrder;
    $(document).on('change keyup', "input[name=shipping_option_id],input[name=payment_option_id],input[name=use_credits]", function (e) {
        clearTimeout(timerOrder);
        timerOrder = setTimeout(function () {
            Util.getOrderTotal();
        }, 500);
    });

    // 账单地址切换
    $("#billingThisAddress").change(function () {
        var checked = $(this).is(':checked');
        if (checked) {
            $('.st-checkout-address-billing').removeClass('show').hide();
            // 当选中"账单地址与配送地址相同"时，复制配送地址到账单地址
            copyShippingToBilling();
        } else {
            $('.st-checkout-address-billing').addClass('show').show();
        }
    });

    // 复制配送地址到账单地址的函数
    function copyShippingToBilling() {
        $('input[name="billing_first_name"]').val($('input[name="first_name"]').val());
        $('input[name="billing_last_name"]').val($('input[name="last_name"]').val());
        $('input[name="billing_email"]').val($('input[name="email"]').val());
        $('input[name="billing_phone"]').val($('input[name="phone"]').val());
        $('input[name="billing_fax"]').val($('input[name="fax"]').val());
        $('input[name="billing_country_code"]').val($('select[name="country_code"]').val());
        $('input[name="billing_state_code"]').val($('select[name="state_code"]').val());
        $('input[name="billing_state_other"]').val($('input[name="state_other"]').val() || 'N/A');
        $('input[name="billing_city"]').val($('input[name="city"]').val());
        $('input[name="billing_address_line_1"]').val($('input[name="address_line_1"]').val());
        $('input[name="billing_address_line_2"]').val($('input[name="address_line_2"]').val());
        $('input[name="billing_postal_code"]').val($('input[name="postal_code"]').val());
    }

    // 当配送地址字段改变时，如果选中了"账单地址与配送地址相同"，则自动更新账单地址
    $('input[name="first_name"], input[name="last_name"], input[name="email"], input[name="phone"], input[name="fax"], input[name="city"], input[name="address_line_1"], input[name="address_line_2"], input[name="postal_code"], input[name="state_other"]').on('input change', function() {
        if ($('#billingThisAddress').is(':checked')) {
            copyShippingToBilling();
        }
    });

    $('select[name="country_code"], select[name="state_code"]').on('change', function() {
        if ($('#billingThisAddress').is(':checked')) {
            copyShippingToBilling();
        }
    });

    // 页面加载时初始化账单地址
    $(document).ready(function() {
        if ($('#billingThisAddress').is(':checked')) {
            copyShippingToBilling();
        }
    });

    // 配送地区联动
    $("select[name=country_code]").change(function () {
        var country_code = this.value;
        var buyNow = $("input[name=buyNow]").val();

        // 同步到账单地址
        $("select[name=billing_country_code]").val(this.value);

        // 获取省份列表
        $.get('/common/region/states', {country_code: country_code}, function (res) {
            var option_html = "<option value=''>@lang('Select state')</option>";
            $.each(res.data, function (i, item) {
                option_html += '<option value="' + i + '">' + item + '</option>';
            });
            $("select[name=state_code]").html(option_html);
            $("select[name=billing_state_code]").html(option_html);
        });

        // 获取配送方式
        $.get('/shoppingcart/shippingoptions', {country_code: country_code, buyNow: buyNow}, function (res) {
            $("#ST-SHIPPING_OPTIONS").html(res.content);
            Util.getOrderTotal();
        });
    });

    // 账单地区联动
    $("select[name=billing_country_code]").change(function () {
        var country_code = this.value;
        $.get('/common/region/states', {country_code: country_code}, function (res) {
            var option_html = "<option value=''>@lang('Select state')</option>";
            $.each(res.data, function (i, item) {
                option_html += '<option value="' + i + '">' + item + '</option>';
            });
            $("select[name=billing_state_code]").html(option_html);
        });
    });

    // 更新state字段的函数
    window.updateStateFields = function(stateCode) {
        // 更新billing state code
        $('input[name="billing_state_code"]').val(stateCode);

        // 如果没有选择state code，设置state_other为默认值
        if (!stateCode) {
            $('input[name="state_other"]').val('N/A');
            $('input[name="billing_state_other"]').val('N/A');
        } else {
            $('input[name="state_other"]').val('');
            $('input[name="billing_state_other"]').val('');
        }
    };

    // 初始化state字段
    setTimeout(function() {
        var currentStateCode = $('select[name="state_code"]').val();
        updateStateFields(currentStateCode);
    }, 100);

    // 表单提交处理
    $(document).on('click', ".btn-checkout-large", function (e) {
        console.log('提交按钮被点击'); // 调试信息
        alert('JavaScript事件被触发！'); // 明显的调试信息
        e.preventDefault();
        e.stopPropagation();

        // 清除之前的错误状态
        clearFormErrors();

        // 客户端验证
        var validationResult = validateForm();
        if (!validationResult.isValid) {
            showFormErrors(validationResult.errors);
            return false;
        }

        // 添加加载状态
        $('.st-checkout-elegant').addClass('loading');

        // 显示加载层
        layer.load(1, {shade: [0.6, '#000']});

        // 提交表单
        $('#ST-FORM').ajaxSubmit({
            dataType: 'json',
            timeout: 30000,
            success: function (res) {
                console.log('AJAX成功响应:', res); // 调试信息
                layer.closeAll();
                $('.st-checkout-elegant').removeClass('loading');

                if (res && res.code !== 200) {
                    // 处理错误
                    console.log('检测到错误响应，code:', res.code, 'message:', res.message); // 调试信息
                    if (res.message) {
                        console.log('显示错误弹窗:', res.message); // 调试信息
                        layer.msg(res.message, {icon: 2});
                    } else if (res.data && typeof res.data === 'object') {
                        showServerErrors(res.data);
                    } else {
                        layer.msg('Unknown error', {icon: 2});
                    }
                    return;
                }

                // 成功后跳转
                if (res && res.data && res.data.toUrl) {
                    // 检查是否是线下扫码支付
                    if (res.data.paycode === 'offline_qr_payment') {
                        // 使用AJAX获取支付数据并显示弹窗
                        fetch(res.data.toUrl, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.type === 'offline_qr_modal') {
                                showOfflineQrModal(data.data);
                            } else {
                                window.location.href = res.data.toUrl;
                            }
                        })
                        .catch(error => {
                            console.error('Error loading offline QR payment:', error);
                            window.location.href = res.data.toUrl;
                        });
                    } else {
                        // 其他支付方式正常跳转
                        window.location.href = res.data.toUrl;
                    }
                } else {
                    layer.msg('Order submitted successfully!', {icon: 1}, function() {
                        window.location.href = '/';
                    });
                }
            },
            error: function (xhr, status, error) {
                console.log('AJAX错误响应:', xhr.status, xhr.responseText, error); // 调试信息
                layer.closeAll();
                $('.st-checkout-elegant').removeClass('loading');

                if (xhr.status === 422) {
                    // 验证错误
                    try {
                        var errors = JSON.parse(xhr.responseText);
                        showServerErrors(errors.errors || errors);
                    } catch (e) {
                        layer.msg('Validation error occurred', {icon: 2});
                    }
                } else {
                    layer.msg('An error occurred: ' + (error || 'Unknown error'), {icon: 2});
                }
            }
        });

        return false;
    });





    // 表单验证增强
    $('.form-control-large').on('blur', function() {
        var $this = $(this);
        var value = $this.val().trim();
        var isRequired = $this.siblings('.label-large').find('.required-large').length > 0;

        if (isRequired && !value) {
            $this.css('border-color', '#dc3545');
        } else {
            $this.css('border-color', '#333333');
        }
    });

    // 支付选项点击动画
    $('.payment-label-large').on('click', function() {
        $('.payment-label-large').removeClass('selected');
        $(this).addClass('selected');
    });

    // 表单验证函数
                    }

                    if (res.data && typeof res.data === 'object') {
                        showServerErrors(res.data);
                    } else {
                        layer.msg(res.message || 'Unknown error', {icon: 2});
                    }
                    return;
                }

                // 成功后跳转
                console.log('Checking success conditions...');
                console.log('res exists:', !!res);
                console.log('res.code === 200:', res && res.code === 200);
                console.log('res.data exists:', !!(res && res.data));
                console.log('res.data.toUrl exists:', !!(res && res.data && res.data.toUrl));

                if (res && res.code === 200 && res.data && res.data.toUrl) {
                    console.log('SUCCESS: Redirecting to:', res.data.toUrl);
                    console.log('Payment code:', res.data.paycode);

                    // 检查是否是线下扫码支付
                    if (res.data.paycode === 'offline_qr_payment') {
                        // 使用AJAX获取支付数据并显示弹窗
                        fetch(res.data.toUrl, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.type === 'offline_qr_modal') {
                                showOfflineQrModal(data.data);
                            } else {
                                // 如果AJAX失败，还是跳转到页面
                                window.location.href = res.data.toUrl;
                            }
                        })
                        .catch(error => {
                            console.error('Error loading offline QR payment:', error);
                            // 如果AJAX失败，还是跳转到页面
                            window.location.href = res.data.toUrl;
                        });
                    } else {
                        // 其他支付方式正常跳转
                        layer.msg('Order created successfully! Redirecting to payment...', {icon: 1, time: 2000}, function() {
                            window.location.href = res.data.toUrl;
                        });
                    }
                } else {
                    console.log('ERROR: Missing required data for redirect');
                    console.log('res:', res);
                    console.log('res.code:', res ? res.code : 'undefined');
                    console.log('res.data:', res ? res.data : 'undefined');
                    console.log('res.data.toUrl:', res && res.data ? res.data.toUrl : 'undefined');

                    if (res && res.code === 200) {
                        layer.msg('订单创建成功，但跳转信息缺失，请联系客服', {icon: 2});
                    } else {
                        layer.msg('订单提交失败，请重试', {icon: 2});
                    }

                    // 暂时不跳转到首页，让用户看到错误信息
                    // layer.msg('Order submitted successfully!', {icon: 1}, function() {
                    //     window.location.href = '/';
                    // });
                }
            },
            error: function (xhr, status, error) {
                layer.closeAll();
                $('.st-checkout-elegant').removeClass('loading');

                console.log('Error:', xhr.status, xhr.responseText); // 调试信息

                if (xhr.status === 204) {
                    // 204 No Content 通常表示成功但没有返回内容
                    layer.msg('Order submitted successfully!', {icon: 1}, function() {
                        window.location.href = '/';
                    });
                } else if (xhr.status === 422) {
                    // 验证错误
                    try {
                        var errors = JSON.parse(xhr.responseText);
                        showServerErrors(errors.errors || errors);
                    } catch (e) {
                        layer.msg('Validation error occurred', {icon: 2});
                    }
                } else if (xhr.status === 419) {
                    // CSRF token 过期
                    layer.msg('Session expired, please refresh the page', {icon: 2}, function() {
                        window.location.reload();
                    });
                } else {
                    layer.msg('An error occurred: ' + (error || 'Unknown error'), {icon: 2});
                }
            }
        });

        return false;
    });

    // 表单验证增强
    $('.form-control-large').on('blur', function() {
        var $this = $(this);
        var value = $this.val().trim();
        var isRequired = $this.siblings('.label-large').find('.required-large').length > 0;

        if (isRequired && !value) {
            $this.css('border-color', '#dc3545');
        } else {
            $this.css('border-color', '#333333');
        }
    });

    // 支付选项点击动画
    $('.payment-label-large').on('click', function() {
        $('.payment-label-large').removeClass('selected');
        $(this).addClass('selected');
    });

    // 表单验证函数
    function validateForm() {
        var errors = {};
        var isValid = true;

        // 验证必填字段
        var requiredFields = {
            'country_code': '@lang("Country")',
            'first_name': '@lang("First Name")',
            'last_name': '@lang("Last Name")',
            'phone': '@lang("Phone Number")',
            'address_line_1': '@lang("Address Line 1")',
            'city': '@lang("Town/City")',
            'state_code': '@lang("State/Province")',
            'postal_code': '@lang("Zip/Postal Code")'
        };

        $.each(requiredFields, function(fieldName, fieldLabel) {
            var $field = $('[name="' + fieldName + '"]');
            var value = $field.val();

            if (!value || value.trim() === '') {
                errors[fieldName] = fieldLabel + ' @lang("is required")';
                isValid = false;
            }
        });

        // 验证邮箱格式
        var email = $('[name="email"]').val();
        if (email && !isValidEmail(email)) {
            errors['email'] = '@lang("Please enter a valid email address")';
            isValid = false;
        }

        // 验证手机号格式
        var phone = $('[name="phone"]').val();
        if (phone && !isValidPhone(phone)) {
            errors['phone'] = '@lang("Please enter a valid phone number")';
            isValid = false;
        }

        // 验证配送方式
        if (!$('[name="shipping_option_id"]:checked').length) {
            errors['shipping_option'] = '@lang("Please select a shipping option")';
            isValid = false;
        }

        // 验证支付方式
        if (!$('[name="payment_option_id"]:checked').length) {
            errors['payment_option'] = '@lang("Please select a payment option")';
            isValid = false;
        }

        return {
            isValid: isValid,
            errors: errors
        };
    }

    // 显示表单错误
    function showFormErrors(errors) {
        $.each(errors, function(fieldName, message) {
            var $field = $('[name="' + fieldName + '"]');
            var $formGroup = $field.closest('.form-group-large');
            var $section = $field.closest('.checkout-section');

            // 添加错误样式
            $field.addClass('error');
            $formGroup.addClass('error');
            $section.addClass('error');

            // 添加错误消息
            if (!$formGroup.find('.error-message').length) {
                $formGroup.append('<span class="error-message">' + message + '</span>');
            }

            // 高亮对应的面板标题
            $section.find('.panel-header-large').addClass('error');
        });

        // 滚动到第一个错误字段
        var $firstError = $('.form-control-large.error').first();
        if ($firstError.length) {
            $('html, body').animate({
                scrollTop: $firstError.offset().top - 100
            }, 500);
        }
    }

    // 显示服务器端错误
    function showServerErrors(errors) {
        if (typeof errors === 'string') {
            layer.msg(errors, {icon: 2});
            return;
        }

        $.each(errors, function(fieldName, messages) {
            if (Array.isArray(messages)) {
                messages = messages[0]; // 取第一个错误消息
            }
            showFormErrors({[fieldName]: messages});
        });
    }

    // 清除表单错误
    function clearFormErrors() {
        $('.form-control-large').removeClass('error success');
        $('.form-group-large').removeClass('error success');
        $('.checkout-section').removeClass('error');
        $('.panel-header-large').removeClass('error');
        $('.error-message').remove();
    }

    // 邮箱验证
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // 手机号验证
    function isValidPhone(phone) {
        var phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }

    // 实时验证
    $('.form-control-large').on('blur', function() {
        var $this = $(this);
        var $formGroup = $this.closest('.form-group-large');
        var fieldName = $this.attr('name');
        var value = $this.val().trim();

        // 清除当前字段的错误状态
        $this.removeClass('error success');
        $formGroup.removeClass('error success');
        $formGroup.find('.error-message').remove();

        // 检查必填字段
        var isRequired = $formGroup.find('.required-large').length > 0;
        if (isRequired && !value) {
            $this.addClass('error');
            $formGroup.addClass('error');
            return;
        }

        // 特殊验证
        if (fieldName === 'email' && value && !isValidEmail(value)) {
            $this.addClass('error');
            $formGroup.addClass('error');
            $formGroup.append('<span class="error-message">@lang("Please enter a valid email address")</span>');
            return;
        }

        if (fieldName === 'phone' && value && !isValidPhone(value)) {
            $this.addClass('error');
            $formGroup.addClass('error');
            $formGroup.append('<span class="error-message">@lang("Please enter a valid phone number")</span>');
            return;
        }

        // 如果验证通过，添加成功样式
        if (value) {
            $this.addClass('success');
            $formGroup.addClass('success');
        }
    });

    // 线下扫码支付弹窗相关函数
    window.currentOrder = null;
    window.selectedQrPaymentId = null;

    window.showOfflineQrModal = function(data) {
        window.currentOrder = data.order;
        const qrPayments = data.qrPayments;

        // 填充订单信息
        const orderInfoHtml = `
            <h4>📋 订单信息</h4>
            <div class="order-detail">
                <span style="font-size: 14px;">订单号：</span>
                <strong style="font-size: 14px;">${window.currentOrder.order_no}</strong>
            </div>
            <div class="order-detail">
                <span style="font-size: 14px;">订单金额：</span>
                <span class="order-amount">${window.currentOrder.currency_code} ${parseFloat(window.currentOrder.order_amount).toFixed(2)}</span>
            </div>
            <div class="order-detail">
                <span style="font-size: 14px;">联系邮箱：</span>
                <span style="font-size: 14px;">${window.currentOrder.email}</span>
            </div>
        `;
        document.getElementById('orderInfo').innerHTML = orderInfoHtml;

        // 填充支付方式
        let qrMethodsHtml = '';
        qrPayments.forEach(payment => {
            const coverImage = payment.cover_image ?
                `<img src="${payment.cover_image}" class="qr-cover-image" alt="${payment.name}">` :
                `<div class="qr-cover-image" style="background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-image" style="font-size: 2rem; color: #ccc;"></i>
                </div>`;

            qrMethodsHtml += `
                <div class="qr-payment-item" data-id="${payment.id}" data-qr-image="${payment.qr_image || ''}" onclick="selectQrPayment(this)">
                    ${coverImage}
                    <div class="qr-payment-content">
                        <div class="qr-payment-name">${payment.name}</div>
                        <div class="qr-payment-desc">${payment.description || '便捷安全的支付方式'}</div>
                    </div>
                    <button type="button" class="select-qr-btn">选择</button>
                </div>
            `;
        });
        document.getElementById('qrPaymentMethods').innerHTML = qrMethodsHtml;

        // 显示弹窗
        document.getElementById('offlineQrModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
    };

    window.selectQrPayment = function(element) {
        // 移除其他选中状态
        document.querySelectorAll('.qr-payment-item').forEach(item => {
            item.classList.remove('selected');
        });

        // 添加选中状态
        element.classList.add('selected');
        window.selectedQrPaymentId = element.dataset.id;

        // 显示二维码弹窗
        const paymentName = element.querySelector('.qr-payment-name').textContent;
        const qrImageUrl = element.dataset.qrImage;

        showQrCodeModal(paymentName, qrImageUrl);
    };

    window.showQrCodeModal = function(paymentName, qrImageUrl) {
        document.getElementById('qrModalTitle').textContent = paymentName;

        let qrContentHtml = `<h4>${paymentName}</h4>`;
        if (qrImageUrl) {
            qrContentHtml += `<img src="${qrImageUrl}" class="qr-code-image" alt="${paymentName}二维码">`;
            qrContentHtml += `<p style="color: #666; margin-bottom: 16px; font-weight: 500;">请扫描上方二维码完成付款</p>`;
        } else {
            qrContentHtml += `
                <div class="qr-code-image" style="background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-qrcode" style="font-size: 3rem; color: #ccc;"></i>
                </div>
                <p style="color: #999; margin-bottom: 16px;">二维码暂未上传</p>
            `;
        }

        // 添加手机端扫码提示和按钮
        const paymentNameLower = paymentName.toLowerCase();
        let appButtonText = '🚀 打开应用扫码';

        if (paymentNameLower.includes('微信') || paymentNameLower.includes('wechat') || paymentNameLower.includes('weixin')) {
            appButtonText = '💬 打开微信扫码';
        } else if (paymentNameLower.includes('支付宝') || paymentNameLower.includes('alipay') || paymentNameLower.includes('zhifubao')) {
            appButtonText = '💰 打开支付宝扫码';
        }

        qrContentHtml += `
            <div style="text-align: center; margin: 12px 0;">
                <div style="display: flex; gap: 8px; justify-content: center; flex-wrap: wrap;">
                    <button onclick="saveQrCode()" style="background: #333; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 13px; cursor: pointer;">
                        📱 保存二维码
                    </button>
                    <button onclick="openInApp('${paymentName}')" style="background: #333; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 13px; cursor: pointer;">
                        ${appButtonText}
                    </button>
                </div>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 12px;">
                    手机用户：长按二维码保存到相册，或点击按钮直接打开对应应用扫码付款
                </p>
            </div>
        `;

        // 添加订单信息提示
        qrContentHtml += `
            <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; margin-top: 8px; text-align: left;">
                <h5 style="margin: 0 0 8px 0; color: #000; font-size: 14px; font-weight: 600;">💡 支付时请备注以下信息：</h5>
                <div style="display: flex; flex-direction: column; gap: 8px;">
                    <div style="display: flex; flex-direction: column; gap: 4px;">
                        <span style="color: #666; font-size: 13px; font-weight: 500;">订单号：</span>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <span id="orderNo" style="color: #000; font-weight: 600; font-family: 'Courier New', monospace; background: #fff; padding: 4px 8px; border-radius: 3px; border: 1px solid #ddd; font-size: 13px; flex: 1;">${window.currentOrder.order_no}</span>
                            <button onclick="copyToClipboard('orderNo')" class="copy-btn" style="padding: 4px 8px; font-size: 11px;">复制</button>
                        </div>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 4px;">
                        <span style="color: #666; font-size: 13px; font-weight: 500;">联系邮箱：</span>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <span id="orderEmail" style="color: #000; font-weight: 500; background: #fff; padding: 4px 8px; border-radius: 3px; border: 1px solid #ddd; font-size: 13px; flex: 1;">${window.currentOrder.email}</span>
                            <button onclick="copyToClipboard('orderEmail')" class="copy-btn" style="padding: 4px 8px; font-size: 11px;">复制</button>
                        </div>
                    </div>
                </div>
                <p style="margin: 8px 0 0 0; color: #666; font-size: 12px; line-height: 1.3;">
                    <strong>重要：</strong>请在转账备注中填写订单号和邮箱，以便我们快速确认您的支付。
                </p>
            </div>
        `;

        document.getElementById('qrCodeContent').innerHTML = qrContentHtml;
        document.getElementById('qrCodeModal').style.display = 'block';
    };

    // 复制到剪贴板功能
    window.copyToClipboard = function(elementId) {
        const element = document.getElementById(elementId);
        const text = element.textContent || element.innerText;

        // 使用现代的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(function() {
                showCopySuccess();
            }).catch(function(err) {
                // 如果失败，使用备用方法
                fallbackCopyTextToClipboard(text);
            });
        } else {
            // 备用方法
            fallbackCopyTextToClipboard(text);
        }
    };

    // 备用复制方法
    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess();
            } else {
                showCopyError();
            }
        } catch (err) {
            showCopyError();
        }

        document.body.removeChild(textArea);
    }

    // 显示复制成功提示
    function showCopySuccess() {
        // 使用layui的消息提示
        if (typeof layer !== 'undefined') {
            layer.msg('复制成功！', {icon: 1, time: 1500});
        } else {
            alert('复制成功！');
        }
    }

    // 显示复制失败提示
    function showCopyError() {
        if (typeof layer !== 'undefined') {
            layer.msg('复制失败，请手动复制', {icon: 2, time: 2000});
        } else {
            alert('复制失败，请手动复制');
        }
    }

    // 保存二维码到相册
    window.saveQrCode = function() {
        const qrImage = document.querySelector('.qr-code-image');
        if (!qrImage) {
            layer.msg('未找到二维码图片', {icon: 2});
            return;
        }

        // 检测是否为移动设备
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isMobile) {
            // 移动端：提示用户长按保存
            layer.alert('请长按二维码图片，选择"保存图片"或"添加到相册"', {
                icon: 1,
                title: '保存提示',
                btn: ['知道了']
            });
        } else {
            // 桌面端：尝试下载图片
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.crossOrigin = 'anonymous';
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    canvas.toBlob(function(blob) {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = '支付二维码.png';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                        layer.msg('二维码已下载', {icon: 1});
                    });
                };
                img.src = qrImage.src;
            } catch (error) {
                layer.msg('保存失败，请右键图片另存为', {icon: 2});
            }
        }
    };

    // 打开对应应用扫码
    window.openInApp = function(paymentName) {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (!isMobile) {
            layer.msg('此功能仅支持移动设备', {icon: 2});
            return;
        }

        let appUrl = '';
        let appName = '';

        // 更精确的支付方式识别
        const paymentNameLower = paymentName.toLowerCase();

        if (paymentNameLower.includes('微信') || paymentNameLower.includes('wechat') || paymentNameLower.includes('weixin')) {
            // 微信扫码付款
            appUrl = 'weixin://scanqrcode';
            appName = '微信';
        } else if (paymentNameLower.includes('支付宝') || paymentNameLower.includes('alipay') || paymentNameLower.includes('zhifubao')) {
            // 支付宝扫码付款
            appUrl = 'alipays://platformapi/startapp?saId=10000007';
            appName = '支付宝';
        } else {
            // 如果无法识别具体应用，提供选择
            layer.confirm('请选择要打开的应用进行扫码付款：', {
                btn: ['微信扫码付款', '支付宝扫码付款', '取消'],
                icon: 3,
                title: '选择扫码付款应用'
            }, function(index) {
                // 微信
                openSpecificApp('weixin://scanqrcode', '微信');
                layer.close(index);
            }, function(index) {
                // 支付宝
                openSpecificApp('alipays://platformapi/startapp?saId=10000007', '支付宝');
                layer.close(index);
            });
            return;
        }

        openSpecificApp(appUrl, appName);
    };

    // 打开指定应用的函数
    function openSpecificApp(appUrl, appName) {
        // 尝试打开应用
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = appUrl;
        document.body.appendChild(iframe);

        setTimeout(() => {
            document.body.removeChild(iframe);
        }, 1000);

        // 提示用户
        layer.msg(`正在尝试打开${appName}扫码付款...`, {icon: 1, time: 2000});

        // 备用提示
        setTimeout(() => {
            layer.alert(`如果${appName}没有自动打开，请手动打开${appName}应用，使用扫码功能扫描上方二维码进行付款`, {
                icon: 1,
                title: '扫码付款提示',
                btn: ['知道了']
            });
        }, 3000);
    }

    window.closeOfflineQrModal = function() {
        document.getElementById('offlineQrModal').style.display = 'none';
        document.body.style.overflow = 'auto';
        window.selectedQrPaymentId = null;
    };

    window.closeQrCodeModal = function() {
        document.getElementById('qrCodeModal').style.display = 'none';
        // 移除选中状态
        document.querySelectorAll('.qr-payment-item').forEach(item => {
            item.classList.remove('selected');
        });
        window.selectedQrPaymentId = null;
    };

    window.confirmOfflinePayment = function() {
        if (!window.selectedQrPaymentId || !window.currentOrder) {
            layer.msg('请选择支付方式', {icon: 2});
            return;
        }

        // 发送确认请求
        fetch('/payment/offline-qr/confirm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                order_id: window.currentOrder.id,
                qr_payment_id: window.selectedQrPaymentId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                layer.msg(data.message, {icon: 1}, function() {
                    closeQrCodeModal();
                    closeOfflineQrModal();
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        // 刷新页面或跳转到首页
                        window.location.href = '/';
                    }
                });
            } else {
                layer.msg('确认失败：' + data.message, {icon: 2});
            }
        })
        .catch(error => {
            console.error('Error:', error);
            layer.msg('网络错误，请稍后重试', {icon: 2});
        });
    };

    // 点击弹窗外部关闭
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-overlay')) {
            if (e.target.closest('#offlineQrModal')) {
                closeOfflineQrModal();
            } else if (e.target.closest('#qrCodeModal')) {
                closeQrCodeModal();
            }
        }
    });

}();
</script>
@endpush