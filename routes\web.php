<?php

/*
  |--------------------------------------------------------------------------
  | Web Routes
  |--------------------------------------------------------------------------
 */
Route::get('jagship/home', function () {
    return redirect('strongadmin');
});
Route::any('/', 'HomeController@index')->name('home');
Route::get('/test', 'HomeController@test');

// 客服系统路由
Route::prefix('customer-service')->group(function () {
    Route::post('/init', 'SimpleCustomerServiceController@initSession');
    Route::post('/send', 'SimpleCustomerServiceController@sendMessage');
    Route::post('/heartbeat', 'SimpleCustomerServiceController@heartbeat');
    Route::get('/heartbeat-img', 'SimpleCustomerServiceController@heartbeatImg'); // 图片方式心跳
    Route::get('/status', 'SimpleCustomerServiceController@getOnlineStatus');
    Route::get('/message-stream', 'SimpleCustomerServiceController@messageStream');
});

// 客服系统API路由
Route::get('/api/customer-service/messages/{sessionId}', 'Strongadmin\CustomerServiceController@getNewMessages');

// 临时直接SSE路由测试
Route::get('/customer-service-sse-test', function(\Illuminate\Http\Request $request) {
    $sessionId = $request->input('session_id', 'test');
    $lastMessageId = $request->input('last_message_id', 0);

    return response()->stream(function () use ($sessionId, $lastMessageId) {
        set_time_limit(0);

        // 发送初始连接消息
        echo "data: " . json_encode([
            'type' => 'connected',
            'message' => 'SSE连接已建立',
            'session_id' => $sessionId,
            'timestamp' => time()
        ]) . "\n\n";
        flush();

        // 模拟检查新消息
        for ($i = 0; $i < 10; $i++) {
            sleep(2);

            // 发送心跳
            echo "data: " . json_encode([
                'type' => 'heartbeat',
                'timestamp' => time()
            ]) . "\n\n";
            flush();

            // 检查连接是否还活着
            if (connection_aborted()) {
                break;
            }
        }

    }, 200, [
        'Content-Type' => 'text/event-stream',
        'Cache-Control' => 'no-cache',
        'Connection' => 'keep-alive',
        'X-Accel-Buffering' => 'no',
    ]);
});

// 客服系统API路由
Route::prefix('api/customer-service')->group(function () {
    Route::get('/status', 'SimpleCustomerServiceController@getSystemStatus');
    Route::get('/csrf-token', function() {
        return response()->json(['token' => csrf_token()]);
    });
    Route::post('/my-messages', 'SimpleCustomerServiceController@getMyMessages');
});

// 临时测试路由
Route::get('/test-sse', function() {
    return response()->stream(function () {
        echo "data: " . json_encode(['type' => 'test', 'message' => 'SSE连接测试成功', 'timestamp' => time()]) . "\n\n";
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }, 200, [
        'Content-Type' => 'text/event-stream',
        'Cache-Control' => 'no-cache',
        'Connection' => 'keep-alive',
        'X-Accel-Buffering' => 'no',
    ]);
});

// 最简单的SSE测试
Route::get('/simple-sse', function() {
    return response()->stream(function () {
        echo "data: " . json_encode(['message' => 'Hello SSE!', 'time' => date('H:i:s')]) . "\n\n";
        flush();
    }, 200, [
        'Content-Type' => 'text/event-stream',
        'Cache-Control' => 'no-cache',
        'Connection' => 'keep-alive',
    ]);
});

// 工作的SSE实现（不依赖数据库）
Route::get('/working-sse', function(\Illuminate\Http\Request $request) {
    $sessionId = $request->input('session_id', 'default');
    $lastMessageId = $request->input('last_message_id', 0);

    return response()->stream(function () use ($sessionId, $lastMessageId) {
        // 发送连接成功消息
        echo "data: " . json_encode([
            'type' => 'connected',
            'message' => '工作的SSE连接成功',
            'session_id' => $sessionId,
            'timestamp' => time()
        ]) . "\n\n";
        flush();

        // 模拟消息检查循环
        for ($i = 0; $i < 30; $i++) {
            // 每10秒发送一个模拟消息
            if ($i % 5 == 0 && $i > 0) {
                echo "data: " . json_encode([
                    'type' => 'new_reply',
                    'message_id' => $lastMessageId + $i,
                    'sender_type' => 'admin',
                    'message' => '模拟管理员回复 ' . date('H:i:s'),
                    'timestamp' => time()
                ]) . "\n\n";
                flush();
            }

            // 心跳包
            if ($i % 15 == 0) {
                echo "data: " . json_encode(['type' => 'heartbeat', 'timestamp' => time()]) . "\n\n";
                flush();
            }

            // 检查连接
            if (connection_aborted()) {
                break;
            }

            sleep(2);
        }

    }, 200, [
        'Content-Type' => 'text/event-stream',
        'Cache-Control' => 'no-cache',
        'Connection' => 'keep-alive',
        'X-Accel-Buffering' => 'no',
    ]);
});

// 真正的前台SSE实现
Route::get('/frontend-sse', function(\Illuminate\Http\Request $request) {
    try {
        $sessionId = $request->input('session_id');
        $lastMessageId = $request->input('last_message_id', 0);

        if (!$sessionId) {
            return response('Session ID required', 400);
        }

        return response()->stream(function () use ($sessionId, $lastMessageId) {
            set_time_limit(0);

            try {
                // 获取会话数据库ID
                $session = DB::table('customer_service_sessions')->where('session_id', $sessionId)->first();
                if (!$session) {
                    // 如果会话不存在，创建一个新会话
                    $sessionDbId = DB::table('customer_service_sessions')->insertGetId([
                        'session_id' => $sessionId,
                        'visitor_ip' => request()->ip(),
                        'user_agent' => request()->userAgent(),
                        'status' => 'active',
                        'last_activity' => now(),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    echo "data: " . json_encode([
                        'type' => 'session_created',
                        'message' => '新会话已创建',
                        'session_db_id' => $sessionDbId
                    ]) . "\n\n";
                    flush();
                } else {
                    $sessionDbId = $session->id;
                }
            } catch (\Exception $e) {
                echo "data: " . json_encode(['type' => 'error', 'message' => 'Database error: ' . $e->getMessage()]) . "\n\n";
                flush();
                return;
            }

            // 发送连接成功消息
            echo "data: " . json_encode([
                'type' => 'connected',
                'message' => '前台SSE连接成功',
                'session_id' => $sessionId,
                'session_db_id' => $sessionDbId,
                'timestamp' => time()
            ]) . "\n\n";
            flush();

            $checkInterval = 2; // 2秒检查一次
            $maxDuration = 300; // 最大连接时间5分钟
            $startTime = time();

            while (time() - $startTime < $maxDuration) {
                try {
                    // 检查该会话的新回复消息（AI或管理员回复）
                    $newMessages = DB::table('customer_service_messages')
                                    ->where('session_id', $sessionDbId)
                                    ->where('id', '>', $lastMessageId)
                                    ->whereIn('sender_type', ['ai', 'admin'])
                                    ->orderBy('id', 'asc')
                                    ->get();

                    if ($newMessages->count() > 0) {
                        foreach ($newMessages as $message) {
                            $data = [
                                'type' => 'new_reply',
                                'message_id' => $message->id,
                                'sender_type' => $message->sender_type,
                                'message' => $message->message,
                                'created_at' => $message->created_at,
                                'timestamp' => time()
                            ];

                            echo "data: " . json_encode($data) . "\n\n";
                            $lastMessageId = $message->id;
                        }

                        flush();
                    }

                    // 发送心跳包
                    if (time() % 30 == 0) {
                        echo "data: " . json_encode(['type' => 'heartbeat', 'timestamp' => time()]) . "\n\n";
                        flush();
                    }

                    // 检查连接是否还活着
                    if (connection_aborted()) {
                        break;
                    }

                    sleep($checkInterval);

                } catch (\Exception $e) {
                    echo "data: " . json_encode(['type' => 'error', 'message' => 'Stream error: ' . $e->getMessage()]) . "\n\n";
                    flush();
                    break;
                }
            }

        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no',
        ]);

    } catch (\Exception $e) {
        \Log::error('Frontend SSE error: ' . $e->getMessage());
        return response('SSE Error: ' . $e->getMessage(), 500);
    }
});

//登录
Route::get('login', 'Auth\LoginController@showLoginForm')->name('login')->middleware(['guest']);
Route::post('login', 'Auth\LoginController@login');
Route::post('login/code', 'Auth\LoginController@loginCode')->middleware(['throttle:3,1']); //获取验证码
//登出
Route::get('logout', 'Auth\LoginController@logout')->name('logout');
//注册
Route::get('register', 'Auth\RegisterController@showRegisterForm')->name('register')->middleware('guest');
Route::post('register', 'Auth\RegisterController@register')->middleware(['throttle:5,1', 'guest']);
Route::get('register/captcha', 'Auth\RegisterController@captcha')->name('register.captcha');
Route::get('register/success', 'Auth\RegisterController@registerSuccess')->name('register.success');
//重置密码
Route::get('password/reset', 'Auth\ForgotPasswordController@showLinkRequestForm')->name('password.request');
Route::post('password/email', 'Auth\ForgotPasswordController@sendResetLinkEmail')->name('password.email');
Route::get('password/reset/{token}', 'Auth\ResetPasswordController@showResetForm')->name('password.reset');
Route::post('password/reset', 'Auth\ResetPasswordController@reset')->name('password.update');
//确认密码
Route::get('password/confirm', 'Auth\ConfirmPasswordController@showConfirmForm')->name('password.confirm');
Route::post('password/confirm', 'Auth\ConfirmPasswordController@confirm');
//邮箱验证
Route::get('email/verify', 'Auth\VerificationController@show')->name('verification.notice');
Route::get('email/verify/{id}/{hash}', 'Auth\VerificationController@verify')->name('verification.verify');
Route::post('email/resend', 'Auth\VerificationController@resend')->name('verification.resend');
//产品列表
Route::get('product/category', 'Product\ProductController@index')->name('product.list');
Route::get('category-{catid}.html', 'Product\ProductController@index')->name('product.list.rewrite'); //url rewrite
//产品搜索
Route::get('product/search', 'Product\ProductController@search')->name('product.search');
//产品详情
Route::get('product-{id}.html', 'Product\ProductController@show')->name('product.show.rewrite'); //url rewrite
Route::get('product-{id}', 'Product\ProductController@show'); //url rewrite
Route::get('product', 'Product\ProductController@show')->name('product.show');
Route::post('product/collect', 'Product\ProductController@collect')->middleware(['auth:web']); //收藏产品
//产品评论
Route::get('comment/list', 'Product\CommentController@list')->name('comment.list');
Route::any('comment/create', 'Product\CommentController@create')->name('comment.create')->middleware(['auth:web']);
Route::post('comment/upload/picture', 'Common\UploadController@imageForWork')->middleware(['auth:web']);
Route::get('comment/search/product', 'Product\CommentController@searchProduct');
//购物车
Route::get('shoppingcart', 'Product\ShoppingCartController@showIndexForm')->name('shoppingcart'); //购物车页
Route::get('shoppingcart/fetchNavcartHtml', 'Product\ShoppingCartController@fetchShoppingcartHtml'); //顶部导航购物车html
Route::get('shoppingcart/fetchShoppingcartHtml', 'Product\ShoppingCartController@fetchShoppingcartHtml')->name('shoppingcart.fetchShoppingcartHtml'); //购物车html
Route::get('shoppingcart/products', 'Product\ShoppingCartController@products'); //购物车产品
Route::post('shoppingcart/create', 'Product\ShoppingCartController@create'); //添加/更新购物车
Route::post('shoppingcart/update', 'Product\ShoppingCartController@update'); //更新购物车
Route::post('shoppingcart/remove', 'Product\ShoppingCartController@remove'); //移除购物车产品
Route::get('shoppingcart/checkout', 'Product\CheckoutController@showIndexForm')->name('shoppingcart.checkout')->middleware('guest.order'); //结算页
Route::post('shoppingcart/checkout/createOrder', 'Product\CheckoutController@createOrder')->name('shoppingcart.createOrder')->middleware('guest.order'); //创建订单

// 临时调试路由：测试游客下单配置
Route::get('debug/guest-order', function() {
    try {
        $guestOrderEnabled = \App\Models\SystemSetting::get('guest_order_enabled', false);
        $requirePhone = \App\Models\SystemSetting::get('guest_order_require_phone', true);
        $requireEmail = \App\Models\SystemSetting::get('guest_order_require_email', true);

        // 直接查询数据库
        $rawSetting = \App\Models\SystemSetting::where('setting_key', 'guest_order_enabled')->first();

        return response()->json([
            'guest_order_enabled' => $guestOrderEnabled,
            'guest_order_enabled_type' => gettype($guestOrderEnabled),
            'require_phone' => $requirePhone,
            'require_email' => $requireEmail,
            'raw_setting' => $rawSetting ? $rawSetting->toArray() : null,
            'user_logged_in' => auth('web')->check(),
            'middleware_should_pass' => $guestOrderEnabled || auth('web')->check()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：修复游客支付方式配置
Route::get('fix/guest-payment-methods', function() {
    try {
        // 更新游客支付方式配置为正确的JSON格式
        \App\Models\SystemSetting::updateOrCreate(
            ['setting_key' => 'guest_order_payment_methods'],
            [
                'setting_value' => json_encode(['paypal']),
                'setting_type' => 'json',
                'setting_group' => 'guest_order',
                'description' => '游客下单允许的支付方式'
            ]
        );

        $setting = \App\Models\SystemSetting::where('setting_key', 'guest_order_payment_methods')->first();
        $value = \App\Models\SystemSetting::get('guest_order_payment_methods', ['paypal']);

        // 清除缓存
        \Cache::forget("system_setting_guest_order_payment_methods");

        // 重新获取值
        $value = \App\Models\SystemSetting::get('guest_order_payment_methods', ['paypal']);

        return response()->json([
            'success' => true,
            'message' => 'Guest payment methods updated and cache cleared',
            'raw_setting' => $setting->toArray(),
            'parsed_value' => $value,
            'value_type' => gettype($value),
            'is_array' => is_array($value)
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ]);
    }
});

// 临时路由：清除缓存
Route::get('clear-cache', function() {
    try {
        \Cache::flush();
        return response()->json([
            'success' => true,
            'message' => 'All cache cleared successfully'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ]);
    }
});

// 临时路由：检查产品和添加到购物车
Route::get('test/add-to-cart', function() {
    try {
        // 检查产品表
        $productCount = \App\Models\Product\Product::count();
        $availableProducts = \App\Models\Product\Product::where('status', 1)->get(['id', 'title', 'status', 'stock_status']);

        if ($availableProducts->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No available products found',
                'total_products' => $productCount,
                'available_products' => $availableProducts->toArray()
            ]);
        }

        $product = $availableProducts->first();

        // 模拟添加到购物车的请求
        $request = new \Illuminate\Http\Request();
        $request->merge([
            'product_id' => $product->id,
            'qty' => 1,
            'buyNow' => 1
        ]);

        // 设置必要的请求属性
        $request->setUserResolver(function () {
            return auth('web')->user();
        });

        $controller = new \App\Http\Controllers\Product\ShoppingCartController();
        $result = $controller->create($request);

        return response()->json([
            'success' => $result['code'] == 200,
            'message' => 'Attempted to add product to cart',
            'product' => [
                'id' => $product->id,
                'title' => $product->title,
                'status' => $product->status,
                'stock_status' => $product->stock_status
            ],
            'cart_result' => $result,
            'total_products' => $productCount,
            'available_products_count' => $availableProducts->count()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：检查购物车状态
Route::get('debug/cart-status', function(\Illuminate\Http\Request $request) {
    try {
        $buyNow = $request->get('buyNow') == '1';

        // 检查购物车数据
        $cart = \App\Repositories\CartRepository::getCart($buyNow);

        // 检查数据库中的购物车记录
        $cartItems = \App\Models\Product\ShoppingCart::where('uuid', app('strongshop')->uuid)
            ->where('is_buy_now', $buyNow ? 1 : 0)
            ->with('product')
            ->get();

        return response()->json([
            'buyNow' => $buyNow,
            'uuid' => app('strongshop')->uuid,
            'cart_summary' => $cart,
            'cart_items_count' => $cartItems->count(),
            'cart_items' => $cartItems->toArray(),
            'user_logged_in' => auth('web')->check()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});








Route::get('payment/pay', 'Payment\IndexController@index')->name('payment.pay')->middleware('guest.order');//支付
//购物车订单费用合计/总计
Route::post('shoppingcart/ordertotal', 'Product\CheckoutController@orderTotal');
//订单跟踪
Route::any('user/order/tracking', 'User\OrderController@orderTracking')->name('user.orderTracking');
//地区
Route::get('common/region/states', 'Common\RegionController@states');
//配送方式列表
Route::get('shoppingcart/shippingoptions', 'Product\CheckoutController@getShippingOptions');
//paypal 支付
Route::any('payment/paypal/return', 'Payment\PaypalController@successReturn')->name('paypal.return'); //支付成功后返回页面
Route::post('payment/paypal/notify', 'Payment\PaypalController@notify')->name('paypal.notify'); //paypal 回调
Route::get('payment/paypal/pay', 'Payment\PaypalController@payStandard')->name('paypal.pay')->middleware('guest.order'); //paypal 支付
//文章
Route::get('article/list', 'ArticleController@index')->name('article.index'); //列表
Route::get('article', 'ArticleController@show')->name('article.show'); //详情
Route::get('article-{id}.html', 'ArticleController@show')->name('article.show.rewrite');
Route::get('article/{postid}', 'ArticleController@show')->name('article.show.postid'); //根据postid唯一表示查找文章详情
//auth
Route::middleware(['auth:web'])->group(function () {
    //个人中心
    Route::get('user/index', 'User\UserController@index')->name('user.index'); //用户首页
    Route::get('user/account', 'User\UserController@account')->name('user.my.account'); //我的账号
    Route::post('user/update', 'User\UserController@update')->name('user.my.account.update'); //更新个人信息
    Route::post('user/email/resendVerify', 'User\UserController@resendEmailVerification')->middleware(['throttle:3,1']); //重新发送email验证
    Route::get('user/collects', 'User\UserController@collects')->name('user.my.collects'); //我的收藏
    Route::post('user/collects/remove', 'User\UserController@removeCollects'); //移除收藏
    //订单
    Route::get('user/orders', 'User\OrderController@orders')->name('user.my.orders'); //我的订单
    Route::get('user/order/detail', 'User\OrderController@orderDetail')->name('user.my.order.detail'); //我的订单-详情
    Route::post('user/order/cancel', 'User\OrderController@cancelOrder'); //取消订单
    Route::post('user/order/receive', 'User\OrderController@receiveOrder'); //确认收货
    //意见反馈
    Route::get('user/feedback/index', 'User\FeedbackController@index')->name('user.my.feedback'); //我的反馈
    Route::match(['get', 'post'], 'user/feedback/create', 'User\FeedbackController@create')->name('user.my.feedback.create')->middleware(['throttle:3,1']); //提交反馈
    Route::match(['get', 'post'], 'user/feedback/replies', 'User\FeedbackController@replies')->name('user.my.feedback.reylies'); //回复列表
});

Route::get('auth/facebook', 'Auth\Socialite\FacebookController@redirectToProvider')->name('auth.facebook');
Route::get('auth/facebook/callback', 'Auth\Socialite\FacebookController@handleProviderCallback')->name('auth.facebook.callback');
Route::get('auth/google', 'Auth\Socialite\GoogleController@redirectToProvider')->name('auth.google');
Route::get('auth/google/callback', 'Auth\Socialite\GoogleController@handleProviderCallback')->name('auth.google.callback');

Route::get('payment/paondelivery/pay', 'Payment\PayondeliveryController@index')->name('paondelivery.pay')->middleware('auth:web'); //货到付款
// 视频上传路由
Route::post('/upload-video', function(){
    $file = request()->file('video');
    
    // 验证文件类型
    if (!$file->isValid() || !in_array($file->extension(), ['mp4','webm'])) {
        return response()->json(['code' => 1, 'msg' => '仅支持MP4/WEBM格式']);
    }

    // 上传到OSS
    try {
        $ossPath = 'videos/'.date('Ym').'/'.$file->hashName();
        $url = (new App\Services\OssService())->upload($ossPath, $file->getRealPath());
        return response()->json(['code' => 0, 'url' => $url]);
    } catch (\Exception $e) {
        return response()->json(['code' => 2, 'msg' => $e->getMessage()]);
    }
});
//优惠邮箱
Route::post('/submit-email', [\App\Http\Controllers\EmailController::class, 'store']);

// ============ 国际支付路由 International Payment Routes ============

// Stripe 支付路由
Route::prefix('payment/stripe')->name('stripe.')->group(function () {
    Route::get('/', [App\Http\Controllers\Payment\StripeController::class, 'index'])->name('index');
    Route::get('/callback', [App\Http\Controllers\Payment\StripeController::class, 'callback'])->name('callback');
    Route::post('/webhook', [App\Http\Controllers\Payment\StripeController::class, 'webhook'])->name('webhook');
    Route::get('/status', [App\Http\Controllers\Payment\StripeController::class, 'status'])->name('status');
    Route::post('/refund', [App\Http\Controllers\Payment\StripeController::class, 'refund'])->name('refund');
    Route::get('/info', [App\Http\Controllers\Payment\StripeController::class, 'info'])->name('info');
    Route::get('/test', [App\Http\Controllers\Payment\StripeController::class, 'test'])->name('test');
});

// PayPal 支付路由
Route::prefix('payment/paypal')->name('paypal.')->group(function () {
    Route::get('/', [App\Http\Controllers\Payment\PayPalController::class, 'index'])->name('index');
    Route::get('/callback', [App\Http\Controllers\Payment\PayPalController::class, 'callback'])->name('callback');
    Route::post('/webhook', [App\Http\Controllers\Payment\PayPalController::class, 'webhook'])->name('webhook');
    Route::get('/info', [App\Http\Controllers\Payment\PayPalController::class, 'info'])->name('info');
    Route::get('/test', [App\Http\Controllers\Payment\PayPalController::class, 'test'])->name('test');
});

// 支付宝支付路由
Route::prefix('payment/alipay')->name('alipay.')->group(function () {
    Route::get('/', [App\Http\Controllers\Payment\AlipayController::class, 'index'])->name('index');
    Route::get('/callback', [App\Http\Controllers\Payment\AlipayController::class, 'callback'])->name('callback');
    Route::post('/webhook', [App\Http\Controllers\Payment\AlipayController::class, 'webhook'])->name('webhook');
    Route::get('/info', [App\Http\Controllers\Payment\AlipayController::class, 'info'])->name('info');
    Route::get('/test', [App\Http\Controllers\Payment\AlipayController::class, 'test'])->name('test');
});

// 通用支付路由
Route::prefix('payment')->name('payment.')->group(function () {
    Route::get('/success', function () {
        return view('payment.success');
    })->name('success');

    Route::get('/failed', function () {
        return view('payment.failed');
    })->name('failed');

    Route::get('/cancel', function () {
        return view('payment.cancel');
    })->name('cancel');
});



// 支付宝支付路由 - 基于最新OpenID体系
Route::prefix('alipay')->name('alipay.')->group(function () {
    // 支付相关
    Route::post('create', 'Payment\AlipayController@createPayment')->name('create');
    Route::get('callback', 'Payment\AlipayController@callback')->name('callback');
    Route::post('webhook', 'Payment\AlipayController@webhook')->name('webhook');
    Route::any('test', 'Payment\AlipayController@test')->name('test');

    // 授权登录相关
    Route::get('auth', 'Payment\AlipayController@auth')->name('auth');
    Route::get('auth/callback', 'Payment\AlipayController@authCallback')->name('auth.callback');

    // 账号绑定相关
    Route::post('bind', 'Payment\AlipayController@bindAccount')->name('bind');
    Route::get('bind/callback', 'Payment\AlipayController@bindCallback')->name('bind.callback');
});

// 临时路由：检查产品数据
Route::get('debug/products', function() {
    try {
        $totalProducts = \App\Models\Product\Product::count();
        $activeProducts = \App\Models\Product\Product::where('status', 1)->count();
        $products = \App\Models\Product\Product::where('status', 1)
            ->select('id', 'title', 'status', 'stock_status', 'sale_price')
            ->limit(5)
            ->get();

        return response()->json([
            'total_products' => $totalProducts,
            'active_products' => $activeProducts,
            'sample_products' => $products->toArray()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：直接创建测试产品
Route::get('create/test-product', function() {
    try {
        // 创建一个测试产品
        $product = new \App\Models\Product\Product();
        $product->title = 'Test Product for Buy Now';
        $product->sku = 'TEST-' . time();
        $product->original_price = 100.00;
        $product->sale_price = 85.00;
        $product->stock = 999;
        $product->stock_status = 1; // 有库存
        $product->status = 1; // 启用
        $product->weight = 0.5;
        $product->img_cover = '/images/default-product.jpg';
        $product->description = 'This is a test product for testing the buy now functionality.';
        $product->save();

        return response()->json([
            'success' => true,
            'message' => 'Test product created successfully',
            'product' => [
                'id' => $product->id,
                'title' => $product->title,
                'sku' => $product->sku,
                'price' => $product->sale_price,
                'status' => $product->status,
                'stock_status' => $product->stock_status
            ],
            'test_add_to_cart_url' => '/test/add-to-cart'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：检查支付选项
Route::get('debug/payment-options', function() {
    try {
        $allPaymentOptions = \App\Models\PaymentOption::all();
        $enabledPaymentOptions = \App\Models\PaymentOption::where('status', 1)->get();

        // 检查游客支付方式配置
        $guestPaymentMethods = \App\Models\SystemSetting::get('guest_order_payment_methods', ['paypal']);

        return response()->json([
            'all_payment_options_count' => $allPaymentOptions->count(),
            'all_payment_options' => $allPaymentOptions->toArray(),
            'enabled_payment_options_count' => $enabledPaymentOptions->count(),
            'enabled_payment_options' => $enabledPaymentOptions->toArray(),
            'guest_payment_methods' => $guestPaymentMethods,
            'guest_payment_methods_type' => gettype($guestPaymentMethods)
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：重新创建支付选项
Route::get('setup/payment-options-fix', function() {
    try {
        // 清空现有数据
        \App\Models\PaymentOption::truncate();

        // 创建支付选项 - 使用正确的字段名
        $paymentOptions = [
            [
                'title' => 'PayPal',
                'desc' => 'Pay securely with PayPal worldwide',
                'code' => 'paypal',
                'status' => 1,
                'more' => json_encode([
                    'business' => '<EMAIL>',
                    'env' => 'sandbox'
                ])
            ],
            [
                'title' => 'Stripe',
                'desc' => 'Credit card payments via Stripe',
                'code' => 'stripe',
                'status' => 0, // 禁用，因为您说没启用
                'more' => json_encode([
                    'public_key' => 'pk_test_...',
                    'secret_key' => 'sk_test_...'
                ])
            ],
            [
                'title' => 'Bank Transfer',
                'desc' => 'Direct bank transfer',
                'code' => 'bank_transfer',
                'status' => 0,
                'more' => json_encode([
                    'bank_name' => 'Test Bank',
                    'account_number' => '**********'
                ])
            ]
        ];

        foreach ($paymentOptions as $option) {
            \App\Models\PaymentOption::create($option);
        }

        // 验证创建结果
        $allOptions = \App\Models\PaymentOption::all();
        $enabledOptions = \App\Models\PaymentOption::where('status', 1)->get();

        return response()->json([
            'success' => true,
            'message' => 'Payment options recreated successfully',
            'total_created' => $allOptions->count(),
            'enabled_count' => $enabledOptions->count(),
            'all_options' => $allOptions->toArray(),
            'enabled_options' => $enabledOptions->toArray()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：检查支付选项表结构
Route::get('debug/payment-table', function() {
    try {
        // 检查表是否存在
        $tables = \DB::select("SHOW TABLES LIKE 'st_payment_options'");
        $tablesPaymentOptions = \DB::select("SHOW TABLES LIKE 'st_payment_option'");

        $result = [
            'st_payment_options_exists' => !empty($tables),
            'st_payment_option_exists' => !empty($tablesPaymentOptions),
            'db_prefix' => env('DB_PREFIX'),
        ];

        // 如果表存在，检查表结构
        if (!empty($tables)) {
            $columns = \DB::select("DESCRIBE st_payment_options");
            $result['st_payment_options_columns'] = $columns;

            // 检查数据
            $data = \DB::select("SELECT * FROM st_payment_options LIMIT 5");
            $result['st_payment_options_data'] = $data;
        }

        if (!empty($tablesPaymentOptions)) {
            $columns = \DB::select("DESCRIBE st_payment_option");
            $result['st_payment_option_columns'] = $columns;

            // 检查数据
            $data = \DB::select("SELECT * FROM st_payment_option LIMIT 5");
            $result['st_payment_option_data'] = $data;
        }

        return response()->json($result);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：使用正确字段创建支付选项
Route::get('setup/payment-options-correct', function() {
    try {
        // 清空现有数据 - 使用原始SQL避免前缀问题
        \DB::statement('DELETE FROM st_payment_options');

        // 使用正确的字段名创建支付选项
        $paymentOptions = [
            [
                'name' => 'PayPal',
                'code' => 'paypal',
                'description' => 'Pay securely with PayPal worldwide',
                'status' => 1,
                'sort_order' => 10,
                'config' => json_encode([
                    'business' => '<EMAIL>',
                    'env' => 'sandbox',
                    'currency' => 'USD'
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Stripe',
                'code' => 'stripe',
                'description' => 'Credit card payments via Stripe',
                'status' => 0, // 禁用
                'sort_order' => 20,
                'config' => json_encode([
                    'public_key' => 'pk_test_...',
                    'secret_key' => 'sk_test_...'
                ]),
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        // 直接插入数据库 - 使用原始SQL避免前缀问题
        foreach ($paymentOptions as $option) {
            \DB::statement("INSERT INTO st_payment_options (name, code, description, status, sort_order, config, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", [
                $option['name'],
                $option['code'],
                $option['description'],
                $option['status'],
                $option['sort_order'],
                $option['config'],
                $option['created_at']->format('Y-m-d H:i:s'),
                $option['updated_at']->format('Y-m-d H:i:s')
            ]);
        }

        // 验证创建结果 - 使用原始SQL查询
        $allOptions = \DB::select('SELECT * FROM st_payment_options');
        $enabledOptions = \DB::select('SELECT * FROM st_payment_options WHERE status = 1');

        return response()->json([
            'success' => true,
            'message' => 'Payment options created with correct fields',
            'total_created' => count($allOptions),
            'enabled_count' => count($enabledOptions),
            'all_options' => $allOptions,
            'enabled_options' => $enabledOptions
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：测试PaymentOption模型查询
Route::get('debug/payment-model', function() {
    try {
        // 直接查询数据库
        $directQuery = \DB::table('st_payment_options')->get();

        // 通过模型查询 - 先注释掉避免错误
        // $modelQuery = \App\Models\PaymentOption::all();

        // 检查模型的实际表名
        $model = new \App\Models\PaymentOption();
        $actualTableName = $model->getTable();

        // 检查数据库前缀
        $prefix = \DB::getTablePrefix();

        return response()->json([
            'db_prefix' => $prefix,
            'model_table_name' => $actualTableName,
            'full_table_name' => $prefix . $actualTableName,
            'direct_query_count' => $directQuery->count(),
            'direct_query_data' => $directQuery->toArray(),
            // 'model_query_count' => $modelQuery->count(),
            // 'model_query_data' => $modelQuery->toArray()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：调试PayPal表单
Route::get('debug/paypal/{orderId}', function($orderId) {
    try {
        $order = \App\Models\Admin\Order\Order::find($orderId);
        if (!$order) {
            return response()->json(['error' => 'Order not found']);
        }

        $model = \DB::select("SELECT * FROM st_payment_options WHERE code = 'paypal' AND status = 1 LIMIT 1");
        if (empty($model)) {
            return response()->json(['error' => 'PayPal payment option not found']);
        }
        $model = (object) $model[0];
        $model->more = json_decode($model->config, true);

        $business = $model->more['business'] ?? '<EMAIL>';
        $env = $model->more['env'] ?? 'sandbox';
        $currency = $order->currency_code ?? 'USD';
        $total = $order->order_amount ?? 0;
        $invoiceNumber = $order->order_no ?? '';

        $url = $env === 'live'
            ? 'https://www.paypal.com/cgi-bin/webscr'
            : 'https://www.sandbox.paypal.com/cgi-bin/webscr';

        return response()->json([
            'order_info' => [
                'id' => $order->id,
                'order_no' => $order->order_no,
                'currency_code' => $order->currency_code,
                'order_amount' => $order->order_amount,
                'order_status' => $order->order_status
            ],
            'paypal_config' => [
                'business' => $business,
                'env' => $env,
                'currency' => $currency,
                'url' => $url
            ],
            'form_data' => [
                'cmd' => '_xclick',
                'business' => $business,
                'item_name' => config('app.name'),
                'amount' => $total,
                'currency_code' => $currency,
                'invoice' => $invoiceNumber,
                'charset' => 'utf-8',
                'no_shipping' => '1'
            ],
            'issues' => [
                'is_test_business' => strpos($business, 'example.com') !== false,
                'is_sandbox' => $env === 'sandbox',
                'amount_valid' => $total > 0,
                'currency_valid' => !empty($currency)
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：更新PayPal配置为真实沙盒账号
Route::get('update/paypal-config', function() {
    try {
        // 更新PayPal配置，使用真实的沙盒商家账号
        \DB::statement("UPDATE st_payment_options SET config = ? WHERE code = 'paypal'", [
            json_encode([
                'business' => '<EMAIL>',
                'env' => 'sandbox',
                'currency' => 'USD'
            ])
        ]);

        // 验证更新结果
        $paypalConfig = \DB::select("SELECT * FROM st_payment_options WHERE code = 'paypal' LIMIT 1");

        return response()->json([
            'success' => true,
            'message' => 'PayPal configuration updated with real sandbox account',
            'config' => $paypalConfig ? json_decode($paypalConfig[0]->config, true) : null
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ]);
    }
});

// 临时路由：检查订单信息
Route::get('debug/order/{id}', function($id) {
    try {
        $order = \App\Models\Admin\Order\Order::find($id);

        if (!$order) {
            return response()->json([
                'error' => 'Order not found',
                'order_id' => $id
            ]);
        }

        return response()->json([
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'currency_code' => $order->currency_code,
            'currency_rate' => $order->currency_rate,
            'order_amount' => $order->order_amount,
            'products_amount' => $order->products_amount,
            'shipping_fee' => $order->shipping_fee,
            'order_status' => $order->order_status,
            'payment_option_id' => $order->payment_option_id,
            'all_fields' => $order->toArray()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：调试后台订单列表
Route::get('debug/admin-orders', function() {
    try {
        // 模拟后台订单列表查询
        $model = \App\Models\Admin\Order\Order::orderBy('id', 'desc');

        // 测试基本查询
        $basicQuery = $model->limit(5)->get();

        // 测试关联查询
        $model2 = \App\Models\Admin\Order\Order::orderBy('id', 'desc');
        $model2->with('buyer:id,email,first_name,last_name');
        $model2->with('shippingOption:id,title');

        try {
            $withBuyerShipping = $model2->limit(5)->get();
        } catch (\Exception $e) {
            $withBuyerShipping = 'Error: ' . $e->getMessage();
        }

        // 测试支付选项关联
        $model3 = \App\Models\Admin\Order\Order::orderBy('id', 'desc');
        try {
            $model3->with('paymentOption:id,name,code');
            $withPaymentOption = $model3->limit(5)->get();
        } catch (\Exception $e) {
            $withPaymentOption = 'Error: ' . $e->getMessage();
        }

        return response()->json([
            'basic_query_count' => $basicQuery->count(),
            'basic_query_sample' => $basicQuery->take(2)->toArray(),
            'with_buyer_shipping' => is_string($withBuyerShipping) ? $withBuyerShipping : $withBuyerShipping->count() . ' records',
            'with_payment_option' => is_string($withPaymentOption) ? $withPaymentOption : $withPaymentOption->count() . ' records',
            'total_orders' => \App\Models\Admin\Order\Order::count()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：检查所有支付方式
Route::get('debug/all-payment-options', function() {
    try {
        // 查看数据库中所有支付方式
        $allPayments = \DB::select("SELECT * FROM st_payment_options ORDER BY sort_order");

        // 查看后台支付管理可能使用的配置
        $domesticPayments = [];
        $internationalPayments = [];

        foreach ($allPayments as $payment) {
            $config = json_decode($payment->config ?? '{}', true);

            // 根据支付方式代码分类
            if (in_array($payment->code, ['alipay', 'wechat_pay', 'unionpay', 'bank_transfer'])) {
                $domesticPayments[] = $payment;
            } else {
                $internationalPayments[] = $payment;
            }
        }

        return response()->json([
            'total_count' => count($allPayments),
            'domestic_count' => count($domesticPayments),
            'international_count' => count($internationalPayments),
            'all_payments' => $allPayments,
            'domestic_payments' => $domesticPayments,
            'international_payments' => $internationalPayments
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：创建完整的支付方式列表
Route::get('setup/complete-payment-options', function() {
    try {
        // 清空现有数据
        \DB::statement('DELETE FROM st_payment_options');

        // 完整的支付方式列表
        $paymentOptions = [
            // 国际支付方式
            [
                'name' => 'PayPal',
                'code' => 'paypal',
                'description' => 'Pay securely with PayPal worldwide',
                'logo' => null,
                'config' => json_encode([
                    'business' => '<EMAIL>',
                    'env' => 'sandbox',
                    'currency' => 'USD'
                ]),
                'status' => 1,
                'sort_order' => 10,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Stripe',
                'code' => 'stripe',
                'description' => 'Credit card payments via Stripe',
                'logo' => null,
                'config' => json_encode([
                    'public_key' => 'pk_test_...',
                    'secret_key' => 'sk_test_...',
                    'currency' => 'USD'
                ]),
                'status' => 0,
                'sort_order' => 20,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'PayPal Express',
                'code' => 'paypal_express',
                'description' => 'PayPal Express Checkout',
                'logo' => null,
                'config' => json_encode([
                    'client_id' => 'your_client_id',
                    'client_secret' => 'your_client_secret',
                    'env' => 'sandbox'
                ]),
                'status' => 0,
                'sort_order' => 30,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Credit Card',
                'code' => 'credit_card',
                'description' => 'Direct credit card payment',
                'logo' => null,
                'config' => json_encode([
                    'gateway' => 'stripe',
                    'currency' => 'USD'
                ]),
                'status' => 0,
                'sort_order' => 40,
                'created_at' => now(),
                'updated_at' => now()
            ],
            // 国内支付方式
            [
                'name' => '支付宝',
                'code' => 'alipay',
                'description' => '支付宝在线支付',
                'logo' => null,
                'config' => json_encode([
                    'app_id' => 'your_app_id',
                    'private_key' => 'your_private_key',
                    'public_key' => 'alipay_public_key',
                    'env' => 'sandbox'
                ]),
                'status' => 0,
                'sort_order' => 50,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '微信支付',
                'code' => 'wechat_pay',
                'description' => '微信支付',
                'logo' => null,
                'config' => json_encode([
                    'app_id' => 'your_app_id',
                    'mch_id' => 'your_mch_id',
                    'key' => 'your_key',
                    'env' => 'sandbox'
                ]),
                'status' => 0,
                'sort_order' => 60,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '银联支付',
                'code' => 'unionpay',
                'description' => '中国银联在线支付',
                'logo' => null,
                'config' => json_encode([
                    'mer_id' => 'your_mer_id',
                    'cert_path' => 'path_to_cert',
                    'env' => 'sandbox'
                ]),
                'status' => 0,
                'sort_order' => 70,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => '银行转账',
                'code' => 'bank_transfer',
                'description' => '线下银行转账',
                'logo' => null,
                'config' => json_encode([
                    'bank_name' => '中国工商银行',
                    'account_name' => '公司名称',
                    'account_number' => '**********123456789'
                ]),
                'status' => 0,
                'sort_order' => 80,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        // 插入数据
        foreach ($paymentOptions as $option) {
            \DB::statement("INSERT INTO st_payment_options (name, code, description, logo, config, status, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", [
                $option['name'],
                $option['code'],
                $option['description'],
                $option['logo'],
                $option['config'],
                $option['status'],
                $option['sort_order'],
                $option['created_at']->format('Y-m-d H:i:s'),
                $option['updated_at']->format('Y-m-d H:i:s')
            ]);
        }

        // 验证结果
        $allOptions = \DB::select('SELECT * FROM st_payment_options ORDER BY sort_order');
        $enabledOptions = \DB::select('SELECT * FROM st_payment_options WHERE status = 1');

        return response()->json([
            'success' => true,
            'message' => 'Complete payment options created successfully',
            'total_created' => count($allOptions),
            'enabled_count' => count($enabledOptions),
            'domestic_count' => count(array_filter($allOptions, function($p) {
                return in_array($p->code, ['alipay', 'wechat_pay', 'unionpay', 'bank_transfer']);
            })),
            'international_count' => count(array_filter($allOptions, function($p) {
                return !in_array($p->code, ['alipay', 'wechat_pay', 'unionpay', 'bank_transfer']);
            })),
            'all_options' => $allOptions
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：调试后台支付分类
Route::get('debug/payment-classification', function() {
    try {
        // 模拟后台支付管理的分类逻辑
        $allPayments = \App\Models\PaymentOption::orderBy('sort_order', 'asc')->orderBy('id', 'asc')->get();

        $domesticPayments = collect();
        $internationalPayments = collect();

        // 使用相同的分类逻辑
        $controller = new \App\Http\Controllers\Strongadmin\PaymentCenterController();

        foreach ($allPayments as $payment) {
            // 使用反射调用私有方法
            $reflection = new ReflectionClass($controller);
            $method = $reflection->getMethod('isDomesticPayment');
            $method->setAccessible(true);

            if ($method->invoke($controller, $payment->code)) {
                $domesticPayments->push($payment);
            } else {
                $internationalPayments->push($payment);
            }
        }

        return response()->json([
            'total_payments' => $allPayments->count(),
            'domestic_count' => $domesticPayments->count(),
            'international_count' => $internationalPayments->count(),
            'domestic_payments' => $domesticPayments->map(function($p) {
                return [
                    'id' => $p->id,
                    'name' => $p->name,
                    'code' => $p->code,
                    'status' => $p->status
                ];
            }),
            'international_payments' => $internationalPayments->map(function($p) {
                return [
                    'id' => $p->id,
                    'name' => $p->name,
                    'code' => $p->code,
                    'status' => $p->status
                ];
            }),
            'bank_transfer_check' => [
                'exists' => $allPayments->where('code', 'bank_transfer')->count() > 0,
                'details' => $allPayments->where('code', 'bank_transfer')->first()
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 临时路由：调试支付配置保存
Route::post('debug/payment-config-save', function(\Illuminate\Http\Request $request) {
    return response()->json([
        'received_data' => $request->all(),
        'code_param' => $request->input('code'),
        'config_param' => $request->input('config'),
        'raw_input' => $request->getContent(),
        'headers' => $request->headers->all()
    ]);
});

// 临时路由：测试支付配置保存
Route::post('test/payment-config-save', function(\Illuminate\Http\Request $request) {
    try {
        $code = $request->input('code');
        $config = $request->input('config', []);

        \Log::info('测试保存支付配置', [
            'code' => $code,
            'config' => $config,
            'all_input' => $request->all()
        ]);

        if (!$code) {
            return response()->json([
                'code' => 4001,
                'message' => '支付方式代码不能为空',
                'received_data' => $request->all()
            ]);
        }

        // 查找支付方式
        $payment = \App\Models\PaymentOption::where('code', $code)->first();

        if (!$payment) {
            return response()->json([
                'code' => 4002,
                'message' => '支付方式不存在：' . $code
            ]);
        }

        // 更新配置
        $existingConfig = [];
        if ($payment->config) {
            $existingConfig = is_string($payment->config) ? json_decode($payment->config, true) : $payment->config;
        }

        $mergedConfig = array_merge($existingConfig, $config);

        // 强制更新配置
        $payment->config = json_encode($mergedConfig);
        $payment->updated_at = now();

        // 使用原始SQL确保保存
        $result = \DB::update("UPDATE st_payment_options SET config = ?, updated_at = ? WHERE id = ?", [
            json_encode($mergedConfig),
            now()->format('Y-m-d H:i:s'),
            $payment->id
        ]);

        \Log::info('保存结果', [
            'update_result' => $result,
            'payment_id' => $payment->id,
            'merged_config' => $mergedConfig
        ]);

        return response()->json([
            'code' => 200,
            'message' => '配置保存成功',
            'data' => [
                'id' => $payment->id,
                'code' => $payment->code,
                'old_config' => $existingConfig,
                'new_config' => $mergedConfig,
                'saved_config' => json_decode($payment->config, true)
            ]
        ]);

    } catch (\Exception $e) {
        \Log::error('测试保存配置失败', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        return response()->json([
            'code' => 5001,
            'message' => '保存失败：' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 游客订单查询路由
Route::prefix('guest')->name('guest.')->group(function () {
    Route::get('order-lookup', [App\Http\Controllers\GuestOrderController::class, 'index'])->name('order.lookup');
    Route::post('order-lookup', [App\Http\Controllers\GuestOrderController::class, 'lookup']);
    Route::get('order-tracking', [App\Http\Controllers\GuestOrderController::class, 'tracking'])->name('order.tracking');
    Route::post('order-tracking', [App\Http\Controllers\GuestOrderController::class, 'tracking']);
});

// 临时路由：启用自动注册功能
Route::get('debug/enable-auto-register', function() {
    try {
        \App\Models\SystemSetting::set('guest_order_auto_register', true, 'bool', 'guest_order', '游客下单后自动注册账户');

        return response()->json([
            'success' => true,
            'message' => '自动注册功能已启用',
            'setting' => \App\Models\SystemSetting::get('guest_order_auto_register', false)
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '启用失败：' . $e->getMessage()
        ]);
    }
});

// 临时路由：检查自动注册状态
Route::get('debug/check-auto-register', function() {
    try {
        $enabled = \App\Models\SystemSetting::get('guest_order_auto_register', false);

        return response()->json([
            'enabled' => $enabled,
            'message' => $enabled ? '自动注册功能已启用' : '自动注册功能未启用'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage()
        ]);
    }
});

// API路由：智能检查邮箱和手机号
Route::post('api/check-account', function(\Illuminate\Http\Request $request) {
    $email = $request->input('email');
    $phone = $request->input('phone');

    $result = ['can_proceed' => true, 'message' => '', 'action' => 'create'];

    if (!empty($email)) {
        $existingUser = \App\Models\User::where('email', $email)->first();
        if ($existingUser) {
            $userPhone = $existingUser->mobile ?? '';

            if (!empty($phone) && !empty($userPhone) && $phone !== $userPhone) {
                // 邮箱存在但手机号不匹配
                $result = [
                    'can_proceed' => false,
                    'message' => "该邮箱已注册，但手机号不匹配。如果这是您的账户，请登录后下单。\n注册手机号：" . substr($userPhone, 0, 3) . '****' . substr($userPhone, -4),
                    'action' => 'login_required'
                ];
            } else {
                // 邮箱存在且手机号匹配（或其中一个为空）
                $result = [
                    'can_proceed' => true,
                    'message' => '将自动关联到您的现有账户',
                    'action' => 'link_existing'
                ];
            }
        }
    }

    if ($result['can_proceed'] && !empty($phone)) {
        $existingUserByPhone = \App\Models\User::where('mobile', $phone)->first();
        if ($existingUserByPhone && $existingUserByPhone->email !== $email) {
            // 手机号被其他邮箱注册
            $result = [
                'can_proceed' => false,
                'message' => '该手机号已被其他邮箱注册，请使用其他手机号或登录原账户',
                'action' => 'change_phone'
            ];
        }
    }

    return response()->json($result);
});

// 线下支付路由
Route::prefix('payment/offline')->name('payment.offline.')->group(function () {
    Route::get('{orderId}', [App\Http\Controllers\Payment\OfflinePaymentController::class, 'show'])->name('show');
    Route::post('{orderId}/confirm', [App\Http\Controllers\Payment\OfflinePaymentController::class, 'confirm'])->name('confirm');
});

// 简化路由
Route::get('payment/offline/{orderId}', [App\Http\Controllers\Payment\OfflinePaymentController::class, 'show'])->name('payment.offline');

// 线下扫码支付路由
Route::get('payment/offline-qr', [App\Http\Controllers\Payment\OfflineQrPaymentController::class, 'show'])->name('payment.offline_qr');
Route::post('payment/offline-qr/confirm', [App\Http\Controllers\Payment\OfflineQrPaymentController::class, 'confirm'])->name('payment.offline_qr.confirm');

// 测试支付路由
Route::get('debug/payment/{orderId}/{paycode}', function($orderId, $paycode) {
    try {
        $controller = new App\Http\Controllers\Payment\IndexController();
        $request = new Illuminate\Http\Request();
        $request->merge(['orderId' => $orderId, 'paycode' => $paycode]);
        $request->headers->set('Accept', 'application/json');

        $result = $controller->index($request);

        return response()->json([
            'success' => true,
            'result_type' => get_class($result),
            'result' => $result instanceof \Illuminate\Http\JsonResponse ? $result->getData() : 'Not JSON response'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'line' => $e->getLine(),
            'file' => $e->getFile()
        ]);
    }
});

// 测试OfflineQrPayment模型
Route::get('debug/qr-payments', function() {
    try {
        $qrPayments = \App\Models\OfflineQrPayment::where('status', 1)
                                                 ->orderBy('sort_order', 'asc')
                                                 ->orderBy('id', 'asc')
                                                 ->get();

        return response()->json([
            'success' => true,
            'count' => $qrPayments->count(),
            'data' => $qrPayments->toArray()
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 测试错误处理
Route::get('debug/test-error/{code}', function($code) {
    $messages = [
        '4007' => '该邮箱已注册，但手机号不匹配。如果这是您的账户，请登录后下单；如果不是，请使用其他邮箱。\n注册手机号：123****5678',
        '4001' => '该邮箱已经注册，请使用其他邮箱或登录现有账户',
        '4002' => '该手机号已经注册，请使用其他手机号或登录现有账户',
        '4000' => '请求参数错误，请检查输入信息'
    ];

    return response()->json([
        'code' => (int)$code,
        'message' => $messages[$code] ?? '未知错误',
        'data' => null
    ]);
});

// 测试添加商品到购物车
Route::get('debug/add-to-cart', function() {
    try {
        // 获取第一个可用商品
        $product = \App\Models\Product\Product::where('status', 1)->whereNull('deleted_at')->first();

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => '没有找到可用的商品'
            ]);
        }

        // 获取UUID
        $uuid = app('strongshop')->uuid;
        $user = app('strongshop')->user;

        // 检查是否已经在购物车中
        $existingCart = \App\Models\Product\ShoppingCart::where('product_id', $product->id)
            ->where('is_buy_now', 0)
            ->where('uuid', $uuid)
            ->first();

        if ($existingCart) {
            // 如果已存在，增加数量
            $existingCart->qty += 1;
            $existingCart->save();
            $message = '商品数量已增加';
        } else {
            // 创建新的购物车记录
            $cartItem = new \App\Models\Product\ShoppingCart();
            $cartItem->user_id = $user ? $user->id : 0;
            $cartItem->uuid = $uuid;
            $cartItem->product_id = $product->id;
            $cartItem->qty = 1;
            $cartItem->is_buy_now = 0;
            $cartItem->product_price = $product->getOriginal('sale_price');
            $cartItem->save();
            $message = '商品已添加到购物车';
        }

        // 获取购物车总数
        $cartCount = \App\Models\Product\ShoppingCart::where('uuid', $uuid)
            ->where('is_buy_now', 0)
            ->sum('qty');

        return response()->json([
            'success' => true,
            'message' => $message,
            'product' => $product->title,
            'cart_count' => $cartCount,
            'uuid' => $uuid
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '添加失败：' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// 测试清空购物车
Route::get('debug/clear-cart', function() {
    try {
        $uuid = app('strongshop')->uuid;
        $user = app('strongshop')->user;

        // 清空购物车
        $query = \App\Models\Product\ShoppingCart::where('uuid', $uuid)->where('is_buy_now', 0);
        if ($user) {
            $query->orWhere('user_id', $user->id);
        }
        $deletedCount = $query->delete();

        return response()->json([
            'success' => true,
            'message' => "已清空购物车，删除了 {$deletedCount} 个商品",
            'uuid' => $uuid
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '清空失败：' . $e->getMessage()
        ]);
    }
});

// 测试非AJAX支付路由
Route::get('debug/payment-normal/{orderId}/{paycode}', function($orderId, $paycode) {
    try {
        $controller = new App\Http\Controllers\Payment\IndexController();
        $request = new Illuminate\Http\Request();
        $request->merge(['orderId' => $orderId, 'paycode' => $paycode]);
        // 不设置Accept: application/json，模拟普通请求

        $result = $controller->index($request);

        return response()->json([
            'success' => true,
            'result_type' => get_class($result),
            'result' => 'Normal request processed'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'line' => $e->getLine(),
            'file' => $e->getFile()
        ]);
    }
});

// 线下扫码支付管理路由（添加到admin.php中的路由组）

// 临时路由：调试银行转账配置
Route::get('debug/bank-transfer-config', function() {
    try {
        // 获取银行转账配置
        $bankTransferConfig = \DB::select("SELECT * FROM st_payment_options WHERE code = 'bank_transfer' LIMIT 1");

        if (empty($bankTransferConfig)) {
            return response()->json([
                'error' => '银行转账配置不存在'
            ]);
        }

        $config = json_decode($bankTransferConfig[0]->config ?? '{}', true);

        $bankInfo = [
            'bank_name' => $config['bank_name'] ?? '',
            'account_name' => $config['account_name'] ?? '',
            'account_number' => $config['account_number'] ?? '',
            'bank_address' => $config['bank_address'] ?? '',
            'swift_code' => $config['swift_code'] ?? '',
            'supported_currencies' => $config['supported_currencies'] ?? '',
            'transfer_instructions' => $config['transfer_instructions'] ?? '',
            'contact_info' => $config['contact_info'] ?? '',
            // 新增的联系方式字段
            'contact_email' => $config['contact_email'] ?? '',
            'contact_wechat' => $config['contact_wechat'] ?? '',
            'contact_phone' => $config['contact_phone'] ?? '',
            'service_hours' => $config['service_hours'] ?? '',
            'contact_qq' => $config['contact_qq'] ?? '',
            'wechat_qrcode' => $config['wechat_qrcode'] ?? ''
        ];

        return response()->json([
            'raw_config' => $config,
            'processed_bank_info' => $bankInfo,
            'has_contact_fields' => [
                'contact_email' => !empty($bankInfo['contact_email']),
                'contact_wechat' => !empty($bankInfo['contact_wechat']),
                'contact_phone' => !empty($bankInfo['contact_phone']),
                'service_hours' => !empty($bankInfo['service_hours']),
                'contact_qq' => !empty($bankInfo['contact_qq'])
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

