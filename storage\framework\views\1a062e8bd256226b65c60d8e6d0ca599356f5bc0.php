<!--错误信息-->
<div id="st-alert">
    <div class="container st-form-alert" style="display: none;">
        <div class="alert alert-dismissible fade in" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
            <p></p>
        </div>
    </div>
    <?php if($errors->any()): ?>
    <div class="st-h20"></div>
    <div class="container">
        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="alert alert-danger alert-dismissible fade in st-form-alert" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
            <p><?php echo e($error); ?></p>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <?php endif; ?>
    <?php if(session('status')): ?>
    <div class="st-h10"></div>
    <div class="container">
        <div class="alert alert-info" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
            <p><?php echo session('status'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div><?php /**PATH C:\Users\<USER>\Documents\GitHub\huaifeng\resources\views\themes\default/layouts/includes/errors.blade.php ENDPATH**/ ?>