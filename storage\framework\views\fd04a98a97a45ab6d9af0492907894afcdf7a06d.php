<!doctype html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <!-- 超级简单的扩展错误静默器 -->
        <script>
        // 最简单最直接的方法：完全静默特定错误
        (function() {
            // 重写console.error，过滤扩展错误
            const _error = console.error;
            console.error = function() {
                const msg = Array.from(arguments).join(' ');
                if (msg.indexOf('message channel closed') === -1 &&
                    msg.indexOf('Extension context invalidated') === -1 &&
                    msg.indexOf('listener indicated an asynchronous response') === -1) {
                    _error.apply(console, arguments);
                }
            };

            // 拦截window错误
            window.onerror = function(msg) {
                if (msg && msg.indexOf('message channel closed') > -1) return true;
                return false;
            };

            // 拦截Promise错误
            window.onunhandledrejection = function(e) {
                const msg = (e.reason && e.reason.message) || e.reason || '';
                if (msg.indexOf && msg.indexOf('message channel closed') > -1) {
                    e.preventDefault();
                    return true;
                }
                return false;
            };
        })();
        </script>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <meta name="renderer" content="webkit">
        <meta name="keywords" content="<?php echo e($_meta_keywords ?? ''); ?>">
        <meta name="description" content="<?php echo e($_meta_description ?? ''); ?>">
        <!-- CSRF Token -->
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <!-- 立即拦截扩展错误 - 最高优先级 -->
        <script>
        (function() {
            'use strict';

            // 立即拦截Promise rejection
            window.addEventListener('unhandledrejection', function(event) {
                const reason = event.reason || {};
                const message = reason.message || reason.toString();

                if (message.includes('message channel closed') ||
                    message.includes('Extension context invalidated') ||
                    message.includes('listener indicated an asynchronous response')) {
                    event.preventDefault();
                    event.stopImmediatePropagation();
                    return false;
                }
            }, true);

            // 立即拦截全局错误
            window.addEventListener('error', function(event) {
                const message = event.message || '';

                if (message.includes('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received')) {
                    event.preventDefault();
                    event.stopImmediatePropagation();
                    return false;
                }
            }, true);

            // 重写console.error以过滤扩展错误
            const originalConsoleError = console.error;
            console.error = function(...args) {
                const message = args.join(' ');

                if (message.includes('message channel closed') ||
                    message.includes('Extension context invalidated') ||
                    message.includes('listener indicated an asynchronous response')) {
                    // 静默处理扩展错误
                    return;
                }

                originalConsoleError.apply(console, args);
            };

            // 标记修复器已加载
            window.extensionErrorFixerLoaded = true;
        })();
        </script>

        <!-- User Info for Customer Service -->
        <?php if(auth()->guard()->check()): ?>
        <meta name="user-name" content="<?php echo e(auth()->user()->name ?? auth()->user()->username ?? ''); ?>">
        <meta name="user-email" content="<?php echo e(auth()->user()->email ?? ''); ?>">
        <meta name="user-id" content="<?php echo e(auth()->user()->id ?? ''); ?>">
        <?php endif; ?>
        <title><?php if(isset($_title)): ?><?php echo e($_title); ?> - <?php echo e(config('strongshop.storeName')); ?><?php else: ?> <?php echo e(app('strongshop')->getShopConfig('store_title')); ?> <?php endif; ?></title>
        <!-- Styles -->
        <link rel="stylesheet" href="<?php echo e(asset('css/bootstrap.min.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('css/bootstrap-theme.min.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('css/bootstrap-icons.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('css/main.css')); ?>?v=<?php echo e(env('APP_VERSION')); ?>">

        <!-- Scripts 兼容 ie8 自适应 -->
        <script src="<?php echo e(asset('js/vendor/modernizr-2.8.3-respond-1.4.2.min.js')); ?>"></script>

        <!-- 阻止外部字体加载，解决CORS问题 -->
        <script>
        (function() {
            // 安全的字体阻止函数
            function blockExternalFonts() {
                try {
                    // 移除现有的外部字体链接
                    const externalFontLinks = document.querySelectorAll('link[href*="shiptobuy.com"], link[href*="fonts.googleapis.com"]');
                    externalFontLinks.forEach(function(link) {
                        console.warn('移除外部字体链接:', link.href);
                        link.remove();
                    });

                    // 设置观察器（如果支持）
                    if (typeof MutationObserver !== 'undefined' && document.head) {
                        const observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(mutation) {
                                mutation.addedNodes.forEach(function(node) {
                                    if (node.nodeType === 1 && node.tagName === 'LINK') {
                                        if (node.href && (node.href.includes('shiptobuy.com') || node.href.includes('fonts.googleapis.com'))) {
                                            console.warn('阻止外部字体加载:', node.href);
                                            node.remove();
                                        }
                                    }
                                });
                            });
                        });

                        observer.observe(document.head, {
                            childList: true,
                            subtree: true
                        });
                    }
                } catch (error) {
                    console.warn('字体阻止功能错误:', error);
                }
            }

            // 立即执行一次
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', blockExternalFonts);
            } else {
                blockExternalFonts();
            }
        })();
        </script>

        <!--统计代码 - 条件加载-->
        <script>
        function loadStatisticalCode() {
            if (localStorage.getItem('cookieConsent') === 'accepted') {
                try {
                    const statisticalCode = <?php echo json_encode(app('strongshop')->getShopConfig('statistical_code') ?? '', JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP); ?>;
                    if (statisticalCode && statisticalCode.trim()) {
                        document.head.insertAdjacentHTML('beforeend', statisticalCode);
                    }
                } catch (error) {
                    console.warn('统计代码加载错误:', error);
                }
            }
        }

        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', loadStatisticalCode);
        </script>
        <?php echo $__env->yieldPushContent('styles'); ?>
        <?php echo $__env->yieldPushContent('scripts'); ?>
        <style>
/* 视频缩略图样式 */
.video-thumb {
    position: relative;
    cursor: pointer;
    list-style: none;
}
.video-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    color: white;
    font-size: 24px;
    text-shadow: 0 0 5px rgba(0,0,0,0.5);
    pointer-events: none;
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function(){
    // 缩略图点击切换
    document.querySelectorAll('.st-detail-img-left li').forEach(thumb => {
        thumb.addEventListener('click', function() {
            // 移除所有激活状态
            document.querySelectorAll('.st-detail-img-left li').forEach(li => li.classList.remove('active'));
            this.classList.add('active');

            // 主图切换
            const mainContainer = document.querySelector('.pic');
            if (this.classList.contains('video-thumb')) {
                // 显示视频
                mainContainer.innerHTML = `
                    <video 
                        id="mainVideo"
                        muted loop controls playsinline
                        poster="${this.querySelector('video').poster}"
                        style="width:100%"
                    >
                        <source src="${this.querySelector('source').src}" type="video/mp4">
                    </video>
                    <div class="magnify"></div>
                `;
                mainContainer.querySelector('video').play();
            } else {
                // 显示图片
                const imgSrc = this.querySelector('img').dataset.src;
                mainContainer.innerHTML = `
                    <img src="${imgSrc}" alt="">
                    <div class="magnify"></div>
                `;
            }
        });
    });

    // 自动播放视频缩略图
    document.querySelectorAll('.video-thumb video').forEach(v => {
        v.play().catch(() => v.load()); // 处理自动播放限制
    });
});
</script>
<script type="text/javascript">
    // Microsoft Clarity - 条件加载
    function loadMicrosoftClarity() {
        if (localStorage.getItem('cookieConsent') === 'accepted') {
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "reo4jc7ny6");
        }
    }

    // 页面加载时检查
    document.addEventListener('DOMContentLoaded', loadMicrosoftClarity);
</script>
    </head>
    <body id="app" class="st">







        <!--Cookie同意横幅-->
        <div id="cookie-consent-banner" style="display: none; position: fixed; bottom: 0; left: 0; right: 0; background: #333; color: #fff; padding: 15px; z-index: 10000; box-shadow: 0 -2px 10px rgba(0,0,0,0.3);">
            <div class="container" style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 15px;">
                <div style="flex: 1; min-width: 300px;">
                    <p style="margin: 0; font-size: 14px; line-height: 1.4;">
                        We use cookies to improve your experience and for analytics. By continuing to browse, you agree to our use of cookies.
                        <a href="/privacy-policy" style="color: #4CAF50; text-decoration: underline;">Learn more</a>
                    </p>
                </div>
                <div style="display: flex; gap: 10px; flex-shrink: 0;">
                    <button id="cookie-decline" style="background: transparent; border: 1px solid #666; color: #fff; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">
                        Decline
                    </button>
                    <button id="cookie-accept" style="background: #4CAF50; border: none; color: #fff; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 13px;">
                        Accept All
                    </button>
                </div>
            </div>
        </div>

        <!--顶部-提示信息-->
        <div class="st-navtip">
            <?php if(!isset($_COOKIE['strongshop_browserOutdated'])): ?>
            <!--[if lte IE 8]>
            <div class="container">
                <div class="alert alert-warning alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <p data-cookie="strongshop_browserOutdated">You are using an <strong>outdated</strong> browser. Please <a href="https://www.google.com/chrome/">upgrade your browser</a> to improve your experience.</p>
                </div>
            </div>
            <![endif]-->
            <?php endif; ?>
            <?php if(app('strongshop')->getShopConfig('notice') && !isset($_COOKIE['strongshop_notice'])): ?>
            <div class="container">
                <div class="alert alert-warning alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <p data-cookie="strongshop_notice"><?php echo app('strongshop')->getShopConfig('notice'); ?></p>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <!--导航区域-顶部 * 移动端隐藏-->
        <div class="st-navtop hidden-xs">
            <div class="container">
                <ul class="nav nav-pills pull-left st-navtop-items">
                    <?php if(false): ?>
                    <li>
                        <div id="st-google-translate-element"></div>
                    </li>
                    <?php else: ?>
                    <li class="dropdown st-navtop-items-account">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                            <i class="glyphicon glyphicon-globe"></i>
                            <font id="current-language">🇰🇭 ភាសាខ្មែរ</font><span class="caret"></span>
                        </a>
                        <ul class="dropdown-menu" id="language-menu">
                            <!-- 东南亚主要语言 -->
                            <li><a href="javascript:selectLanguage('khmer', '🇰🇭 ភាសាខ្មែរ');" style="font-weight: bold;">🇰🇭 ភាសាខ្មែរ</a></li>
                            <li role="separator" class="divider"></li>
                            <li><a href="javascript:selectLanguage('thai', '🇹🇭 ภาษาไทย');">🇹🇭 ภาษาไทย</a></li>
                            <li><a href="javascript:selectLanguage('vietnamese', '🇻🇳 Tiếng Việt');">🇻🇳 Tiếng Việt</a></li>
                            <li><a href="javascript:selectLanguage('malay', '🇲🇾 Bahasa Melayu');">🇲🇾 Bahasa Melayu</a></li>
                            <li><a href="javascript:selectLanguage('indonesian', '🇮🇩 Bahasa Indonesia');">🇮🇩 Bahasa Indonesia</a></li>
                            <li role="separator" class="divider"></li>
                            <li><a href="javascript:selectLanguage('chinese_simplified', '🇨🇳 简体中文');">🇨🇳 简体中文</a></li>
                            <li><a href="javascript:selectLanguage('english', '🇺🇸 English');">🇺🇸 English</a></li>

                        </ul>
                    </li>
                    <?php endif; ?>
                    <li class="dropdown st-navtop-items-account">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                            <font><?php echo e($_current_currency_name); ?></font>
                            <!--<span class="caret"></span>-->
                            <span aria-hidden="true" style="color: rgb(118, 118, 118);">▼</span>
                        </a>
                        <ul class="dropdown-menu">
                            <?php $__currentLoopData = $_currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li <?php if($currency['code'] == app('strongshop')->getCurrentCurrency()): ?> class="active" <?php endif; ?>>
                                <a rel="nofollow" href="<?php echo e(request()->fullUrlWithQuery(['currency'=>$currency['code']])); ?>" rel="nofollow"><?php echo e($currency['name']); ?></a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </li>
                </ul>
                <ul class="nav nav-pills pull-right st-navtop-items">
                    <li>
                        <a href="<?php echo e(route('user.my.feedback')); ?>" rel="nofollow">
                            <i class="glyphicon glyphicon-question-sign"></i><font><?php echo app('translator')->get('Feedback Us'); ?></font>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('user.orderTracking')); ?>" rel="nofollow">
                            <i class="glyphicon glyphicon-map-marker"></i><font><?php echo app('translator')->get('Order Tracking'); ?></font>
                        </a>
                    </li>
                    <li class="hidden-sm">
                        <a href="<?php echo e(route('shoppingcart')); ?>" rel="nofollow">
                            <i class="glyphicon glyphicon-shopping-cart"></i>
                            <font><?php echo app('translator')->get('Shopping Cart'); ?>
                            (<span class="st-cartnum"><?php if($_cart['total']['cart_qty_total']>99): ?>99+<?php else: ?><?php echo e($_cart['total']['cart_qty_total']); ?><?php endif; ?></span>)
                            </font>
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('user.my.collects')); ?>" rel="nofollow">
                            <i class="glyphicon glyphicon-heart-empty"></i><font><?php echo app('translator')->get('Wish List'); ?>(<span id="ST-WISH-LIST-TOTAL"><?php echo e($_wish_list_total); ?></span>)</font>
                        </a>
                    </li>
                    <?php if(auth()->guard()->guest()): ?>
                    <li>
                        <a href="<?php echo e(route('login')); ?>" rel="nofollow">
                            <i class="glyphicon glyphicon-log-in"></i><font><?php echo app('translator')->get('Sign in'); ?></font>
                        </a>
                    </li>
                    <?php endif; ?>
                    <li class="dropdown st-navtop-items-account">
                        <a href="<?php echo e(route('user.index')); ?>" class="dropdown-toggle" data-hover="dropdown">
                            <i class="glyphicon glyphicon-user"></i>
                            <font><?php echo app('translator')->get('My Account'); ?>
                            <?php if(auth()->guard()->check()): ?>
                            (<?php echo e(auth()->user()->nickname); ?>)
                            <span class="badge"><?php echo e($_unread_feedback_replies_total); ?></span>
                            <?php endif; ?>
                            </font>
                            <span class="caret"></span>
                        </a>
                        <ul class="dropdown-menu">
                            <?php if(auth()->guard()->guest()): ?>
                            <li><a href="<?php echo e(route('login')); ?>" rel="nofollow"><?php echo app('translator')->get('Sign in'); ?></a></li>
                            <li><a href="<?php echo e(route('register')); ?>" rel="nofollow"><?php echo app('translator')->get('Sign up'); ?></a></li>
                            <li role="separator" class="divider"></li>
                            <?php endif; ?>
                            <li><a href="<?php echo e(route('user.index')); ?>" rel="nofollow"><?php echo app('translator')->get('User Home'); ?></a></li>
                            <li><a href="<?php echo e(route('user.my.orders')); ?>" rel="nofollow"><?php echo app('translator')->get('My Orders'); ?></a></li>
                            <li><a href="<?php echo e(route('user.my.collects')); ?>" rel="nofollow"><?php echo app('translator')->get('My Wish List'); ?></a></li>
                            <li><a href="<?php echo e(route('user.my.feedback')); ?>" rel="nofollow"><?php echo app('translator')->get('My Feedback'); ?> <span class="badge"><?php echo e($_unread_feedback_replies_total); ?></span></a></li>
                            <?php if(auth()->guard()->check()): ?>
                            <li role="separator" class="divider"></li>
                            <li><a rel="nofollow" href="<?php echo e(route('logout')); ?>"><i class="glyphicon glyphicon-log-out"></i><font><?php echo app('translator')->get('Sign out'); ?></font></a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
        <!--导航区域-品牌、搜索和购物车 * 移动端隐藏-->
        <div class="st-navbrand hidden-xs">
            <div class="container">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="st-navbrand-logo">
                            <a href="/"><img src="<?php echo e(asset('img/logo.272x92.png')); ?>"class="img-responsive" alt="<?php echo e(config('app.name')); ?>"  title="<?php echo e(config('app.name')); ?>" /></a>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <!--<div class="st-navbrand-slogan hidden-sm"><?php echo app('translator')->get('Slogan'); ?></div>-->
                    </div>
                    <div class="col-sm-5">
                        <form id="ST-SEARCH" method="get" action="<?php echo e(route('product.list')); ?>">
                            <div class="input-group st-navbrand-search">
                                <input name="keywords" type="text" class="form-control" placeholder="<?php echo app('translator')->get('Search Products'); ?>" required="required" value="<?php echo e(request('keywords')); ?>" />
                                <div class="input-group-addon" onclick="document.getElementById('ST-SEARCH').submit();">
                                    <i class="glyphicon glyphicon-search"></i>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-sm-2">
                        <div id="ST-NAVCART-ICON"  class="st-navbrand-cart pull-right">
                            <a rel="nofollow" href="<?php echo e(route('shoppingcart')); ?>"><i class="glyphicon glyphicon-shopping-cart"></i>Cart</a>
                            <span class="badge st-cartnum"><?php if($_cart['total']['cart_qty_total']>99): ?> 99+ <?php else: ?> <?php echo e($_cart['total']['cart_qty_total']); ?> <?php endif; ?></span>
                        </div>
                        <?php if(!request()->route()->named(['shoppingcart', 'shoppingcart.checkout'])): ?>
                        <!--导航区域-购物车-->
                        <div id="ST-NAVCART-PRODUCTS">
                            <div class="page-header st-navbrand-cart-total">
                                <?php echo $__env->make('layouts.includes.shoppingcartBtn', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <div class="st-navbrand-cart-product-list st-cart-product-list">
                                <?php echo $__env->make('layouts.includes.shoppingcart', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <!--导航区域-菜单 * 移动端隐藏-->
        <div class="st-navmenu hidden-xs">
            <div class="container">
                <ul class="nav nav-pills">
                    <li ><a href="/"><?php echo app('translator')->get('Home'); ?></a></li>
                    <li id="products">
                        <a href="<?php echo e(route('product.list')); ?>"><?php echo app('translator')->get('Product Categories'); ?></a>
                        <div class="st-allcat panel panel-default">
                            <!-- List 一级分类 -->
                            <ul class="list-group st-allcat-items">
                                <?php $__currentLoopData = $_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="list-group-item">
                                    <a href="<?php echo e(route('product.list.rewrite', ['catid'=>$category->id])); ?>">
                                        <i class="bi-life-preserver"></i>
                                        <font><?php echo e($category->name); ?></font>
                                    </a>
                                </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <div class="st-allcat-content">
                                <?php $__currentLoopData = $_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="st-allcat-content-item">
                                    <?php $__currentLoopData = $category->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <dl>
                                        <!--二级分类-->
                                        <dt><a href="<?php echo e(route('product.list.rewrite', ['catid'=>$child->id])); ?>"><?php echo e($child['name']); ?></a></dt>
                                        <?php $__currentLoopData = $child['children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $childChild): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <!--三级分类-->
                                        <dd><a href="<?php echo e(route('product.list.rewrite', ['catid'=>$childChild->id])); ?>"><?php echo e($childChild['name']); ?></a></dd>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </dl>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </li>
                
                   <!-- <li ><a href="#">Women</a></li>
                    <li ><a href="#">Man</a></li>
                    <li ><a href="#"><?php echo app('translator')->get('Promotion'); ?></a></li>-->
                    <li ><a href="<?php echo e(route('user.my.feedback')); ?>"><?php echo app('translator')->get('Feedback Us'); ?></a></li>
                </ul>
            </div>
        </div>
        <!-- 头部导航 * 移动端显示 -->
        <nav class="navbar navbar-default st-header visible-xs-block">
            <div class="container-fluid">
                <!-- Brand and toggle get grouped for better mobile display -->
                <div class="row">
                    <div class="navbar-header">
                        <div class="col-xs-2">
                            <button type="button" class="navbar-toggle collapsed pull-left" data-toggle="collapse" data-target="#nav-product-categories" aria-expanded="false">
                                <span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span>
                            </button>
                        </div>
                        <div class="col-xs-6 navbar-brand-container">
                            <a class="navbar-brand" href="/" style="display: block; max-width: 272px; padding:5px 0;">
                                <img src="<?php echo e(asset('img/logo.272x92.png')); ?>" class="img-responsive" alt="<?php echo e(config('app.name')); ?>"  title="<?php echo e(config('app.name')); ?>" style="width: 100%; height: auto; max-height: 92px; object-fit: contain; display: block; margin: 0 auto;"/>
                            </a>
                        </div>
                        <div class="col-xs-4">
                            <span class="bi bi-person-circle pull-right st-nav-user" data-toggle="collapse" data-target="#nav-user" aria-expanded="true"></span>
                            <a href="<?php echo e(route('shoppingcart')); ?>" class="st-header-cart pull-right">
                                <i class="glyphicon glyphicon-shopping-cart"></i>
                                <span class="st-cartnum"><?php if($_cart['total']['cart_qty_total']>99): ?> 99+ <?php else: ?> <?php echo e($_cart['total']['cart_qty_total']); ?> <?php endif; ?></span>
                            </a>
                        </div>
                    </div>
                </div>
                <!-- Collect the nav links, forms, and other content for toggling -->
                <div class="collapse navbar-collapse" id="nav-product-categories">
                    <form class="navbar-form" id="ST-SEARCH-M" method="get" action="<?php echo e(route('product.list')); ?>">
                        <div class="input-group">
                            <input type="text" name="keywords" class="form-control" placeholder="<?php echo app('translator')->get('Search Products'); ?>" required="required" value="<?php echo e(request('keywords')); ?>">
                            <div class="input-group-addon" onclick="document.getElementById('ST-SEARCH-M').submit();"><i class="glyphicon glyphicon-search"></i></div>
                        </div>
                    </form>
                    <ul class="nav navbar-nav">
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="true"><?php echo app('translator')->get('Product Categories'); ?><span class="caret"></span></a>
                            <ul class="dropdown-menu open st-cat">
                                <!--一级分类-->
                                <?php $__currentLoopData = $_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="dropdown">
                                    <a href="<?php echo e(route('product.list.rewrite', ['catid'=>$category->id])); ?>"><?php echo e($category->name); ?></a>
                                    <ul class="dropdown-menu show st-subcat">
                                        <!--二级分类-->
                                        <?php $__currentLoopData = $category->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="dropdown">
                                            <a href="<?php echo e(route('product.list.rewrite', ['catid'=>$child->id])); ?>"><?php echo e($child->name); ?></a>
                                            <ul class="dropdown-menu show st-subsubcat">
                                                <!--三级分类-->
                                                <?php $__currentLoopData = $child['children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $childChild): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li><a href="<?php echo e(route('product.list.rewrite', ['catid'=>$childChild->id])); ?>"><?php echo e($childChild->name); ?></a></li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </li>
                    </ul>
                </div><!-- /.navbar-collapse -->
                <!-- Collect the nav links, forms, and other content for toggling -->
                <div class="collapse navbar-collapse" id="nav-user">
                    <ul class="nav navbar-nav">
                        <li class="dropdown"><a href="/"><?php echo app('translator')->get('Home'); ?></a></li>
                        <?php if(auth()->guard()->guest()): ?>
                        <li class="dropdown"><a rel="nofollow" href="<?php echo e(route('login')); ?>"><?php echo app('translator')->get('Sign in'); ?></a></li>
                        <li class="dropdown"><a rel="nofollow" href="<?php echo e(route('register')); ?>"><?php echo app('translator')->get('Sign up'); ?></a></li>
                        <?php endif; ?>
                        <li>
                            <a rel="nofollow" href="<?php echo e(route('user.index')); ?>">
                                <?php echo app('translator')->get('My Account'); ?>
                                <?php if(auth()->guard()->check()): ?>
                                (<?php echo e(auth()->user()->nickname); ?>)
                                <?php endif; ?>
                            </a>
                        </li>
                        <li><a rel="nofollow" href="<?php echo e(route('user.my.orders')); ?>"><?php echo app('translator')->get('My Orders'); ?></a></li>
                        <li><a rel="nofollow" href="<?php echo e(route('user.my.collects')); ?>"><?php echo app('translator')->get('My Wish List'); ?></a></li>
                        <li><a rel="nofollow" href="<?php echo e(route('user.my.feedback')); ?>"><?php echo app('translator')->get('My Feedback'); ?> <span class="badge"><?php echo e($_unread_feedback_replies_total); ?></span></a></li>
                        <li role="separator" class="divider"></li>
                        <li class="dropdown"><a rel="nofollow" href="/article-53.html"><?php echo app('translator')->get('Contact Us'); ?></a></li>
                        <li class="dropdown"><a rel="nofollow" href="<?php echo e(route('user.my.feedback')); ?>"><?php echo app('translator')->get('Feedback us'); ?></a></li>
                        <li role="separator" class="divider"></li>
                        <li class="dropdown st-navtop-items-account">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <i class="glyphicon glyphicon-globe"></i>
                                <font id="current-language-mobile">🇰🇭 ភាសាខ្មែរ</font><span class="caret"></span>
                            </a>
                            <ul class="dropdown-menu">
                                <!-- 东南亚主要语言 -->
                                <li><a href="javascript:selectLanguage('khmer', '🇰🇭 ភាសាខ្មែរ');" style="font-weight: bold;">🇰🇭 ភាសាខ្មែរ</a></li>
                                <li role="separator" class="divider"></li>
                                <li><a href="javascript:selectLanguage('thai', '🇹🇭 ภាษាไทย');">🇹🇭 ภាษាไทย</a></li>
                                <li><a href="javascript:selectLanguage('vietnamese', '🇻🇳 Tiếng Việt');">🇻🇳 Tiếng Việt</a></li>
                                <li><a href="javascript:selectLanguage('malay', '🇲🇾 Bahasa Melayu');">🇲🇾 Bahasa Melayu</a></li>
                                <li><a href="javascript:selectLanguage('indonesian', '🇮🇩 Bahasa Indonesia');">🇮🇩 Bahasa Indonesia</a></li>
                                <li role="separator" class="divider"></li>
                                <li><a href="javascript:selectLanguage('chinese_simplified', '🇨🇳 简体中文');">🇨🇳 简体中文</a></li>
                                <li><a href="javascript:selectLanguage('english', '🇺🇸 English');">🇺🇸 English</a></li>
                            </ul>
                        </li>
                        <li class="dropdown st-navtop-items-account">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <font><?php echo e($_current_currency_name); ?></font><span class="caret"></span>
                            </a>
                            <ul class="dropdown-menu">
                                <?php $__currentLoopData = $_currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li <?php if($currency['code'] == app('strongshop')->getCurrentCurrency()): ?> class="active" <?php endif; ?>>
                                    <a rel="nofollow" href="<?php echo e(request()->fullUrlWithQuery(['currency'=>$currency['code']])); ?>"><?php echo e($currency['name']); ?></a>
                                </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </li>
                        <?php if(auth()->guard()->check()): ?>
                        <li role="separator" class="divider"></li>
                        <li><a rel="nofollow" href="<?php echo e(route('logout')); ?>"><i class="glyphicon glyphicon-log-out"></i><font><?php echo app('translator')->get('Sign out'); ?></font></a></li>
                        <?php endif; ?>

                    </ul>
                </div><!-- /.navbar-collapse -->
            </div>
        </nav>

        <?php echo $__env->make('layouts.includes.errors', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php echo $__env->yieldContent('content'); ?>
        <!--        <div class="st-translate">
                    <div class="st-h100"></div>
                    <div class="container">
                        <p id="google_translate_element" class="pull-right"></p>
                    </div>
                </div>-->
        <!--底部信息-->
        <div class="st-footer">
            <div class="st-footer-service">
                <div class="container">
                    <div class="row">
                        <div class="col-sm-6 col-md-3">
                            <dl>
                                <dt><?php echo app('translator')->get('Services'); ?></dt>
                                <dd><a href="<?php echo e(route('user.my.feedback')); ?>"><?php echo app('translator')->get('Feedback'); ?></a><dd>
                                <dd><a href="<?php echo e(route('article.show.postid', ['postid'=>'privacy'])); ?>"><?php echo app('translator')->get('Privacy & Security'); ?></a><dd>
                            </dl>
                        </div>
                   <!--     <div class="col-sm-6 col-md-3">
                            <dl>
                                <dt><?php echo app('translator')->get('Shopping with us'); ?></dt>
                                <dd><a href="<?php echo e(route('article.show.postid', ['postid'=>'delivery'])); ?>"><?php echo app('translator')->get('Delivery'); ?></a><dd>
                                <dd><a href="<?php echo e(route('article.show.postid', ['postid'=>'returns'])); ?>"><?php echo app('translator')->get('Returns'); ?></a><dd>
                            </dl>
                        </div>-->
                        <div class="col-sm-6 col-md-3">
                            <dl>
                                <dt><?php echo app('translator')->get('Customer Support'); ?></dt>
                                <dd><a href="<?php echo e(route('article.show.postid', ['postid'=>'aboutus'])); ?>"><?php echo app('translator')->get('About us'); ?></a><dd>
                                <dd><a href="<?php echo e(route('article.show.postid', ['postid'=>'contactus'])); ?>"><?php echo app('translator')->get('Contact us'); ?></a><dd>
                            </dl>
                        </div>
                        <!--<div class="col-sm-6 col-md-3">
                            <dl>
                                <dt><?php echo app('translator')->get('Connect with us'); ?></dt>
                                <dd class="st-footer-service-icon">
                                    <a href="#" >
                                        <i class="bi-facebook"></i>
                                    </a>
                                    <a href="#" >
                                        <i class="bi-instagram"></i>
                                    </a>
                                    <a href="#" >
                                        <i class="bi-pinterest"><svg t="1624281165955" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3033" width="22" height="20"><path d="M512 0.040958c-282.75605 0-511.979521 229.223471-511.979521 511.979521 0 216.895004 134.937323 402.149674 325.393704 476.75533-4.484941-40.50782-8.519339-102.641654 1.781689-146.876685 9.297548-39.954882 60.044958-254.49478 60.044958-254.49478s-15.318427-30.657334-15.318427-75.99824c0-71.185633 41.265549-124.329107 92.647814-124.329107 43.682093 0 64.775649 32.787169 64.775649 72.107196 0 43.927843-27.954082 109.604576-42.412384 170.448222-12.062238 50.972681 25.558018 92.524939 75.813927 92.524939 91.00948 0 160.945882-95.965441 160.945882-234.466141 0-122.588376-88.080957-208.293748-213.864085-208.293748-145.668413 0-231.168993 109.276909-231.168993 222.178633 0 44.00976 16.956762 91.193792 38.091276 116.833727 4.177753 5.078837 4.792128 9.522819 3.542898 14.683573-3.891044 16.178553-12.512779 50.952202-14.212551 58.078957-2.232231 9.358986-7.413463 11.345466-17.120595 6.840046-63.956482-29.776729-103.931843-123.264189-103.931843-198.340866 0-161.49882 117.345706-309.829527 338.275109-309.829527 177.595456 0 315.625135 126.561338 315.625135 295.698892 0 176.448622-111.24291 318.451262-265.655934 318.451262-51.873765 0-100.655174-26.950602-117.345706-58.795728 0 0-25.680893 97.767609-31.886085 121.707772-11.570737 44.460302-42.76053 100.204632-63.649294 134.220551 47.900804 14.826927 98.812048 22.834287 151.607376 22.834287 282.75605 0 511.979521-229.223471 511.979521-511.979521s-229.223471-511.979521-511.979521-511.979521z" p-id="3034" fill="#707070"></path></svg></i>
                                    </a>
                                </dd>
                                <dd class="st-footer-service-signup">
                                    <p><?php echo app('translator')->get('Sign Up for Our Newsletter'); ?>:</p>
                                    <div class="input-group">
                                        <input type="text" class="form-control" placeholder="Enter your email">
                                        <span class="input-group-addon" id="basic-addon2"><?php echo app('translator')->get('Subscribe'); ?></span>
                                    </div>
                                </dd>
                            </dl>
                        </div>-->
                    </div>
                </div>
            </div>
            <!--备案信息-->
            <div class="st-footer-beian">
                <div class="container">
                    &copy; <?php echo e(date('Y')); ?> <?php echo e(config('app.name')); ?> Copyright, All Rights Reserved. Powered By <a target="_blank" href="http://www.shiptobuy.com"><?php echo e(config('app.name')); ?> </a>
                </div>
             
            </div>
            <div class="st-h10"></div>
        </div>
        <!-- Scripts -->
        <script src="<?php echo e(asset('js/vendor/jquery-1.11.2.min.js')); ?>"></script>
        <script src="<?php echo e(asset('js/vendor/jquery.form.min.js')); ?>"></script>
        <script src="<?php echo e(asset('js/vendor/jquery.cookie.js')); ?>"></script>
        <script src="<?php echo e(asset('js/vendor/bootstrap.min.js')); ?>"></script>
        <script src="<?php echo e(asset('js/vendor/bootstrap-hover-dropdown.js')); ?>"></script>
        <script src="<?php echo e(asset('plugins/layer/layer.js')); ?>"></script>
        <script src="<?php echo e(asset('js/main.js')); ?>?v=<?php echo e(env('APP_VERSION')); ?>"></script>
        <?php if(env('BlockSimpliedChineseBrowerVisitByJS')): ?>
        <script>
                                if (Util.maybe360Browser()) {
                                    console.log('360Browser');
                                    window.location.href = 'http://www.baidu.com';
                                }
        </script>
        <?php endif; ?>
        <script>
        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.warn('JavaScript错误:', e.error);
            return true; // 阻止错误冒泡
        });

        // 安全的初始化
        try {
            //js 初始化
            if (typeof Util !== 'undefined' && Util.init) Util.init();
            //产品分类菜单
            if (typeof Util !== 'undefined' && Util.allCategories) Util.allCategories();
            //顶部全局通知手动关闭后不再显示
            if (typeof Util !== 'undefined' && Util.navNotice) Util.navNotice();
            //导航区域-购物车(显隐)
            if (typeof Util !== 'undefined' && Util.navCart) Util.navCart();
        } catch (error) {
            console.warn('初始化错误:', error);
        }

// Cookie同意横幅逻辑
(function() {
    // 等待所有函数定义完成后再执行
    document.addEventListener('DOMContentLoaded', function() {
        const banner = document.getElementById('cookie-consent-banner');
        const acceptBtn = document.getElementById('cookie-accept');
        const declineBtn = document.getElementById('cookie-decline');

        // 检查是否已经做出选择
        const cookieConsent = localStorage.getItem('cookieConsent');

        if (!cookieConsent && banner) {
            // 显示横幅
            banner.style.display = 'block';
        }

        // 接受Cookie
        if (acceptBtn) {
            acceptBtn.addEventListener('click', function() {
                localStorage.setItem('cookieConsent', 'accepted');
                if (banner) banner.style.display = 'none';

                // 加载所有第三方服务
                if (typeof loadGoogleTagManager === 'function') loadGoogleTagManager();
                if (typeof loadMicrosoftClarity === 'function') loadMicrosoftClarity();
                if (typeof loadStatisticalCode === 'function') loadStatisticalCode();
                if (typeof loadGoogleTranslate === 'function') loadGoogleTranslate();
                if (typeof loadGoogleAnalytics === 'function') loadGoogleAnalytics();
            });
        }

        // 拒绝Cookie
        if (declineBtn) {
            declineBtn.addEventListener('click', function() {
                localStorage.setItem('cookieConsent', 'declined');
                if (banner) banner.style.display = 'none';
            });
        }
    });
})();
        </script>

        <?php echo $__env->yieldPushContent('scripts_bottom'); ?>
        <!-- Google Analytics - 条件加载 -->


      <!-- Responsive Popup Code - Perfect Proportions + Mobile Adaption 
<div id="email-popup" style="display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.4);z-index:9999;justify-content:center;align-items:center;font-family:'Nunito', sans-serif;">
    <div style="background:#fff;border-radius:16px;width:90%;max-width:800px;max-height:90vh;display:flex;flex-direction:row;position:relative;overflow:hidden;box-shadow:0 5px 20px rgba(0,0,0,0.08);">
   
        <button id="close-popup" style="position:absolute;top:15px;right:15px;width:32px;height:32px;background:#f5f5f5;border:none;border-radius:50%;color:#888;font-size:18px;cursor:pointer;display:flex;justify-content:center;align-items:center;z-index:2;">×</button>
        
 
        <div style="width:50%;aspect-ratio:1/1;background:#f9f9f9;position:relative;overflow:hidden;">
            <img src="https://images.unsplash.com/photo-1556740738-b6a63e27c4df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
                 style="width:100%;height:100%;object-fit:cover;">
   
            <div style="position:absolute;bottom:20px;left:20px;background:rgba(255,255,255,0.9);padding:6px 12px;border-radius:16px;font-size:12px;font-weight:600;color:#ff6b8b;box-shadow:0 2px 8px rgba(0,0,0,0.1);">
                ✨ Limited Offer
            </div>
        </div>
        

        <div style="width:50%;padding:30px;box-sizing:border-box;display:flex;flex-direction:column;overflow-y:auto;">
            <div style="margin-bottom:20px;">
                <h2 style="font-size:24px;color:#333;margin:0 0 10px 0;font-weight:600;">Exclusive Deal</h2>
                <p style="color:#666;margin:0;line-height:1.5;font-size:15px;">
                    Get <span style="font-weight:bold;color:#ff6b8b;">$100 OFF</span> on your first order over <span style="font-weight:bold;color:#ff6b8b;">$1,000</span>
                </p>
            </div>
            

            <form id="email-form" style="margin-top:auto;margin-bottom:15px;">
                <div style="margin-bottom:15px;position:relative;">
                    <input type="email" name="email" placeholder="Enter your email" required 
                           style="width:100%;padding:12px 15px;border:1px solid #e0e0e0;border-radius:8px;font-size:14px;background:#fafafa;padding-left:40px;">
                    <span style="position:absolute;left:15px;top:50%;transform:translateY(-50%);color:#aaa;">✉️</span>
                </div>
                <button type="submit" style="width:100%;padding:14px;background:#ff6b8b;color:white;border:none;border-radius:8px;font-size:15px;font-weight:600;cursor:pointer;transition:all 0.2s;">
                    Claim Discount
                </button>
                <?php echo csrf_field(); ?>
            </form>
            
           
            <div id="success-message" style="display:none;background:#f8f9fa;border-radius:8px;padding:15px;text-align:center;">
                <p style="color:#4CAF50;font-weight:600;margin-bottom:5px;">🎉 Success!</p>
                <p style="color:#666;font-size:13px;">Your discount code has been sent</p>
            </div>
            
            
            <p style="color:#999;font-size:12px;text-align:center;margin-top:15px;">
                We respect your privacy and won't share your email
            </p>
        </div>
    </div>
</div>-->

<!-- 使用本地字体，避免CORS问题 -->

<style>
    #close-popup:hover {
        background: #eee;
        color: #555;
        transform: rotate(90deg);
    }
    
    button[type="submit"]:hover {
        background: #ff5b7d;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255,107,139,0.3);
    }
    
    input:focus {
        border-color: #ff6b8b !important;
        background: #fff !important;
        outline: none;
        box-shadow: 0 0 0 3px rgba(255,107,139,0.2);
    }
    
    @keyframes  fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* Responsive Adjustments */
    @media (max-width: 768px) {
        #email-popup > div {
            flex-direction: column;
            max-height: 90vh;
        }
        #email-popup > div > div {
            width: 100% !important;
        }
        #email-popup > div > div:first-child {
            aspect-ratio: 16/9;
            max-height: 40vh;
        }
        #email-popup > div > div:last-child {
            padding: 25px;
            max-height: 60vh;
        }
    }
    
    /* Scrollbar Styling */
    ::-webkit-scrollbar {
        width: 6px;
    }
    ::-webkit-scrollbar-thumb {
        background: rgba(0,0,0,0.1);
        border-radius: 3px;
    }
</style>

    <!--<script>
document.addEventListener('DOMContentLoaded', function() {
    try {
        // 安全检查邮件弹窗元素是否存在
        const emailPopup = document.getElementById('email-popup');
        const closePopup = document.getElementById('close-popup');
        const emailForm = document.getElementById('email-form');

        if (!emailPopup || !closePopup || !emailForm) {
            console.warn('邮件弹窗元素不存在，跳过初始化');
            return;
        }

        // Show popup after 3 seconds
        setTimeout(function() {
            if (emailPopup) {
                emailPopup.style.display = 'flex';
            }
        }, 3000);

        // Close popup
        closePopup.addEventListener('click', function() {
            if (emailPopup) {
                emailPopup.style.display = 'none';
            }
        });

        // Close when clicking outside
        emailPopup.addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });

        // Form submission
        emailForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Disable button to prevent multiple submissions
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = 'Processing...';

            fetch('/submit-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
                },
                body: JSON.stringify({
                    email: document.querySelector('input[name="email"]').value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('email-form').style.display = 'none';
                    document.getElementById('success-message').style.display = 'block';

                    setTimeout(function() {
                        document.getElementById('email-popup').style.display = 'none';
                    }, 2000);
                } else {
                    alert(data.message);
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = 'Claim Discount';
                }
            })
            .catch(error => {
                alert('Submission failed, please try again');
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Claim Discount';
            });
        });
    } catch (error) {
        console.warn('邮件弹窗初始化错误:', error);
    }
});




</script>-->
    </body>
    <!-- Google Tag Manager (noscript) - 条件加载
    <script>
    if (localStorage.getItem('cookieConsent') === 'accepted') {
        document.body.insertAdjacentHTML('beforeend',
            '<noscript class="notranslate"><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NNHXPH7G" height="0" width="0" style="display:none;visibility:hidden" class="notranslate"></iframe></noscript>'
        );
    }
    </script>
   End Google Tag Manager (noscript) -->



    <script>
    // 立即定义selectLanguage函数，避免未定义错误
    window.selectLanguage = function(languageCode, displayName) {
        // 如果translate.js还未加载，先更新显示，稍后再执行翻译
        updateLanguageDisplayOnly(languageCode, displayName);

        // 延迟执行翻译，等待translate.js加载
        setTimeout(function() {
            if (typeof translate !== 'undefined') {
                translate.changeLanguage(languageCode);
            }
        }, 100);

        // 更新顶部显示
        const currentLangEl = document.getElementById('current-language');
        const currentLangMobileEl = document.getElementById('current-language-mobile');

        if (currentLangEl) {
            currentLangEl.textContent = displayName;
           //  console.log('桌面端显示已更新为:', displayName);
        }
        if (currentLangMobileEl) {
            currentLangMobileEl.textContent = displayName;
           //  console.log('移动端显示已更新为:', displayName);
        }

        // 如果translate.js可用，也执行翻译
        if (typeof translate !== 'undefined' && translate.changeLanguage) {
            translate.changeLanguage(languageCode);
        }
    };

    // 仅更新语言显示的函数（不执行翻译）
    function updateLanguageDisplayOnly(languageCode, displayName) {
        const currentLangEl = document.getElementById('current-language');
        const currentLangMobileEl = document.getElementById('current-language-mobile');

        if (currentLangEl) {
            currentLangEl.textContent = displayName;
        }
        if (currentLangMobileEl) {
            currentLangMobileEl.textContent = displayName;
        }
    };
    </script>

    <!-- translate.js 翻译插件 - 异步加载 -->
    <script>
    // 异步加载translate.js，避免阻塞页面
    (function() {
        const script = document.createElement('script');
        script.src = 'https://cdn.staticfile.net/translate.js/3.16.0/translate.js';
        script.async = true;
        script.onload = function() {
            // translate.js加载完成后初始化
            setTimeout(initTranslate, 100);
        };
        script.onerror = function() {
            console.warn('translate.js加载失败，翻译功能不可用');
        };
        document.head.appendChild(script);
    })();
    </script>

    <script>
    // 优化后的translate.js初始化
    function initTranslate() {
        if (typeof translate !== 'undefined') {
            // translate.js加载成功，快速初始化

            // 语言名称映射（使用translate.js支持的语言代码）
            const languageNames = {
                'chinese_simplified': '🇨🇳 简体中文',
                'chinese_traditional': '🇹🇼 繁體中文',
                'english': '🇺🇸 English',
                'japanese': '🇯🇵 日本語',
                'korean': '🇰� 한국어',
                'thai': '🇹🇭 ภាษាไทย',
                'vietnamese': '🇻🇳 Tiếng Việt',
                'malay': '🇲🇾 Bahasa Melayu',
                'indonesian': '🇮🇩 Bahasa Indonesia',
                'spanish': '�🇸 Español',
                'french': '🇫🇷 Français',
                'german': '🇩🇪 Deutsch',
                'russian': '🇷🇺 Русский',
                'portuguese': '🇧🇷 Português',
                'arabic': '🇸🇦 العربية',
                'hindi': '🇮🇳 हिन्दी'
            };

            // 更新语言显示函数
            function updateLanguageDisplay(language) {
                // console.log('开始更新语言显示，语言代码:', language);

                const displayName = languageNames[language] || '🇰🇭 ភាសាខ្មែរ';
                 //console.log('显示名称:', displayName);

                // 更新顶部显示
                const currentLangEl = document.getElementById('current-language');
                const currentLangMobileEl = document.getElementById('current-language-mobile');

                if (currentLangEl) {
                    currentLangEl.textContent = displayName;
                   //  console.log('桌面端顶部已更新');
                }
                if (currentLangMobileEl) {
                    currentLangMobileEl.textContent = displayName;
                   //  console.log('移动端顶部已更新');
                }

                // 更新下拉菜单中的选中状态
                updateMenuSelection(language);

                // console.log('语言显示更新完成');
            }

            // 更新菜单选中状态
            function updateMenuSelection(selectedLanguage) {
                // console.log('更新菜单选中状态:', selectedLanguage);

                // 桌面端菜单
                const desktopMenuItems = document.querySelectorAll('#language-menu a');
                desktopMenuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes("'" + selectedLanguage + "'")) {
                        item.style.fontWeight = 'bold';
                        item.style.backgroundColor = '#f5f5f5';
                        // console.log('桌面端菜单项已高亮:', selectedLanguage);
                    } else {
                        item.style.fontWeight = 'normal';
                        item.style.backgroundColor = '';
                    }
                });

                // 移动端菜单
                const mobileMenuItems = document.querySelectorAll('.navbar-collapse .dropdown-menu a');
                mobileMenuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes("'" + selectedLanguage + "'")) {
                        item.style.fontWeight = 'bold';
                        item.style.backgroundColor = '#f5f5f5';
                        // console.log('移动端菜单项已高亮:', selectedLanguage);
                    } else {
                        item.style.fontWeight = 'normal';
                        item.style.backgroundColor = '';
                    }
                });
            }

            // 手动测试函数
            window.testUpdateLanguage = function(lang) {
               //  console.log('手动测试更新语言:', lang);
                updateLanguageDisplay(lang);
            };

            // 快速配置，减少初始化时间
            translate.language.setLocal('chinese_simplified');
            translate.service.use('client.edge');
            translate.selectLanguageTag.show = false;

            // 优化性能设置
            translate.execute.timeout = 3000; // 3秒超时
            translate.execute.retry = 1; // 只重试1次

            // 忽略不需要翻译的元素，提升性能
            translate.ignore.tag.push('iframe', 'script', 'style', 'noscript', 'code', 'pre');
            translate.ignore.class.push('notranslate', 'gtm-noscript', 'no-translate');

            // 语言切换完成回调
            translate.listener.changeLanguageFinish = function(language) {
                // console.log('🎉 translate.listener.changeLanguageFinish 被触发!');
                // console.log('语言切换完成:', language);
                updateLanguageDisplay(language);
            };

            // 添加其他可能的回调
            translate.listener.renderTaskFinish = function(task) {
                //console.log('🎉 translate.listener.renderTaskFinish 被触发!');
                // console.log('渲染任务完成:', task);

                // 获取当前语言状态
                const currentLanguage = translate.language.getCurrent();
                 //console.log('🔍 当前translate语言状态:', currentLanguage);

                if (currentLanguage && currentLanguage !== 'chinese_simplified') {
                  //   console.log('📝 更新显示到:', currentLanguage);
                   //  updateLanguageDisplay(currentLanguage);
                }
            };

            // 直接监听语言变化（备用方案）
            const originalChangeLanguage = translate.changeLanguage;
            translate.changeLanguage = function(language) {
                // console.log('🔄 translate.changeLanguage 被调用:', language);
                // console.log('🔍 检查语言是否在映射中:', languageNames[language]);
                // console.log('🔍 所有可用语言:', Object.keys(languageNames));

                const result = originalChangeLanguage.call(this, language);

                // 延迟更新显示（确保翻译完成）
                setTimeout(function() {
                  //   console.log('⏰ 延迟更新语言显示:', language);
                  //   console.log('⏰ 当前translate语言状态:', translate.language.getCurrent());
                    updateLanguageDisplay(language);
                }, 1000);

                return result;
            };

            // 添加手动测试所有语言的函数
            window.testAllLanguages = function() {
                const languages = ['khmer', 'thai', 'vietnamese', 'malay', 'indonesian', 'chinese_simplified', 'english'];
                languages.forEach(function(lang, index) {
                    setTimeout(function() {
                        // console.log('🧪 测试语言:', lang);
                        updateLanguageDisplay(lang);
                    }, index * 2000);
                });
            };

            // 快速执行初始化
            translate.execute();

        } else {
            // translate.js未加载，500ms后重试（减少等待时间）
            setTimeout(initTranslate, 500);
        }
    }

    // 优化：不等待window.load，DOM准备好就初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTranslate);
    } else {
        // DOM已经准备好，立即初始化
        initTranslate();
    }

    // 捕获并忽略浏览器扩展相关的错误
    window.addEventListener('error', function(event) {
        const errorMessage = event.message || '';

        // 检查是否是浏览器扩展相关的错误
        if (errorMessage.includes('message channel closed') ||
            errorMessage.includes('Extension context invalidated') ||
            errorMessage.includes('listener indicated an asynchronous response') ||
            errorMessage.includes('chrome-extension://')) {

            // 静默处理扩展错误，不影响用户体验
            event.preventDefault();
            return false;
        }
    });

    // 捕获Promise rejection错误
    window.addEventListener('unhandledrejection', function(event) {
        const reason = event.reason || {};
        const message = reason.message || reason.toString();

        // 检查是否是浏览器扩展相关的Promise错误
        if (message.includes('message channel closed') ||
            message.includes('Extension context invalidated') ||
            message.includes('listener indicated an asynchronous response') ||
            message.includes('chrome-extension://')) {

            // 静默处理扩展Promise错误
            event.preventDefault();
            return false;
        }
    });
    </script>

    <!-- 在线客服系统 -->
    <div id="customer-service-widget" style="display: none;">
        <!-- 客服按钮 -->
        <div id="cs-chat-button" style="position:fixed;bottom:20px;right:20px;width:60px;height:60px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;color:white;font-size:28px;z-index:9999;box-shadow:0 4px 20px rgba(102, 126, 234, 0.4);transition:all 0.3s ease;border:none;">
            <span id="cs-button-icon">💬</span>
        </div>

        <!-- 聊天窗口 -->
        <div id="cs-chat-window" style="position:fixed;bottom:90px;right:20px;width:380px;height:520px;background:white;border-radius:16px;box-shadow:0 12px 40px rgba(0,0,0,0.15);display:none;z-index:9998;border:1px solid #e1e5e9;overflow:hidden;">
            <!-- 头部 -->
            <div id="cs-chat-header" style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;padding:20px;position:relative;">
                <div style="display:flex;justify-content:space-between;align-items:center;">
                    <div>
                        <h4 style="margin:0;font-size:18px;font-weight:600;">在线客服</h4>
                        <small style="opacity:0.9;font-size:13px;">我们随时为您服务 🌟</small>
                    </div>
                    <div style="display:flex;gap:8px;">
                        <button onclick="showMyMessages()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:12px;display:flex;align-items:center;justify-content:center;" title="查看我的留言">📬</button>
                        <button onclick="testOfflineForm()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:12px;display:flex;align-items:center;justify-content:center;" title="测试离线表单">📝</button>
                        <button onclick="toggleChatWindow()" style="background:rgba(255,255,255,0.2);border:none;color:white;width:32px;height:32px;border-radius:50%;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;">×</button>
                    </div>
                </div>
                <div id="cs-status" style="position:absolute;bottom:8px;right:20px;font-size:12px;opacity:0.8;">
                    <span style="display:inline-block;width:8px;height:8px;background:#4ade80;border-radius:50%;margin-right:5px;"></span>在线
                </div>
            </div>

            <!-- 消息区域 -->
            <div id="cs-messages" style="height:350px;overflow-y:auto;padding:20px;background:#f8fafc;">
                <!-- 欢迎消息 -->
                <div id="cs-welcome" style="text-align:center;padding:30px 20px;color:#64748b;">
                    <div style="font-size:48px;margin-bottom:15px;">👋</div>
                    <h5 style="margin:0 0 8px 0;color:#334155;font-weight:600;">欢迎咨询！</h5>
                    <p style="margin:0;font-size:14px;line-height:1.5;">我是您的专属客服助手，请输入您的问题，我会尽快为您解答。</p>
                </div>
            </div>

            <!-- 离线表单 -->
            <div id="cs-offline-form" style="display:none;height:350px;background:#ffffff;">
                <!-- 表单头部 -->
                <div style="background:#000000;color:#ffffff;padding:12px 20px;text-align:center;">
                    <h4 id="offline-form-title" style="margin:0;font-size:15px;font-weight:300;letter-spacing:1px;">客服离线留言</h4>
                    <p id="offline-form-message" style="margin:4px 0 0 0;font-size:11px;opacity:0.8;font-weight:300;">
                        请留下联系方式，我们会尽快回复您
                    </p>
                </div>

                <!-- 表单内容区域 -->
                <div style="height:calc(100% - 90px);overflow-y:auto;padding:15px 20px;background:#ffffff;">
                    <form id="offline-message-form">
                        <div id="offline-form-fields">
                            <!-- 动态生成表单字段 -->
                        </div>
                    </form>
                </div>

                <!-- 提交按钮 - 绝对定位到最底部 -->
                <div style="position:absolute;bottom:0;left:0;right:0;background:#000000;padding:12px 20px;">
                    <button type="button" id="offline-submit-btn" onclick="submitOfflineForm()" style="
                        width:100%;
                        padding:10px;
                        background:#ffffff;
                        color:#000000;
                        border:none;
                        font-size:13px;
                        font-weight:500;
                        cursor:pointer;
                        transition:all 0.3s ease;
                        letter-spacing:1px;
                    ">
                        提交留言
                    </button>
                </div>
            </div>

            <!-- 我的留言界面 -->
            <div id="cs-my-messages" style="display:none;height:350px;background:#ffffff;">
                <!-- 头部 -->
                <div style="background:#000000;color:#ffffff;padding:12px 20px;text-align:center;">
                    <h4 style="margin:0;font-size:15px;font-weight:300;letter-spacing:1px;">我的留言</h4>
                    <p style="margin:4px 0 0 0;font-size:11px;opacity:0.8;font-weight:300;">
                        查看留言和客服回复
                    </p>
                </div>

                <!-- 留言列表区域 -->
                <div style="height:calc(100% - 90px);overflow-y:auto;padding:15px 20px;background:#ffffff;">
                    <div id="my-messages-list">
                        <!-- 动态加载留言列表 -->
                        <div style="text-align:center;color:#666;margin-top:50px;">
                            <div style="font-size:48px;margin-bottom:10px;">📬</div>
                            <p>正在加载您的留言...</p>
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div style="position:absolute;bottom:0;left:0;right:0;background:#000000;padding:12px 20px;">
                    <button type="button" onclick="hideChatPanels(); showChatInterface()" style="
                        width:100%;
                        padding:10px;
                        background:#ffffff;
                        color:#000000;
                        border:none;
                        font-size:13px;
                        font-weight:500;
                        cursor:pointer;
                        transition:all 0.3s ease;
                        letter-spacing:1px;
                    ">
                        返回聊天
                    </button>
                </div>
            </div>

            <!-- 输入区域 -->
            <div id="cs-input-area" style="padding:20px;border-top:1px solid #e2e8f0;background:white;">
                <div style="display:flex;gap:12px;align-items:flex-end;">
                    <input type="text" id="cs-message-input" placeholder="输入您的问题..." style="flex:1;padding:12px 16px;border:1px solid #d1d5db;border-radius:24px;outline:none;font-size:14px;transition:border-color 0.2s;background:#f9fafb;" onfocus="this.style.borderColor='#667eea';this.style.background='white'" onblur="this.style.borderColor='#d1d5db';this.style.background='#f9fafb'" onkeypress="handleKeyPress(event)">
                    <button id="cs-send-btn" onclick="sendMessage()" style="padding:12px 20px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;border:none;border-radius:24px;cursor:pointer;font-size:14px;font-weight:500;transition:all 0.2s;box-shadow:0 2px 8px rgba(102, 126, 234, 0.3);" onmouseover="this.style.transform='translateY(-1px)';this.style.boxShadow='0 4px 12px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)';this.style.boxShadow='0 2px 8px rgba(102, 126, 234, 0.3)'">发送</button>
                </div>
                <div style="margin-top:8px;font-size:11px;color:#9ca3af;text-align:center;">按 Enter 快速发送</div>
            </div>
        </div>
    </div>

    <script>
    // 全局变量声明
    let csSettings = {};
    let isOffline = false;
    let sessionId = null;
    let isOpen = false;
    let heartbeatInterval = null;
    let eventSource = null;
    let lastMessageId = 0;
    let lastNotificationSound = 0;
    const NOTIFICATION_SOUND_COOLDOWN = 2000; // 2秒内最多播放一次

    // 消息去重机制
    let processedMessageIds = new Set();
    let messageDeduplicationTimeout = null;

    // 轮询控制
    let pollingInterval = null;
    let isPollingActive = false;

    // 优化：使用异步初始化，避免阻塞页面加载
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟初始化客服系统，让页面先完成基本渲染
        setTimeout(async function() {
            try {
                // 初始化会话ID
                sessionId = getOrCreatePersistentSessionId();

                // 异步加载设置
                await loadCustomerServiceSettings();

                // 检查系统是否启用
                if (!csSettings) {
            console.log('❌ 客服设置加载失败');
            return;
        }

        // 检查系统是否启用
        if (!csSettings.system_enabled) {
            console.log('⚠️ 客服系统在后台被禁用，隐藏客服组件');
            document.getElementById('customer-service-widget').style.display = 'none';
            return;
        }

        console.log('✅ 客服系统已启用，继续初始化');

        // 首先检查客服系统是否启用
        checkCustomerServiceStatus();

        // 请求桌面通知权限
        requestNotificationPermission();

        const chatButton = document.getElementById('cs-chat-button');
        const chatWindow = document.getElementById('cs-chat-window');
        const messagesDiv = document.getElementById('cs-messages');
        const messageInput = document.getElementById('cs-message-input');
        const sendBtn = document.getElementById('cs-send-btn');

        // 绑定离线表单事件
        bindOfflineFormEvents();

        // 请求桌面通知权限
        function requestNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        console.log('✅ 桌面通知权限已获取');
                    } else {
                        console.log('❌ 桌面通知权限被拒绝');
                    }
                });
            }
        }

        // 优化后的客服系统状态检查 - 使用懒加载
        function checkCustomerServiceStatus() {
            // 延迟检查，避免阻塞页面加载
            setTimeout(() => {
                fetch('/api/customer-service/status')
                    .then(response => response.json())
                    .then(data => {
                        console.log('🔍 客服系统状态检查结果:', data);
                        if (data.success && data.enabled) {
                            // 系统启用，显示客服按钮
                            console.log('✅ 客服系统启用，显示组件');
                            document.getElementById('customer-service-widget').style.display = 'block';
                        } else {
                            // 系统禁用，隐藏客服按钮
                            console.log('❌ 客服系统禁用，隐藏组件');
                            document.getElementById('customer-service-widget').style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('❌ 客服系统状态检查失败:', error);
                        // 出错时默认隐藏（更安全的做法）
                        document.getElementById('customer-service-widget').style.display = 'none';
                    });
            }, 100); // 延迟100ms，让页面先渲染
        }



        // 优化后的客服系统设置加载 - 懒加载
        async function loadCustomerServiceSettings() {
            try {
                // 确保csSettings已初始化
                if (typeof csSettings === 'undefined') {
                    window.csSettings = {};
                    csSettings = window.csSettings;
                }

                // 延迟加载设置，避免阻塞页面
                const response = await fetch('/api/customer-service/settings.php');

                if (!response.ok) {
                    throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                }

                const data = await response.json();

                if (data.success) {
                    csSettings = data.data;
                    window.csSettings = csSettings; // 确保全局可访问
                    console.log('✅ 客服设置加载成功:', csSettings);
                    console.log('🔍 system_enabled状态:', csSettings.system_enabled);

                    // 应用设置到界面
                    applySettingsToUI();
                } else {
                    console.error('❌ 加载客服设置失败:', data.error || data.message);
                    // 使用默认设置
                    csSettings = getDefaultSettings();
                    window.csSettings = csSettings;
                }
            } catch (error) {
                console.error('💥 加载客服设置异常:', error);
                csSettings = getDefaultSettings();
                window.csSettings = csSettings;
            }
        }

        // 获取默认设置
        function getDefaultSettings() {
            return {
                system_enabled: true,
                welcome_message: '您好！欢迎咨询，我们将竭诚为您服务！',
                offline_message: '客服暂时离线，请留下您的联系方式，我们会尽快回复您！',
                auto_open_enabled: true,
                sound_enabled: true,
                sound_type: 'default',
                sound_volume: 0.3,
                chat_position: 'bottom-right',
                chat_theme_color: '#667eea',
                chat_button_color: '#667eea',
                chat_font_size: 14,
                chat_window_width: 380,
                chat_window_height: 520,
                show_avatar: true,
                admin_name: '客服小助手',
                chat_border_radius: 16,
                offline_form_enabled: true,
                offline_form_title: '客服离线留言',
                offline_form_fields: ['name', 'email', 'phone', 'whatsapp', 'message'],
                offline_form_required: ['message']
            };
        }

        // 生成持久化会话ID
        function getOrCreatePersistentSessionId() {
            // 尝试从localStorage获取
            let sessionId = localStorage.getItem('customer_service_session_id');

            if (!sessionId) {
                // 生成设备指纹
                const deviceFingerprint = generateDeviceFingerprint();

                // 创建新的会话ID
                sessionId = 'session_' + deviceFingerprint + '_' + Date.now();

                // 保存到localStorage
                localStorage.setItem('customer_service_session_id', sessionId);
                localStorage.setItem('device_fingerprint', deviceFingerprint);
                localStorage.setItem('session_created_at', new Date().toISOString());
            }

            return sessionId;
        }

        // 生成设备指纹
        function generateDeviceFingerprint() {
            const fingerprint = {
                screen: screen.width + 'x' + screen.height,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                language: navigator.language,
                platform: navigator.platform,
                userAgent: navigator.userAgent.substring(0, 100)
            };

            // 生成简单哈希
            const fingerprintString = JSON.stringify(fingerprint);
            let hash = 0;
            for (let i = 0; i < fingerprintString.length; i++) {
                const char = fingerprintString.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }

            return 'fp_' + Math.abs(hash).toString(36);
        }

        // 应用设置到UI
        function applySettingsToUI() {
            console.log('🎨 开始应用UI设置:', csSettings);

            const chatWidget = document.getElementById('customer-service-widget');
            const chatWindow = document.getElementById('cs-chat-window');
            const chatButton = document.getElementById('cs-chat-button');

            if (!chatWidget) {
                console.warn('⚠️ 找不到客服组件');
                return;
            }

            // 应用按钮颜色
            if (csSettings.chat_button_color && chatButton) {
                console.log('🎨 应用按钮颜色:', csSettings.chat_button_color);
                chatButton.style.background = 'linear-gradient(135deg, ' + csSettings.chat_button_color + ' 0%, ' + csSettings.chat_button_color + ' 100%)';
            }

            // 应用主题颜色到聊天窗口头部
            if (csSettings.chat_theme_color && chatWindow) {
                console.log('🎨 应用主题颜色:', csSettings.chat_theme_color);
                const chatHeader = document.getElementById('cs-chat-header');
                if (chatHeader) {
                    chatHeader.style.background = 'linear-gradient(135deg, ' + csSettings.chat_theme_color + ' 0%, ' + csSettings.chat_theme_color + ' 100%)';
                    // 更新阴影颜色以匹配主题
                    if (chatButton) {
                        const shadowColor = csSettings.chat_theme_color + '40'; // 添加透明度
                        chatButton.style.boxShadow = '0 4px 20px ' + shadowColor;
                    }
                }

                // 应用到发送按钮
                const sendButton = document.getElementById('cs-send-btn');
                if (sendButton) {
                    sendButton.style.background = 'linear-gradient(135deg, ' + csSettings.chat_theme_color + ' 0%, ' + csSettings.chat_theme_color + ' 100%)';
                    // 同时更新hover效果的阴影颜色
                    const shadowColor = csSettings.chat_theme_color + '40'; // 添加透明度
                    sendButton.style.boxShadow = '0 2px 8px ' + shadowColor;
                }
            }

            // 应用窗口尺寸设置
            if (csSettings.chat_window_width && chatWindow) {
                chatWindow.style.width = csSettings.chat_window_width + 'px';
            }
            if (csSettings.chat_window_height && chatWindow) {
                chatWindow.style.height = csSettings.chat_window_height + 'px';
            }

            // 应用字体大小设置
            if (csSettings.chat_font_size && chatWindow) {
                const messageArea = chatWindow.querySelector('#cs-messages');
                if (messageArea) {
                    messageArea.style.fontSize = csSettings.chat_font_size + 'px';
                }
            }

            // 应用边框圆角设置
            if (csSettings.chat_border_radius && chatWindow) {
                chatWindow.style.borderRadius = csSettings.chat_border_radius + 'px';
            }

            // 应用位置设置
            if (csSettings.chat_position && chatButton && chatWindow) {
                const positions = csSettings.chat_position.split('-');
                if (positions.length === 2) {
                    const vertical = positions[0]; // top 或 bottom
                    const horizontal = positions[1]; // left 或 right

                    // 重置所有位置
                    chatButton.style.top = 'auto';
                    chatButton.style.bottom = 'auto';
                    chatButton.style.left = 'auto';
                    chatButton.style.right = 'auto';

                    chatWindow.style.top = 'auto';
                    chatWindow.style.bottom = 'auto';
                    chatWindow.style.left = 'auto';
                    chatWindow.style.right = 'auto';

                    // 应用新位置
                    if (vertical === 'bottom') {
                        chatButton.style.bottom = '20px';
                        chatWindow.style.bottom = '90px';
                    } else {
                        chatButton.style.top = '20px';
                        chatWindow.style.top = '90px';
                    }

                    if (horizontal === 'right') {
                        chatButton.style.right = '20px';
                        chatWindow.style.right = '20px';
                    } else {
                        chatButton.style.left = '20px';
                        chatWindow.style.left = '20px';
                    }
                }
            }

            // 应用窗口尺寸
            if (csSettings.chat_window_width && chatWindow) {
                console.log('🎨 应用窗口宽度:', csSettings.chat_window_width);
                chatWindow.style.width = csSettings.chat_window_width + 'px';
            }
            if (csSettings.chat_window_height && chatWindow) {
                console.log('🎨 应用窗口高度:', csSettings.chat_window_height);
                chatWindow.style.height = csSettings.chat_window_height + 'px';
            }

            // 应用字体大小
            if (csSettings.chat_font_size) {
                console.log('🎨 应用字体大小:', csSettings.chat_font_size);
                const messagesContainer = document.getElementById('cs-messages');
                if (messagesContainer) {
                    messagesContainer.style.fontSize = csSettings.chat_font_size + 'px';
                }
            }

            // 应用圆角
            if (csSettings.chat_border_radius && chatWindow) {
                console.log('🎨 应用圆角:', csSettings.chat_border_radius);
                chatWindow.style.borderRadius = csSettings.chat_border_radius + 'px';
            }

            // 应用位置
            if (csSettings.chat_position) {
                const positions = {
                    'bottom-right': { bottom: '20px', right: '20px', left: 'auto', top: 'auto' },
                    'bottom-left': { bottom: '20px', left: '20px', right: 'auto', top: 'auto' },
                    'top-right': { top: '20px', right: '20px', left: 'auto', bottom: 'auto' },
                    'top-left': { top: '20px', left: '20px', right: 'auto', bottom: 'auto' }
                };

                const pos = positions[csSettings.chat_position];
                if (pos) {
                    Object.assign(chatWidget.style, pos);
                }
            }

            // 应用离线表单标题
            if (csSettings.offline_form_title) {
                const offlineTitle = document.getElementById('offline-form-title');
                if (offlineTitle) {
                    offlineTitle.textContent = csSettings.offline_form_title;
                }
            }

            // 应用客服名称
            if (csSettings.admin_name) {
                console.log('🎨 应用客服名称:', csSettings.admin_name);
                const chatTitle = document.querySelector('#cs-chat-header h4');
                if (chatTitle) {
                    chatTitle.textContent = csSettings.admin_name;
                }
            }

            // 应用欢迎消息
            if (csSettings.welcome_message) {
                console.log('🎨 应用欢迎消息:', csSettings.welcome_message);
                const welcomeText = document.querySelector('#cs-welcome p');
                if (welcomeText) {
                    welcomeText.textContent = csSettings.welcome_message;
                }
            }

            // 应用离线消息
            if (csSettings.offline_message) {
                console.log('🎨 应用离线消息:', csSettings.offline_message);
                const offlineMessage = document.getElementById('offline-form-message');
                if (offlineMessage) {
                    offlineMessage.textContent = csSettings.offline_message;
                }
            }

            // 立即检查并应用离线表单模式
            setTimeout(() => {
                initializeCustomerServiceMode();
            }, 500); // 延迟500ms确保所有设置都已加载

            console.log('🎨 UI设置应用完成');
        }



        // 初始化客服系统模式
        function initializeCustomerServiceMode() {
            console.log('🚀 初始化客服系统模式');

            // 等待设置加载完成
            if (!csSettings || Object.keys(csSettings).length === 0) {
                console.log('⏳ 等待设置加载...');
                setTimeout(initializeCustomerServiceMode, 100);
                return;
            }

            // 检查是否启用离线表单
            if (!csSettings.offline_form_enabled) {
                console.log('🔍 离线表单未启用，显示聊天界面');
                showChatInterface();
                return;
            }

            console.log('🔍 离线表单已启用，检查当前状态');

            // 检查是否应该显示离线表单
            const shouldShowOfflineForm = checkIfShouldShowOfflineForm();

            if (shouldShowOfflineForm) {
                console.log('✅ 当前是离线状态，直接显示离线表单');
                showOfflineForm();
            } else {
                console.log('✅ 当前是在线状态，显示聊天界面');
                showChatInterface();
            }
        }

        // 检查并显示离线表单（保留兼容性）
        function checkAndShowOfflineForm() {
            initializeCustomerServiceMode();
        }

        // 显示聊天界面
        function showChatInterface() {
            const chatMessages = document.getElementById('cs-messages');
            const chatInput = document.getElementById('cs-input-area');
            const offlineForm = document.getElementById('cs-offline-form');

            if (chatMessages) {
                chatMessages.style.display = 'block';
                console.log('✅ 显示聊天消息区域');
            } else {
                console.error('❌ 找不到聊天消息区域 #cs-messages');
            }

            if (chatInput) {
                chatInput.style.display = 'block';
                console.log('✅ 显示聊天输入区域');
            } else {
                console.error('❌ 找不到聊天输入区域 #cs-input-area');
            }

            if (offlineForm) {
                offlineForm.style.display = 'none';
                console.log('✅ 隐藏离线表单');
            }

            console.log('💬 已切换到聊天界面');
        }

        // 判断是否应该显示离线表单
        function checkIfShouldShowOfflineForm() {
            // 使用后台设置的工作时间进行判断

            console.log('🔍 检查离线表单显示条件');
            console.log('📋 当前设置:', {
                offline_form_enabled: csSettings.offline_form_enabled,
                working_hours_enabled: csSettings.working_hours_enabled,
                working_start_time: csSettings.working_start_time,
                working_end_time: csSettings.working_end_time,
                working_days: csSettings.working_days
            });

            // 1. 检查是否启用了工作时间控制
            if (!csSettings.working_hours_enabled) {
                console.log('🔍 工作时间控制未启用，根据其他条件判断');
                return false; // 如果没有启用工作时间控制，默认不显示离线表单
            }

            const now = new Date();

            // 2. 检查是否是工作日
            const dayOfWeek = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
            const dayNumber = dayOfWeek === 0 ? 7 : dayOfWeek; // 转换为1-7格式

            const workingDays = csSettings.working_days || [1, 2, 3, 4, 5]; // 默认周一到周五
            const isWorkingDay = workingDays.includes(dayNumber);

            if (!isWorkingDay) {
                console.log('🗓️ 今天不是工作日，显示离线表单');
                return true;
            }

            // 3. 检查当前时间是否在工作时间内
            const currentTime = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');
            const workingStartTime = csSettings.working_start_time || '09:00';
            const workingEndTime = csSettings.working_end_time || '18:00';

            const isInWorkingHours = currentTime >= workingStartTime && currentTime <= workingEndTime;

            if (!isInWorkingHours) {
                console.log(`🕐 当前时间 ${currentTime} 不在工作时间 ${workingStartTime}-${workingEndTime} 内，显示离线表单`);
                return true;
            }

            // 4. 检查是否在午休时间
            if (csSettings.lunch_break_enabled) {
                const lunchStartTime = csSettings.lunch_start_time || '12:00';
                const lunchEndTime = csSettings.lunch_end_time || '13:30';

                const isInLunchBreak = currentTime >= lunchStartTime && currentTime <= lunchEndTime;

                if (isInLunchBreak) {
                    console.log(`🍽️ 当前时间 ${currentTime} 在午休时间 ${lunchStartTime}-${lunchEndTime} 内，显示离线表单`);
                    return true;
                }
            }

            // 5. 检查节假日
            if (csSettings.holiday_mode_enabled && csSettings.holiday_dates) {
                const today = now.getFullYear() + '-' +
                             (now.getMonth() + 1).toString().padStart(2, '0') + '-' +
                             now.getDate().toString().padStart(2, '0');

                if (csSettings.holiday_dates.includes(today)) {
                    console.log(`🎉 今天是节假日 ${today}，显示离线表单`);
                    return true;
                }
            }

            console.log('✅ 当前在工作时间内，不显示离线表单');
            return false;
        }

        // 测试离线表单功能
        window.testOfflineForm = function() {
            if (!csSettings.offline_form_enabled) {
                alert('离线表单功能未启用，请在设置中启用后再试。');
                return;
            }

            console.log('🧪 手动测试离线表单');
            showOfflineForm();
        };

        // 切换到聊天模式
        window.switchToChatMode = function() {
            console.log('💬 手动切换到聊天模式');
            showChatInterface();
        };

        // 键盘事件处理
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                sendMessage();
            }
        }

        // 显示离线表单
        function showOfflineForm() {
            const chatMessages = document.getElementById('cs-messages');
            const chatInput = document.getElementById('cs-input-area');
            const offlineForm = document.getElementById('cs-offline-form');

            // 隐藏聊天界面
            if (chatMessages) {
                chatMessages.style.display = 'none';
                console.log('✅ 隐藏聊天消息区域');
            } else {
                console.error('❌ 找不到聊天消息区域 #cs-messages');
            }

            if (chatInput) {
                chatInput.style.display = 'none';
                console.log('✅ 隐藏聊天输入区域');
            } else {
                console.error('❌ 找不到聊天输入区域 #cs-input-area');
            }

            // 显示离线表单
            if (offlineForm) {
                offlineForm.style.display = 'block';
                console.log('✅ 显示离线表单');
            } else {
                console.error('❌ 找不到离线表单元素 #cs-offline-form');
                return;
            }

            // 设置表单标题和消息
            const titleEl = document.getElementById('offline-form-title');
            const messageEl = document.getElementById('offline-form-message');

            if (titleEl && csSettings.offline_form_title) {
                titleEl.textContent = csSettings.offline_form_title;
            } else if (titleEl) {
                titleEl.textContent = '客服离线留言';
            }

            if (messageEl && csSettings.offline_message) {
                messageEl.textContent = csSettings.offline_message;
            } else if (messageEl) {
                messageEl.textContent = '客服暂时离线，请留下您的联系方式，我们会尽快回复您！';
            }

            // 生成表单字段
            generateOfflineFormFields();

            // 确保表单事件已绑定
            bindOfflineFormEvents();

            // 绑定按钮悬停效果
            const submitBtn = document.getElementById('offline-submit-btn');
            if (submitBtn) {
                submitBtn.addEventListener('mouseenter', function() {
                    this.style.background = '#f5f5f5';
                    this.style.transform = 'translateY(-2px)';
                });
                submitBtn.addEventListener('mouseleave', function() {
                    this.style.background = '#ffffff';
                    this.style.transform = 'translateY(0)';
                });
            }

            console.log('📝 已切换到离线表单界面');
        }

        // 隐藏离线表单
        function hideOfflineForm() {
            const messagesDiv = document.getElementById('cs-messages');
            const offlineForm = document.getElementById('cs-offline-form');
            const inputArea = document.getElementById('cs-input-area');

            if (messagesDiv) messagesDiv.style.display = 'block';
            if (offlineForm) offlineForm.style.display = 'none';
            if (inputArea) inputArea.style.display = 'block';
        }

        // 生成离线表单字段
        function generateOfflineFormFields() {
            const fieldsContainer = document.getElementById('offline-form-fields');
            if (!fieldsContainer) {
                console.error('❌ 找不到表单字段容器');
                return;
            }

            const fields = csSettings.offline_form_fields || ['name', 'email', 'phone', 'whatsapp', 'message'];
            const required = csSettings.offline_form_required || [];

            console.log('📋 表单字段配置:', {
                fields: fields,
                required: required,
                settings: csSettings
            });

            const fieldLabels = {
                name: '姓名',
                email: '邮箱',
                phone: '电话',
                whatsapp: 'WhatsApp',
                message: '留言内容'
            };

            let html = '';

            // 分离短字段和长字段
            const shortFields = fields.filter(f => f !== 'message');
            const hasMessage = fields.includes('message');

            // 短字段使用两列布局
            if (shortFields.length > 0) {
                html += '<div style="display:flex;flex-wrap:wrap;gap:8px;margin-bottom:8px;">';

                shortFields.forEach((field, index) => {
                    const isRequired = required.includes(field);
                    const label = fieldLabels[field] || field;
                    const fieldId = 'offline-field-' + field;
                    const inputType = field === 'email' ? 'email' : field === 'phone' ? 'tel' : 'text';

                    // 计算宽度：如果是奇数个字段，最后一个占满宽度
                    const isLastOdd = (index === shortFields.length - 1) && (shortFields.length % 2 === 1);
                    const width = isLastOdd ? '100%' : 'calc(50% - 4px)';

                    html += '<div style="width:' + width + ';">';
                    html += '<label for="' + fieldId + '" style="display:block;margin-bottom:8px;color:#000000;font-size:13px;font-weight:400;letter-spacing:1px;">';
                    html += label;
                    if (isRequired) {
                        html += ' <span style="color:#000000;">*</span>';
                    }
                    html += '</label>';

                    html += '<input';
                    html += ' type="' + inputType + '"';
                    html += ' id="' + fieldId + '"';
                    html += ' name="' + field + '"';
                    html += ' placeholder="请输入' + label + '"';
                    if (isRequired) html += ' required';
                    html += ' style="';
                    html += 'width:100%;';
                    html += 'padding:12px 0;';
                    html += 'border:none;';
                    html += 'border-bottom:1px solid #e0e0e0;';
                    html += 'font-size:14px;';
                    html += 'box-sizing:border-box;';
                    html += 'background:transparent;';
                    html += 'color:#000000;';
                    html += 'outline:none;';
                    html += '"';
                    html += ' onfocus="this.style.borderBottomColor=\'#000000\'"';
                    html += ' onblur="this.style.borderBottomColor=\'#e0e0e0\'"';
                    html += '>';

                    html += '</div>';
                });

                html += '</div>';
            }

            // 留言内容字段单独一行
            if (hasMessage) {
                const isRequired = required.includes('message');
                const label = fieldLabels['message'];
                const fieldId = 'offline-field-message';

                html += '<div style="margin-bottom:8px;">';
                html += '<label for="' + fieldId + '" style="display:block;margin-bottom:8px;color:#000000;font-size:13px;font-weight:400;letter-spacing:1px;">';
                html += label;
                if (isRequired) {
                    html += ' <span style="color:#000000;">*</span>';
                }
                html += '</label>';

                html += '<textarea';
                html += ' id="' + fieldId + '"';
                html += ' name="message"';
                html += ' placeholder="请详细描述您的问题..."';
                if (isRequired) html += ' required';
                html += ' style="';
                html += 'width:100%;';
                html += 'padding:12px 0;';
                html += 'border:none;';
                html += 'border-bottom:1px solid #e0e0e0;';
                html += 'font-size:14px;';
                html += 'height:80px;';
                html += 'resize:none;';
                html += 'box-sizing:border-box;';
                html += 'background:transparent;';
                html += 'color:#000000;';
                html += 'font-family:inherit;';
                html += 'outline:none;';
                html += '"';
                html += ' onfocus="this.style.borderBottomColor=\'#000000\'"';
                html += ' onblur="this.style.borderBottomColor=\'#e0e0e0\'"';
                html += '></textarea>';

                html += '</div>';
            }

            fieldsContainer.innerHTML = html;
            console.log('✅ 离线表单字段生成完成，使用两列布局');
        }

        // 防重复提交标志
        let isSubmitting = false;

        // 提交离线表单
        window.submitOfflineForm = function() {
            console.log('🔄 开始提交离线表单');

            // 防重复提交检查
            if (isSubmitting) {
                console.log('⚠️ 正在提交中，请勿重复点击');
                return;
            }

            const form = document.getElementById('offline-message-form');
            if (!form) {
                console.error('❌ 找不到离线表单');
                alert('表单错误，请刷新页面重试');
                return;
            }

            // 设置提交状态
            isSubmitting = true;

            // 禁用提交按钮
            const submitBtn = document.getElementById('offline-submit-btn');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = '提交中...';
                submitBtn.style.opacity = '0.6';
                submitBtn.style.cursor = 'not-allowed';
            }

            // 手动触发表单提交事件
            const submitEvent = new Event('submit', {
                bubbles: true,
                cancelable: true
            });

            form.dispatchEvent(submitEvent);
        };

        // 显示我的留言
        window.showMyMessages = function() {
            console.log('📬 显示我的留言');

            const chatMessages = document.getElementById('cs-messages');
            const chatInput = document.getElementById('cs-input-area');
            const offlineForm = document.getElementById('cs-offline-form');
            const myMessages = document.getElementById('cs-my-messages');

            // 隐藏其他界面
            if (chatMessages) chatMessages.style.display = 'none';
            if (chatInput) chatInput.style.display = 'none';
            if (offlineForm) offlineForm.style.display = 'none';

            // 显示我的留言界面
            if (myMessages) {
                myMessages.style.display = 'block';
                console.log('✅ 显示我的留言界面');

                // 加载留言列表
                loadMyMessages();
            } else {
                console.error('❌ 找不到我的留言界面元素');
            }
        };

        // 加载我的留言
        function loadMyMessages() {
            console.log('📡 加载我的留言数据');

            const messagesList = document.getElementById('my-messages-list');
            if (!messagesList) return;

            // 显示加载状态
            messagesList.innerHTML = `
                <div style="text-align:center;color:#666;margin-top:50px;">
                    <div style="font-size:48px;margin-bottom:10px;">⏳</div>
                    <p>正在加载您的留言...</p>
                </div>
            `;

            // 获取当前用户的标识（可以是sessionId或其他标识）
            const userIdentifier = sessionId || localStorage.getItem('cs_user_identifier');

            // 发送请求获取留言
            fetch('/api/customer-service/my-messages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: userIdentifier
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.messages) {
                    displayMyMessages(data.messages);
                } else {
                    showNoMessages();
                }
            })
            .catch(error => {
                console.error('加载留言失败:', error);
                showLoadError();
            });
        }

        // 显示留言列表
        function displayMyMessages(messages) {
            const messagesList = document.getElementById('my-messages-list');
            if (!messagesList) return;

            if (messages.length === 0) {
                showNoMessages();
                return;
            }

            let html = '';
            messages.forEach(message => {
                const createdAt = new Date(message.created_at).toLocaleString('zh-CN');
                const statusText = message.status === 'replied' ? '已回复' : '待回复';
                const statusColor = message.status === 'replied' ? '#28a745' : '#ffc107';

                html += `
                    <div style="border-bottom:1px solid #eee;padding:12px 0;">
                        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px;">
                            <span style="font-size:12px;color:#666;">${createdAt}</span>
                            <span style="background:${statusColor};color:white;padding:2px 8px;border-radius:10px;font-size:11px;">${statusText}</span>
                        </div>
                        <div style="margin-bottom:8px;">
                            <strong style="font-size:13px;">我的留言：</strong>
                            <p style="margin:4px 0;font-size:13px;color:#333;">${message.message}</p>
                        </div>
                `;

                if (message.admin_reply) {
                    html += `
                        <div style="background:#f8f9fa;padding:8px;border-radius:4px;border-left:3px solid #28a745;">
                            <strong style="font-size:12px;color:#28a745;">客服回复：</strong>
                            <p style="margin:4px 0;font-size:12px;color:#333;">${message.admin_reply}</p>
                            <small style="color:#666;font-size:11px;">回复时间：${new Date(message.replied_at).toLocaleString('zh-CN')}</small>
                        </div>
                    `;
                }

                html += '</div>';
            });

            messagesList.innerHTML = html;
        }

        // 显示无留言状态
        function showNoMessages() {
            const messagesList = document.getElementById('my-messages-list');
            if (!messagesList) return;

            messagesList.innerHTML = `
                <div style="text-align:center;color:#666;margin-top:50px;">
                    <div style="font-size:48px;margin-bottom:10px;">📭</div>
                    <p>您还没有留言</p>
                    <button onclick="testOfflineForm()" style="background:#000;color:#fff;border:none;padding:8px 16px;border-radius:4px;margin-top:10px;cursor:pointer;">
                        立即留言
                    </button>
                </div>
            `;
        }

        // 显示加载错误
        function showLoadError() {
            const messagesList = document.getElementById('my-messages-list');
            if (!messagesList) return;

            messagesList.innerHTML = `
                <div style="text-align:center;color:#666;margin-top:50px;">
                    <div style="font-size:48px;margin-bottom:10px;">❌</div>
                    <p>加载失败，请重试</p>
                    <button onclick="loadMyMessages()" style="background:#000;color:#fff;border:none;padding:8px 16px;border-radius:4px;margin-top:10px;cursor:pointer;">
                        重新加载
                    </button>
                </div>
            `;
        }

        // 显示聊天界面
        window.showChatInterface = function() {
            console.log('💬 显示聊天界面');

            const chatMessages = document.getElementById('cs-messages');
            const chatInput = document.getElementById('cs-input-area');

            if (chatMessages) chatMessages.style.display = 'block';
            if (chatInput) chatInput.style.display = 'block';
        };

        // 隐藏所有聊天面板
        function hideChatPanels() {
            const offlineForm = document.getElementById('cs-offline-form');
            const myMessages = document.getElementById('cs-my-messages');

            if (offlineForm) offlineForm.style.display = 'none';
            if (myMessages) myMessages.style.display = 'none';
        }

        // 绑定离线表单事件
        function bindOfflineFormEvents() {
            const form = document.getElementById('offline-message-form');
            if (!form) return;

            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                console.log('📝 离线表单提交事件触发');

                const formData = new FormData(form);
                const data = {
                    session_id: sessionId,
                    visitor_name: formData.get('name') || '',
                    visitor_email: formData.get('email') || '',
                    visitor_phone: formData.get('phone') || '',
                    visitor_whatsapp: formData.get('whatsapp') || '',
                    message: formData.get('message') || ''
                };

                console.log('📤 提交离线留言数据:', data);

                // 验证必填字段
                const required = csSettings.offline_form_required || ['message'];
                console.log('✅ 验证必填字段:', required);

                for (const field of required) {
                    const fieldKey = field === 'name' ? 'visitor_name' :
                                   field === 'email' ? 'visitor_email' :
                                   field === 'phone' ? 'visitor_phone' :
                                   field === 'whatsapp' ? 'visitor_whatsapp' : field;

                    if (!data[fieldKey]) {
                        const fieldName = field === 'name' ? '姓名' :
                                        field === 'email' ? '邮箱' :
                                        field === 'phone' ? '电话' :
                                        field === 'whatsapp' ? 'WhatsApp' : '留言内容';
                        alert('请填写' + fieldName);
                        return;
                    }
                }

                const submitBtn = document.getElementById('offline-submit-btn');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = '提交中...';
                submitBtn.disabled = true;

                try {
                    const response = await fetch('/api/customer-service/submit-offline-message.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 显示成功消息
                        alert(result.auto_reply || '留言提交成功，我们会尽快回复您！');

                        // 清空表单
                        form.reset();

                        // 隐藏离线表单
                        hideOfflineForm();

                        // 在消息区域显示提交成功的消息
                        addMessage('system', result.auto_reply || '留言提交成功，我们会尽快回复您！');
                    } else {
                        alert(result.message || '提交失败，请稍后重试');
                    }
                } catch (error) {
                    console.error('提交离线留言失败:', error);
                    alert('提交失败，请检查网络连接后重试');
                } finally {
                    // 重置按钮状态
                    if (submitBtn) {
                        submitBtn.textContent = '提交留言';
                        submitBtn.disabled = false;
                        submitBtn.style.opacity = '1';
                        submitBtn.style.cursor = 'pointer';
                    }

                    // 重置提交状态
                    isSubmitting = false;
                    console.log('🔄 提交状态已重置');
                }
            });
        }

        // 切换聊天窗口
        function toggleChatWindow() {
            isOpen = !isOpen;
            chatWindow.style.display = isOpen ? 'block' : 'none';

            // 更新按钮样式
            const buttonIcon = document.getElementById('cs-button-icon');
            if (isOpen) {
                buttonIcon.textContent = '×';
                chatButton.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';

                console.log('💬 客服窗口已打开');

                // 重新检查并设置显示模式
                initializeCustomerServiceMode();

                // 加载历史消息
                loadHistoryMessages();

                // 首次打开显示欢迎消息（如果没有历史消息）
                const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
                if (messages.length === 0) {
                    setTimeout(() => {
                        addMessage('ai', 'Hello! 👋 Welcome to our store! How can I help you today?');
                    }, 500);
                }

                // 启动实时消息接收（只启动一次）
                if (!window.customerServicePolling) {
                    startRealtimeMessageReceiver();
                }

                // 启动心跳
                startHeartbeat();

                // 初始化会话
                initSession();
            } else {
                buttonIcon.textContent = '💬';
                chatButton.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

                // 停止心跳并发送离线状态
                stopHeartbeat();
                sendOfflineStatus();
            }
        }

        chatButton.addEventListener('click', toggleChatWindow);
        window.toggleChatWindow = toggleChatWindow; // 全局函数供HTML调用

        // 发送消息
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            addMessage('customer', message);
            messageInput.value = '';

            // 发送到服务器
            console.log('发送消息:', message);
            fetch('/customer-service/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    session_id: sessionId,
                    message: message
                })
            })
            .then(response => {
                console.log('响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('服务器响应:', data);
                if (data.success && data.ai_reply) {
                    setTimeout(() => {
                        addMessage('ai', data.ai_reply);
                    }, 1000);
                } else if (data.error) {
                    console.error('服务器错误:', data.error);
                    setTimeout(() => {
                        addMessage('ai', '抱歉，服务器出现错误：' + data.error);
                    }, 1000);
                }
            })
            .catch(error => {
                console.error('请求错误:', error);
                setTimeout(() => {
                    addMessage('ai', '抱歉，连接服务器失败，请稍后重试。');
                }, 1000);
            });
        }

        // 保存消息到本地存储
        function saveMessageToLocal(sender, message, messageType = 'text') {
            const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');
            messages.push({
                sender: sender,
                message: message,
                message_type: messageType,
                timestamp: new Date().toISOString()
            });

            // 只保留最近50条消息
            if (messages.length > 50) {
                messages.splice(0, messages.length - 50);
            }

            localStorage.setItem('chat_messages_' + sessionId, JSON.stringify(messages));
        }

        // 加载历史消息
        function loadHistoryMessages() {
            const messages = JSON.parse(localStorage.getItem('chat_messages_' + sessionId) || '[]');

            messages.forEach(msg => {
                // 确保消息数据完整
                if (msg && msg.sender && msg.message) {
                    addMessageToUI(msg.sender, msg.message, msg.timestamp);
                }
            });

            if (messages.length > 0) {
                console.log('📚 加载了 ' + messages.length + ' 条历史消息');
            }
        }

        // 添加消息到界面
        function addMessage(sender, message, messageType = 'text') {
            addMessageToUI(sender, message, null, messageType);
            saveMessageToLocal(sender, message, messageType);
        }

        // 格式化消息内容
        function formatMessageContent(message, messageType = 'text') {
            console.log('格式化消息:', message, '类型:', messageType);

            // 检查是否是大括号包围的图片URL格式 {url}
            const bracketImageMatch = message.match(/^\{(.+)\}$/);
            if (bracketImageMatch) {
                const imageUrl = bracketImageMatch[1];
                console.log('识别为大括号图片URL:', imageUrl);
                return `<img src="${imageUrl}" alt="图片" style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" onclick="window.open('${imageUrl}', '_blank')" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"><div style="display:none; padding: 8px; background: #f3f4f6; border-radius: 8px; color: #666; font-size: 12px;">图片加载失败</div>`;
            }

            // 检查是否是图片消息类型或包含图片标识
            if (messageType === 'image' || message.includes('[图片]')) {
                let imageUrl = '';

                if (message.includes('http')) {
                    // 从消息中提取完整URL
                    const urlMatch = message.match(/(https?:\/\/[^\s]+)/);
                    if (urlMatch) {
                        imageUrl = urlMatch[1];
                    }
                } else if (message.startsWith('/storage/') || message.startsWith('/uploads/')) {
                    // 相对路径
                    imageUrl = message;
                } else if (message.includes('[图片]')) {
                    // 处理 [图片] 文件名 格式
                    const fileNameMatch = message.match(/\[图片\]\s*(.+)/);
                    if (fileNameMatch) {
                        const fileName = fileNameMatch[1].trim();
                        // 构建图片URL - 使用新的uploads路径
                        const today = new Date();
                        const year = today.getFullYear();
                        const month = String(today.getMonth() + 1).padStart(2, '0');
                        const day = String(today.getDate()).padStart(2, '0');
                        imageUrl = `/uploads/customer-service/images/${year}/${month}/${day}/${fileName}`;
                    }
                }

                if (imageUrl) {
                    console.log('生成图片标签:', imageUrl);
                    return `<img src="${imageUrl}" alt="图片" style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" onclick="window.open('${imageUrl}', '_blank')" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"><div style="display:none; padding: 8px; background: #f3f4f6; border-radius: 8px; color: #666; font-size: 12px;">图片加载失败</div>`;
                }
            }

            // 普通文本消息，转义HTML并处理换行
            return escapeHtml(message).replace(/\n/g, '<br>');
        }

        // 判断是否是图片URL
        function isImageUrl(url) {
            if (!url || typeof url !== 'string') return false;

            // 检查是否是HTTP/HTTPS URL或相对路径且包含图片扩展名
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
            const lowerUrl = url.toLowerCase();

            // 检查是否包含图片扩展名
            const hasImageExt = imageExtensions.some(ext => lowerUrl.includes(ext));

            // 检查是否是URL格式
            const isUrl = url.startsWith('http://') || url.startsWith('https://') ||
                         url.startsWith('/uploads/') || url.startsWith('/storage/');

            return isUrl && hasImageExt;
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 添加消息到UI（内部函数）
        function addMessageToUI(sender, message, timestamp, messageType = 'text') {
            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '15px';
            messageDiv.style.display = 'flex';
            messageDiv.style.alignItems = 'flex-start';
            messageDiv.style.animation = 'fadeInUp 0.3s ease-out';

            const time = timestamp ?
                new Date(timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) :
                new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

            const formattedMessage = formatMessageContent(message, messageType);

            if (sender === 'customer') {
                messageDiv.style.justifyContent = 'flex-end';
                messageDiv.innerHTML = `
                    <div style="max-width:75%;">
                        <div style="background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;padding:12px 16px;border-radius:18px 18px 4px 18px;word-wrap:break-word;box-shadow:0 2px 8px rgba(102, 126, 234, 0.3);">
                            ${formattedMessage}
                        </div>
                        <div style="text-align:right;font-size:11px;color:#9ca3af;margin-top:4px;">${time}</div>
                    </div>
                `;
            } else {
                messageDiv.style.justifyContent = 'flex-start';

                let avatarBg, avatarIcon, senderLabel;
                if (sender === 'admin') {
                    avatarBg = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
                    avatarIcon = '👨‍💼';
                    senderLabel = csSettings.admin_name || '客服';
                } else {
                    avatarBg = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                    avatarIcon = '🤖';
                    senderLabel = 'AI助手';
                }

                // 检查是否显示头像
                const showAvatar = csSettings.show_avatar !== false;

                // 检查是否有自定义头像
                let avatarContent = '';
                if (showAvatar) {
                    if (sender === 'admin' && csSettings.admin_avatar) {
                        // 使用自定义头像图片
                        avatarContent = `<img src="${csSettings.admin_avatar}" style="width:100%;height:100%;border-radius:50%;object-fit:cover;" alt="客服头像">`;
                    } else {
                        // 使用默认图标
                        avatarContent = `<span style="font-size:16px;">${avatarIcon}</span>`;
                    }
                }

                const avatarHtml = showAvatar ? `
                    <div style="width:32px;height:32px;background:${avatarBg};border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:12px;flex-shrink:0;">
                        ${avatarContent}
                    </div>
                ` : '';

                messageDiv.innerHTML = `
                    ${avatarHtml}
                    <div style="max-width:${showAvatar ? '75%' : '85%'};">
                        <div style="background:white;color:#374151;padding:12px 16px;border-radius:18px 18px 18px 4px;word-wrap:break-word;box-shadow:0 2px 8px rgba(0,0,0,0.1);border:1px solid #e5e7eb;">
                            ${formattedMessage}
                        </div>
                        <div style="font-size:11px;color:#9ca3af;margin-top:4px;">${senderLabel} · ${time}</div>
                    </div>
                `;
            }

            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 绑定事件
        sendBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        });

        // 启动实时消息接收 - 简化版本，彻底避免重复
        function startRealtimeMessageReceiver() {
            console.log('🔄 启动简化轮询消息接收，会话ID:', sessionId);

            if (!sessionId) {
                console.error('❌ sessionId为空，无法启动轮询');
                return;
            }

            // 防止重复启动
            if (window.customerServicePolling) {
                console.log('⚠️ 轮询已在运行，跳过重复启动');
                return;
            }

            window.customerServicePolling = true;
            let lastMessageId = 0;

            // 简化的轮询逻辑 - 只处理一次消息
            const pollInterval = setInterval(() => {
                const apiUrl = '/api/customer-service/messages/' + sessionId + '?last_id=' + lastMessageId;

                fetch(apiUrl)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.messages && data.messages.length > 0) {
                            // console.log('📨 收到 ' + data.messages.length + ' 条新消息'); // 已禁用以减少控制台输出

                            // 只处理管理员消息，并且只处理一次
                            data.messages.forEach(message => {
                                if (message.sender_type === 'admin' && message.id > lastMessageId) {
                                    console.log('💬 处理管理员消息:', message.message);

                                    // 检查是否需要自动打开窗口
                                    const needAutoOpen = (chatWindow.style.display === 'none' || !isOpen);

                                    if (needAutoOpen) {
                                        autoOpenChatForNewMessage();
                                        setTimeout(() => {
                                            addMessage('admin', message.message, message.message_type || 'text');
                                        }, 200);
                                    } else {
                                        addMessage('admin', message.message, message.message_type || 'text');
                                    }

                                    // 播放提示音
                                    playNotificationSound();

                                    // 更新最后消息ID
                                    lastMessageId = message.id;
                                }
                            });
                        }
                    })
                    .catch(error => {
                        console.error('轮询失败:', error);
                    });
            }, 3000); // 改为3秒检查一次，减少频率

            // console.log('✅ 简化轮询已启动'); // 已禁用以减少控制台输出

            // 存储间隔ID以便清理
            window.customerServicePollingInterval = pollInterval;
        }

        // 播放提示音（带节流控制）
        function playNotificationSound() {
            // 检查是否启用提示音
            if (!csSettings.sound_enabled) {
                return;
            }

            // 检查是否在冷却期内
            const currentTime = Date.now();
            if (currentTime - lastNotificationSound < NOTIFICATION_SOUND_COOLDOWN) {
                console.log('🔇 提示音冷却中，跳过播放');
                return;
            }

            lastNotificationSound = currentTime;
            console.log('🔊 播放提示音');

            try {
                // 如果是自定义音效且有文件
                if (csSettings.sound_type === 'custom' && csSettings.sound_file) {
                    const audio = new Audio(csSettings.sound_file);
                    audio.volume = csSettings.sound_volume || 0.3;
                    audio.play().catch(e => {
                        console.log('自定义音效播放失败，使用默认音效:', e);
                        playDefaultSound();
                    });
                } else {
                    // 使用默认音效
                    playDefaultSound();
                }
            } catch (e) {
                console.log('无法播放提示音:', e);
            }
        }

        // 播放默认音效
        function playDefaultSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                const volume = (csSettings.sound_volume || 0.3) * 0.2; // 降低音量
                gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(volume * 0.1, audioContext.currentTime + 0.15);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.15);
            } catch (e) {
                console.log('无法播放默认音效:', e);
            }
        }

        // 自动打开聊天窗口（收到新消息时）
        function autoOpenChatForNewMessage() {
            // 检查聊天窗口当前的显示状态
            const currentDisplay = chatWindow.style.display;
            const isWindowHidden = (currentDisplay === 'none' || !isOpen);

            console.log('🔍 检查窗口状态: display=' + currentDisplay + ', isOpen=' + isOpen + ', 需要打开=' + isWindowHidden);

            if (isWindowHidden) {
                console.log('🔄 聊天窗口已关闭，正在自动打开...');

                // 自动打开聊天窗口
                isOpen = true;
                chatWindow.style.display = 'block';

                // 更新按钮样式
                const buttonIcon = document.getElementById('cs-button-icon');
                if (buttonIcon) {
                    buttonIcon.textContent = '×';
                    chatButton.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
                }

                // 启动心跳和实时消息接收（如果还没启动）
                if (!heartbeatInterval) {
                    startHeartbeat();
                }

                // 确保会话已初始化
                initSession();

                // 启动实时消息接收（如果还没启动）
                if (!window.customerServicePolling) {
                    startRealtimeMessageReceiver();
                }

                // 添加特殊的新消息提示动画
                chatWindow.style.animation = 'newMessageBounce 0.6s ease-out';
                setTimeout(() => {
                    chatWindow.style.animation = '';
                }, 600);

                // 显示桌面通知
                showDesktopNotification('新消息', '客服给您发送了新消息');

                console.log('✅ 聊天窗口已自动打开 (display: none → block)');
            } else {
                console.log('ℹ️ 聊天窗口已经打开，无需处理');
            }
        }

        // 显示桌面通知
        function showDesktopNotification(title, message) {
            // 检查浏览器是否支持通知
            if (!('Notification' in window)) {
                console.log('浏览器不支持桌面通知');
                return;
            }

            // 检查通知权限
            if (Notification.permission === 'granted') {
                // 已有权限，直接显示通知
                createNotification(title, message);
            } else if (Notification.permission !== 'denied') {
                // 请求权限
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        createNotification(title, message);
                    }
                });
            }
        }

        function createNotification(title, message) {
            const notification = new Notification(title, {
                body: message,
                icon: '/images/customer-service-avatar.png',
                badge: '/images/customer-service-avatar.png',
                tag: 'customer-service-message',
                requireInteraction: false,
                silent: false
            });

            // 点击通知时聚焦到窗口
            notification.onclick = function() {
                window.focus();
                notification.close();
            };

            // 3秒后自动关闭
            setTimeout(() => {
                notification.close();
            }, 3000);
        }

        // 显示新消息指示器
        function showNewMessageIndicator() {
            const button = document.getElementById('cs-chat-button');
            if (button) {
                // 添加闪烁效果
                button.style.animation = 'pulse 1s infinite';

                // 5秒后移除闪烁
                setTimeout(() => {
                    button.style.animation = '';
                }, 5000);
            }
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes  fadeInUp {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            #cs-chat-button:hover {
                transform: scale(1.05);
                box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5) !important;
            }
            #cs-chat-window {
                animation: slideInUp 0.3s ease-out;
            }
            @keyframes  slideInUp {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            @keyframes  pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); box-shadow: 0 6px 30px rgba(102, 126, 234, 0.6); }
                100% { transform: scale(1); }
            }
            @keyframes  newMessageBounce {
                0% { transform: translateY(20px) scale(0.95); opacity: 0.8; }
                50% { transform: translateY(-10px) scale(1.02); opacity: 1; }
                100% { transform: translateY(0) scale(1); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        // 心跳和关闭检测功能
        function initSession() {
            console.log('🔄 初始化会话:', sessionId);

            if (!sessionId) {
                console.error('❌ sessionId为空，无法初始化会话');
                return;
            }

            fetch('/customer-service/init', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    session_id: sessionId,  // 传递前台生成的sessionId
                    visitor_name: '访客用户',
                    visitor_email: ''
                })
            })
            .then(response => {
                console.log('📡 initSession响应状态: ' + response.status);
                return response.json();
            })
            .then(data => {
                console.log('📨 initSession响应数据:', data);
                if (data.success) {
                    console.log('✅ 会话初始化成功，数据库记录已创建');
                    console.log('📋 会话信息: sessionId=' + data.session_id + ', 访客=' + data.visitor_name);
                } else {
                    console.error('❌ 会话初始化失败:', data);
                }
            })
            .catch(error => {
                console.error('💥 会话初始化异常:', error);
            });
        }

        function startHeartbeat() {
            // console.log('💓 启动心跳'); // 已禁用以减少控制台输出
            sendHeartbeat(); // 立即发送一次
            heartbeatInterval = setInterval(sendHeartbeat, 5000); // 每5秒发送心跳
        }

        function stopHeartbeat() {
            if (heartbeatInterval) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
                // console.log('💓 停止心跳'); // 已禁用以减少控制台输出
            }
        }

        function sendHeartbeat() {
            const now = new Date().toLocaleTimeString();
            // console.log('💓 [' + now + '] 发送心跳'); // 已禁用以减少控制台输出

            fetch('/customer-service/heartbeat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    session_id: sessionId,
                    status: 'online'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // console.log('✅ [' + now + '] 心跳成功，更新了 ' + (data.updated || 0) + ' 行'); // 已禁用以减少控制台输出
                    // 更新按钮状态显示在线
                    updateButtonStatus('online');
                } else {
                    console.error('❌ [' + now + '] 心跳失败:', data);
                }
            })
            .catch(error => {
                console.error('💥 [' + now + '] 心跳异常:', error);
            });
        }

        function sendOfflineStatus() {
            const now = new Date().toLocaleTimeString();
            console.log('📤 [' + now + '] 发送离线状态');

            const data = JSON.stringify({
                session_id: sessionId,
                status: 'offline'
            });

            if (navigator.sendBeacon) {
                const blob = new Blob([data], { type: 'application/json' });
                const success = navigator.sendBeacon('/customer-service/heartbeat', blob);
                console.log('📡 [' + now + '] sendBeacon结果: ' + success);
            }

            // 更新按钮状态显示离线
            updateButtonStatus('offline');
        }

        function updateButtonStatus(status) {
            const chatButton = document.getElementById('cs-chat-button');
            if (chatButton) {
                if (status === 'online') {
                    // 在线状态：绿色边框
                    chatButton.style.border = '3px solid #52c41a';
                    chatButton.style.boxShadow = '0 0 10px rgba(82, 196, 26, 0.3)';
                } else {
                    // 离线状态：恢复默认
                    chatButton.style.border = 'none';
                    chatButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                }
            }
        }

        // 页面关闭检测
        window.addEventListener('beforeunload', function() {
            if (isOpen) sendOfflineStatus();
        });

        window.addEventListener('pagehide', function() {
            if (isOpen) sendOfflineStatus();
        });

            } catch (error) {
                console.error('客服系统初始化失败:', error);
                // 即使初始化失败，也显示客服按钮（降级处理）
                const widget = document.getElementById('customer-service-widget');
                if (widget) widget.style.display = 'block';
            }
        }, 200); // 延迟200ms初始化，让页面先渲染
    });
    </script>
</html>




<?php /**PATH C:\Users\<USER>\Documents\GitHub\huaifeng\resources\views/themes/default/layouts/app.blade.php ENDPATH**/ ?>