<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试结账错误处理</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/layer/3.5.1/layer.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>测试结账错误处理</h1>
    
    <div class="test-section">
        <h3>测试1: 模拟缺少手机号的游客下单</h3>
        <p>这个测试会发送一个没有手机号的下单请求，应该显示弹窗提示而不是JSON文本。</p>
        <button onclick="testMissingPhone()">测试缺少手机号</button>
        <div id="result1" class="result" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>测试2: 模拟缺少邮箱的游客下单</h3>
        <p>这个测试会发送一个没有邮箱的下单请求。</p>
        <button onclick="testMissingEmail()">测试缺少邮箱</button>
        <div id="result2" class="result" style="display:none;"></div>
    </div>

    <div class="test-section">
        <h3>测试3: 直接测试错误响应处理</h3>
        <p>这个测试会模拟前端接收到错误响应的处理逻辑。</p>
        <button onclick="testErrorHandling()">测试错误处理</button>
        <div id="result3" class="result" style="display:none;"></div>
    </div>

    <script>
        // 设置CSRF token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') || 'test-token'
            }
        });

        function testMissingPhone() {
            $('#result1').show().html('正在测试...');
            
            // 模拟下单请求，缺少手机号
            $.ajax({
                url: '/shoppingcart/checkout/createOrder',
                type: 'POST',
                dataType: 'json',
                data: {
                    // 缺少 phone 字段
                    email: '<EMAIL>',
                    first_name: 'Test',
                    last_name: 'User',
                    country_code: 'US',
                    address_line_1: '123 Test St',
                    city: 'Test City',
                    state_code: 'CA',
                    postal_code: '12345',
                    payment_option_id: 1,
                    shipping_option_id: 1
                },
                success: function(res) {
                    $('#result1').html('响应: ' + JSON.stringify(res, null, 2));
                    
                    if (res && res.code !== 200) {
                        // 处理错误 - 这应该显示弹窗
                        if (res.message) {
                            layer.msg(res.message, {icon: 2});
                            $('#result1').append('<br><strong>✅ 成功显示弹窗提示!</strong>');
                        } else {
                            $('#result1').append('<br><strong>❌ 没有错误消息</strong>');
                        }
                    } else {
                        $('#result1').append('<br><strong>❌ 意外的成功响应</strong>');
                    }
                },
                error: function(xhr, status, error) {
                    $('#result1').html('AJAX错误: ' + error + '<br>状态: ' + xhr.status + '<br>响应: ' + xhr.responseText);
                    
                    // 尝试解析响应
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            layer.msg(response.message, {icon: 2});
                            $('#result1').append('<br><strong>✅ 从错误响应中显示弹窗提示!</strong>');
                        }
                    } catch (e) {
                        $('#result1').append('<br><strong>❌ 无法解析错误响应</strong>');
                    }
                }
            });
        }

        function testMissingEmail() {
            $('#result2').show().html('正在测试...');
            
            // 模拟下单请求，缺少邮箱
            $.ajax({
                url: '/shoppingcart/checkout/createOrder',
                type: 'POST',
                dataType: 'json',
                data: {
                    phone: '1234567890',
                    // 缺少 email 字段
                    first_name: 'Test',
                    last_name: 'User',
                    country_code: 'US',
                    address_line_1: '123 Test St',
                    city: 'Test City',
                    state_code: 'CA',
                    postal_code: '12345',
                    payment_option_id: 1,
                    shipping_option_id: 1
                },
                success: function(res) {
                    $('#result2').html('响应: ' + JSON.stringify(res, null, 2));
                    
                    if (res && res.code !== 200) {
                        if (res.message) {
                            layer.msg(res.message, {icon: 2});
                            $('#result2').append('<br><strong>✅ 成功显示弹窗提示!</strong>');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    $('#result2').html('AJAX错误: ' + error + '<br>响应: ' + xhr.responseText);
                }
            });
        }

        function testErrorHandling() {
            $('#result3').show().html('正在测试...');
            
            // 模拟接收到错误响应
            var mockResponse = {
                code: 4005,
                message: 'Phone number is required for guest orders'
            };
            
            $('#result3').html('模拟响应: ' + JSON.stringify(mockResponse, null, 2));
            
            // 测试错误处理逻辑
            if (mockResponse && mockResponse.code !== 200) {
                if (mockResponse.message) {
                    layer.msg(mockResponse.message, {icon: 2});
                    $('#result3').append('<br><strong>✅ 成功显示弹窗提示!</strong>');
                } else {
                    $('#result3').append('<br><strong>❌ 没有错误消息</strong>');
                }
            }
        }
    </script>
</body>
</html>
