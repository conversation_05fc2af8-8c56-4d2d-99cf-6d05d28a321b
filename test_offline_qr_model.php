<?php
// 测试线下扫码支付模型

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "<h2>🔧 测试线下扫码支付模型</h2>";
    
    // 测试获取数据
    echo "<h3>1. 测试获取现有数据</h3>";
    $payments = \App\Models\OfflineQrPayment::all();
    echo "<div>找到 " . $payments->count() . " 条记录</div>";
    
    foreach ($payments as $payment) {
        echo "<div>- ID: {$payment->id}, 名称: {$payment->name}, 状态: " . ($payment->status ? '启用' : '禁用') . "</div>";
    }
    
    // 测试创建数据
    echo "<h3>2. 测试创建新数据</h3>";
    $newPayment = new \App\Models\OfflineQrPayment();
    $newPayment->name = '测试支付方式';
    $newPayment->description = '这是一个测试';
    $newPayment->status = 1;
    $newPayment->icon = 'fas fa-qrcode';
    $newPayment->color = '#000000';
    $newPayment->sort_order = 999;
    
    if ($newPayment->save()) {
        echo "<div style='color: green;'>✅ 创建成功，ID: {$newPayment->id}</div>";
        
        // 删除测试数据
        $newPayment->delete();
        echo "<div style='color: blue;'>🗑️ 测试数据已删除</div>";
    } else {
        echo "<div style='color: red;'>❌ 创建失败</div>";
    }
    
    echo "<h2 style='color: green;'>🎉 模型测试完成！</h2>";
    echo "<p><a href='/strongadmin/domestic-payment/index' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>返回支付管理测试</a></p>";
    
} catch (\Exception $e) {
    echo "<div style='color: red;'>❌ 错误: " . $e->getMessage() . "</div>";
    echo "<div>错误详情: " . $e->getTraceAsString() . "</div>";
}
?>
