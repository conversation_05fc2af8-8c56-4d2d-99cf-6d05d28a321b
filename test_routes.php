<?php
// 测试路由

require_once 'vendor/autoload.php';

// 加载Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "<h2>🔧 测试线下扫码支付路由</h2>";
    
    // 获取所有路由
    $routes = \Route::getRoutes();
    
    echo "<h3>查找offline-qr相关路由：</h3>";
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        if (strpos($uri, 'offline-qr') !== false) {
            $methods = implode('|', $route->methods());
            $action = $route->getActionName();
            echo "<div>- {$methods} {$uri} → {$action}</div>";
        }
    }
    
    echo "<h3>测试具体路由：</h3>";
    
    // 测试更新路由
    $updateRoute = null;
    foreach ($routes as $route) {
        if (strpos($route->uri(), 'offline-qr-update') !== false) {
            $updateRoute = $route;
            break;
        }
    }
    
    if ($updateRoute) {
        echo "<div style='color: green;'>✅ 找到更新路由: " . implode('|', $updateRoute->methods()) . " " . $updateRoute->uri() . "</div>";
    } else {
        echo "<div style='color: red;'>❌ 未找到更新路由</div>";
    }
    
    echo "<h3>测试URL生成：</h3>";
    try {
        $url = url('/strongadmin/domestic-payment/offline-qr-update/1');
        echo "<div>生成的URL: {$url}</div>";
    } catch (\Exception $e) {
        echo "<div style='color: red;'>URL生成失败: " . $e->getMessage() . "</div>";
    }
    
} catch (\Exception $e) {
    echo "<div style='color: red;'>❌ 错误: " . $e->getMessage() . "</div>";
}
?>
